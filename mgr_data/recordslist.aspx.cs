using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class u_records_list : baseClass
{
    protected void Page_Load(object sender, EventArgs e)
    {

    }


    protected void Button1_Click(object sender, EventArgs e)
    {
        string start_date = sdate.Value.Trim();
        string end_date = edate.Value.Trim();
        string _appid = appid.Value.Trim();
        string uid = Request.QueryString["uid"] + "";

        string cond = string.Empty;
        string temp = string.Empty;

        start_date = SafeSql(start_date);
        end_date = SafeSql(end_date);
        uid = SafeSql(uid);

        cond = " ";

        if (IsDate(start_date))
        {
            cond += cond == string.Empty ? string.Empty : " and ";
            cond += " CONVERT(varchar(100),r.createTime,23)>='" + start_date + "' ";
        }
        if (IsDate(end_date))
        {
            cond += cond == string.Empty ? string.Empty : " and ";
            cond += " CONVERT(varchar(100),r.createTime,23)<='" + end_date + "' ";
        }


        SqlParameter[] parames = new SqlParameter[]{
            new SqlParameter("@uid",uConfig.p_uid)
        };
        string sql = " select u.nick,r.note,r.cost,r.createTime from records r with(nolock) left join users u on r.uid=u.id where name='退单' " + cond + " order by createTime desc ";
        dbClass db = new dbClass();
        DataTable dt = db.getDataTable(sql, parames);
        for (int i = 0; i < dt.Rows.Count; i++)
        {
            temp += dt.Rows[i]["nick"] + "\t" + dt.Rows[i]["note"] + "\t" + dt.Rows[i]["cost"] + "\t" + dt.Rows[i]["createTime"] + "\r\n";
            //temp += dt.Rows[i]["res"] + "\r\n";
        }
        Response.Clear();
        Response.Buffer = false;
        Response.ContentEncoding = System.Text.Encoding.UTF8;
        Response.AppendHeader("Content-Disposition", "attachment;filename=退款明细_" + start_date + "_" + end_date + ".txt");
        Response.ContentType = "text/plain";
        EnableViewState = false;
        Response.Write(temp);
        Response.End();


    }
}