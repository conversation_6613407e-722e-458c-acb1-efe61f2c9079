//MOMO version v8.2.0
var MOMO = {}

MOMO.selectData = {};
MOMO.select = {
    init: function (json) {
        //json{
        //    id:id,
        //    text:default text,
        //    data:[{text:text,value:value,select:true|false}...]
        //    onselect:function()
        //}

        //data:[{text:text,value:value}...]
        var id = json.id;
        var selectTemplate = "";
        var selectBody = "";
        var selectClass = "detault_select";
        var selectId = _uuid();
        var htmlId = (json.id.indexOf('#') == 0 ? "id='" + json.id.replace(/\#/g, "") + "'" : "class='" + json.id.replace(/\./g, "") + "'");


        var selectIndex = -1;
        var default_text = v_get(json.text, '請選項項目');
        selectBody += '<p class="select_default"><span>' + default_text + '</span></p>';
        for (var i = 0; i < json.data.length; i++) {
            json.data[i]['indexId'] = i;
            var _temp = json.data[i];
            var is_select = v_get(_temp.select, false);
            selectBody += '<p ' + (is_select ? 'class="is_select"' : '') + ' indexId="' + json.data[i]['indexId'] + '"><span>' + _temp.text + '</span></p>';
            if (is_select) {
                selectIndex = i;
            }
        }


        MOMO.selectData[selectId] = {
            id: id,
            mid: selectId,
            data: json.data,
            value: ''
        };



        selectTemplate = '<div class="moSelect limit_select ' + selectClass + '" tabindex="-1" mid="' + selectId + '"><input type="hidden" ' + htmlId + ' value="">';

        selectTemplate += '<span class="moSelectText">' + default_text + '</span><svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path d="M513.4 599.7c-8.8 0-17.5-3.3-24.2-10L371.5 472c-9.8-9.7-9.8-25.6 0-35.3 9.8-9.8 25.6-9.8 35.3 0l106.6 106.6L620 436.7c9.8-9.8 25.6-9.8 35.3 0 9.8 9.8 9.8 25.6 0 35.3L537.6 589.7c-6.7 6.7-15.4 10-24.2 10z" fill="#586168"></path></svg>';

        selectTemplate += '<div class="moSelectItems moScroll limit_select" mid="' + selectId + '">' + selectBody + '</div>';

        selectTemplate += '</div>';

        if ($(id).is('input')) {
            $(id).closest('.moSelect').replaceWith(selectTemplate);
        } else {
            $(id).replaceWith(selectTemplate);
        }



        $('.moSelect[mid="' + selectId + '"]').click(function () {
            if ($(this).hasClass("is_select")) {
                $(this).trigger('blur');
            } else {
                $(this).addClass('is_select');
                $(this).find(".moSelectItems").slideDown(200);
            }
        })
        $('.moSelect[mid="' + selectId + '"]').on('blur', function () {
            $(this).removeClass('is_select');
            $(this).find(".moSelectItems").slideUp(100);
        })

        $('.moSelect[mid="' + selectId + '"] .moSelectItems>p').on('click', function (attach) {

            var indexId = $(this).attr('indexId');
            var data = $(this).find('span').html();
            var id = $(this).closest(".moSelectItems").attr('mid');

            var textObj = $('.moSelect[mid=' + id + ']').find('.moSelectText');
            textObj.html(data);

            if ($(this).hasClass("select_default")) {
                textObj.closest('.moSelect').addClass("detault_select");
                $(this).removeClass("is_select").siblings().removeClass("is_select");
            } else {
                textObj.closest('.moSelect').removeClass("detault_select");
                $(this).addClass("is_select").siblings().removeClass("is_select");
            }
            $(this).closest(".moSelect").trigger('blur');

            var _tempData = MOMO.selectData[id].data;
            var selectValue = null;
            for (var i = 0; i < _tempData.length; i++) {
                if (_tempData[i].indexId == indexId) {
                    selectValue = _tempData[i].value;
                    break;
                }
            }

            if (v_exists(json.onselect)) {
                json.onselect({
                    id: MOMO.selectData[selectId].id,
                    selectId: id,
                    text: data,
                    value: selectValue
                });
            }
            MOMO.selectData[selectId].value = selectValue;
            $(MOMO.selectData[selectId].id).val(selectValue);

            return false;
        })

        //console.log('selectIndex', selectIndex);
        $('.moSelect[mid="' + selectId + '"] .moSelectItems>p').eq(selectIndex + 1).click();


        //$(MOMO.selectData[selectId].id).val(MOMO.selectData[selectId].value);


        return {
            id: selectId,
            set: function (_val) {
                var _tempData = MOMO.selectData[this.id].data;
                var selectText = "";
                var selectId = -1;
                for (var i = 0; i < _tempData.length; i++) {
                    if (_tempData[i].value == _val) {
                        selectText = _tempData[i].text;
                        selectId = i;
                        break;
                    }
                }
                $('.moSelect[mid="' + this.id + '"] .moSelectItems>p').eq(selectId + 1).click();
                return { success: selectId != -1, text: selectText };
            },
            get: function () {
                return MOMO.selectData[this.id].value;
            },
            click: function () {
                $('.moSelect[mid="' + this.id + '"] .moSelectItems>p.is_select').click();
            }
        }

    }
}


MOMO.selectData = {};
MOMO.pager = {
    config: {
        id: '',
        limit: 10,
        total_num: 0,
        current_page: 1,
        click: function () { }
    },


    init: function (c) {
        //console.log('Init', c);
        this.config = c;
        this.init_pager();

        var fn = this.init_pager;

        $(window).on('resize', { elem: this }, function (e) {
            var elem = e.data.elem;
            elem.init_pager();
        })
    },


    init_pager: function () {
        var c = this.config;
        c.current_page_number = parseInt(c.total_num / c.limit);
        c.current_page_number += (c.current_page_number * c.limit == c.total_num ? 0 : 1);
        //console.log(this.config.id,'config.current_page_number', c.current_page_number);
        //console.log(this.config.id, 'c.current_page ', c.current_page);
        if (c.current_page_number == 0) {
            c.current_page_number = 1;
        }

        if (c.current_page < 0) {
            c.current_page = 0;
        }
        if (c.current_page > c.current_page_number - 1) {
            c.current_page = c.current_page_number - 1;
        }

        var momo_pager_list = "";
        momo_pager_list += '<div class="momo_pager_last">                            <svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-chevron-left">                                <path fill="currentColor" d="M9.54 3.54l.92.92L6.92 8l3.54 3.54-.92.92L5.08 8l4.46-4.46z" fill-opacity="0.9"></path></svg>                        </div>';

        var html = {
            left: "",
            center: "",
            right: ""
        }

        html.left = '<div class="momo_pager_last ' + (c.current_page == 0 || c.current_page_number == c.current_page_number - 1 ? 'pager_limit' : '') + '">                            <svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-chevron-left">                                <path fill="currentColor" d="M9.54 3.54l.92.92L6.92 8l3.54 3.54-.92.92L5.08 8l4.46-4.46z" fill-opacity="0.9"></path></svg>                        </div>';
        html.right = '<div class="momo_pager_next ' + (c.current_page == c.current_page_number - 1 ? 'pager_limit' : '') + '">                            <svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-chevron-right">                                <path fill="currentColor" d="M6.46 12.46l-.92-.92L9.08 8 5.54 4.46l.92-.92L10.92 8l-4.46 4.46z" fill-opacity="0.9"></path></svg>                        </div>';
        html.center = '<div class="momo_pager_number active">                            <a>' + (c.current_page + 1) + '</a>                        </div>';


        var __width = window.innerWidth;
        var show_details = __width > 600;
        var show_list = __width > 380 + 40;
        if (show_list) {
            var current_index = 0;
            current_index = c.current_page;
            for (var i = 0; i < 2; i++) {
                current_index--;
                //console.log('last['+i+']', current_index);
                if (current_index <= -1) {
                    break;
                }
                html.center = '<div class="momo_pager_number">                            <a>' + (current_index + 1) + '</a>                        </div>' + html.center;
            }
            if (current_index > 1) {
                html.left += '<div class="momo_pager_number">                            <a>' + "1" + '</a>                        </div>';
                html.left += '<div class="momo_pager_more">                            <svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-ellipsis">                                <path fill="currentColor" d="M3 9a1 1 0 110-2 1 1 0 010 2zM7 8a1 1 0 102 0 1 1 0 00-2 0zM12 8a1 1 0 102 0 1 1 0 00-2 0z" fill-opacity="0.9"></path></svg>   </div>';
            }
            current_index = c.current_page;
            for (var i = 0; i < 2; i++) {
                current_index++;
                if (current_index == c.current_page_number) {
                    break;
                }
                html.center += '<div class="momo_pager_number">                            <a>' + (current_index + 1) + '</a>                        </div>';
            }
            if (current_index < c.current_page_number - 2) {
                html.center += '<div class="momo_pager_more">                            <svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-ellipsis">                                <path fill="currentColor" d="M3 9a1 1 0 110-2 1 1 0 010 2zM7 8a1 1 0 102 0 1 1 0 00-2 0zM12 8a1 1 0 102 0 1 1 0 00-2 0z" fill-opacity="0.9"></path></svg>   </div>';
                html.center += '<div class="momo_pager_number">                            <a>' + c.current_page_number + '</a>                        </div>';
            }
        }


        $(this.config.id).html('<div class="momo_pager limit_select">                    ' + (show_details ? '<div class="pgaer_detail">共 ' + c.total_num + ' 條數據</div>' : '') + '                    <div class="momo_pager_list">           ' + html.left + html.center + html.right + '         </div>                </div>');

        this.momo_pager_bind();
    },


    momo_pager_bind: function () {
        $(this.config.id + ' .momo_pager .momo_pager_list > div').on('click', { elem: this }, function (e) {
            var elem = e.data.elem;

            if ($(this).hasClass("momo_pager_number")) {
                ////Number
                elem.config.current_page = parseInt($(this).text()) - 1;
                elem.init_pager();
            } else if ($(this).hasClass("momo_pager_more")) {
                //More
                return;
            } else if ($(this).hasClass("momo_pager_last") || $(this).hasClass("momo_pager_next")) {
                if ($(this).hasClass("momo_pager_last")) {
                    elem.config.current_page -= 1;
                    elem.init_pager();
                } else {
                    elem.config.current_page += 1;
                    elem.init_pager();
                }

                if ($(this).hasClass("pager_limit")) {
                    return;
                }
            }

            elem.config.click({ current_page: elem.config.current_page });
        });
    }

}

MOMO.modal = {};
MOMO.modal = function (options) {
    var modal_object;
    var alert = function () {

        var _svg = '<svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-info-circle-filled">                            <path fill="currentColor" d="M8 15A7 7 0 108 1a7 7 0 000 14zM7.4 4h1.2v1.2H7.4V4zm.1 2.5h1V12h-1V6.5z" fill-opacity="0.9"></path></svg>';

        if (options.type == 'success') {
            _svg = '<svg fill="none" viewBox="0 0 16 16" width="1em" height="1em" class="t-icon t-icon-check-circle-filled t-is-success"><path fill="currentColor" d="M8 15A7 7 0 108 1a7 7 0 000 14zM4.5 8.2l.7-.7L7 9.3l3.8-3.8.7.7L7 10.7 4.5 8.2z" fill-opacity="0.9"></path></svg>';
        }

        modal_object = $('<div class="momo-modal momo-modal-' + options.type + '">        <div class="momo-modal-mask">        </div> <div class="momo-modal-dialog">                  <div class="momo-modal-simple" style="opacity:0;top:-100px;">                <div class="momo-modal-title">' + _svg + options.title + '</div>                <div class="momo-modal-content">' + options.content + '</div>                <div class="momo-modal-buttom limit_select">                    ' + (options.actions.cancel ? '<a class="moButton momo-button-gray momo-modal-cancel">取消</a>' : '') + (options.actions.ok ? '<a class="moButton momo-modal-confirm">确认</a>' : '') + '     </div>            </div>        </div>  </div>');
        $('body').append(modal_object);



        modal_object.find('.momo-modal-cancel').on('click', function () {
            options.actions.cancel();
            modal_object.closest('.momo-modal').remove();
        })
        modal_object.find('.momo-modal-confirm').on('click', function () {
            options.actions.ok();
            modal_object.closest('.momo-modal').remove();
        })

        modal_object.find('.momo-modal-simple').animate({ top: 0, opacity: 1 }, 500);
    }

    alert();
}


var v_exists = function (v) {
    if (typeof (v) == "undefined") {
        return false;
    } else {
        return true;
    }
}
var v_get = function (v, default_value) {
    if (v_exists(v)) {
        return v;
    } else {
        return default_value;
    }
}

function _uuid() {
    var d = new Date().getTime();
    if (window.performance && typeof window.performance.now === "function") {
        d += performance.now(); //use high-precision timer if available
    }
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
}

function reg_isEmail(strValue) {
    var objRegExp = /([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,5})+/;
    return objRegExp.test(strValue);
}
function reg_isPassword(strValue) {
    //var objRegExp = /^(?![A-Za-z0-9]+$)(?![a-z0-9_!@#$%^&*()+.]+$)(?![A-Za-z_!@#$%^&*()+.]+$)(?![A-Z0-9_!@#$%^&*()+.]+$)[a-zA-Z0-9_!@#$%^&*()+.]{8,20}$/;
    var objRegExp = /^(?=.*\d)(?=.*[a-zA-Z])[\S]{8,20}$/;
    return objRegExp.test(strValue);
}

function getUrlParam(name) {
    //构造一个含有目标参数的正则表达式对象
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    //匹配目标参数
    var r = window.location.search.substr(1).match(reg);
    //返回参数值
    if (r != null) {
        return decodeURI(r[2]);
    }
    return null;
}

//選項卡
var toggle_speed = function (obj, left, w) {
    $(obj).closest('.momo_toggle').find(".toggle_block").stop().animate({ "left": left, 'width': w }, 150);
}

var toggle_init = function () {
    $('.momo_toggle').each(function () {
        var obj = $(this).find('div.active');
        if (!obj.html()) {
            obj = $(this).find('div').eq(1);
        }
        var x = obj.position();
        toggle_speed(this, x.left, obj.width());
    });

    $('.momo_toggle>div').on('click', function () {
        var x = $(this).position();
        toggle_speed(this, x.left, $(this).width());
    })
}


//表单检测
$(function () {

    function check_reg(event) {
        $(this).removeClass("success_input");
        $(this).removeClass("error_input");
        var _obj = $(this).next();
        if (_obj.hasClass('p-text')) {
            _obj.remove();
        }
        if ($(this).val() == "") {
            return;
        }
        if (!event.data.e($(this).val())) {
            var pushText = $(this).attr('p-text');
            $(this).after("<p class='p-text' style='color:red;margin:0px;font-size:12px;'>" + pushText + "</p>")

            if (typeof (initLang) == 'function') {
                initLang();
            }

            $(this).addClass("error_input");
        } else {
            $(this).addClass("success_input");
        }
    }

    $('[check-email]').bind('change', { e: reg_isEmail }, check_reg);
    $('[check-password]').bind('change', { e: reg_isPassword }, check_reg);



    //搜索框圖標
    $('.ant-input').after('<span class="ant-input-suffix" onclick="g_List()"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><g data-v-dc107548="" clip-path="url(#clip0_1677_3851)"><path data-v-dc107548="" fill-rule="evenodd" clip-rule="evenodd" d="M9.23306 3.45835C7.63841 1.86371 5.05299 1.86371 3.45835 3.45835C1.86371 5.05299 1.86371 7.63841 3.45835 9.23306C5.05299 10.8277 7.63841 10.8277 9.23306 9.23306C10.8277 7.63841 10.8277 5.05299 9.23306 3.45835ZM2.63339 2.63339C4.68364 0.58314 8.00776 0.58314 10.058 2.63339C11.9688 4.54422 12.0988 7.56156 10.4478 9.62288L12.5329 11.7079L11.7079 12.5329L9.62288 10.4478C7.56156 12.0988 4.54422 11.9688 2.63339 10.058C0.58314 8.00776 0.58314 4.68364 2.63339 2.63339Z" fill="#4E5969"></path>                        </g></svg></span>');

    //警告框
    $('.momo_alert_close').on('click', function () {
        $(this).closest(".momo_alert").animate({ opacity: 0 }, 300, function () {
            $(this).remove();
        })
    })

    //選項卡
    toggle_init();

    setTimeout(toggle_init, 100);

})


//Other Function
function mModal(text, title, actions) {
    if (!actions) {
        actions = {
            ok: function () {
            }
        }
    }
    //alert(text);
    MOMO.modal({
        type: '',
        title: (title ? title : 'Tips'),
        content: text.replace(/\n/, "<br>"),
        actions: actions
    });
    //MOMO.modal({
    //    type: 'error',
    //    title: '測試',
    //    content: '阿根廷VS法國',
    //    actions: {
    //        ok: function () {
    //            console.log('點擊完成了');
    //        },
    //        cancel: function () {
    //            console.log('點擊取消了');
    //        }
    //    }
    //})




    initLang();
}
function mNotify(text, params) {
    //alert(text);

    MOMO.modal({
        type: '',
        title: 'Tips',
        content: text.replace(/\n/,"<br>"),
        actions: {
            ok: function () {
            },
            //cancel: function () {
            //    console.log('點擊取消了');
            //}
        }
    });



    initLang();
}
function getParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
}


function text_download(filename, text) {
    var pom = document.createElement('a');
    pom.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
    pom.setAttribute('download', filename);
    if (document.createEvent) {
        var event = document.createEvent('MouseEvents');
        event.initEvent('click', true, true);
        pom.dispatchEvent(event);
    } else {
        pom.click();
    }
}

var initLang = function () { }