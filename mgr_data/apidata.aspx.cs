using com.cotees;
using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Api_admin : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        if (!uConfig._adminIsLogin)
        {
            fz.sendResponse("您的权限不足");
        }
        Response.Write(main(fz.req("do")));
        Response.End();
    }

    public string main(string action)
    {
        string src = string.Empty;
        string temp = string.Empty;
        string temp2 = string.Empty;
        string sql = string.Empty;
        string code = string.Empty;
        string msg = string.Empty;
        string options = string.Empty;
        string id = string.Empty;
        string price = string.Empty;
        bool checkResult = false;
        string[] g;
        string[] g2;

        int res = 0;

        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable dt2 = new DataTable();
        DataTable temp_dt = new DataTable();
        DataSet ds = new DataSet();
        DataRow[] dr;
        SqlParameter[] parames = null;
        List<SqlParameter> pas = new List<SqlParameter>();
        List<string> dicArr = new List<string>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        List<Dictionary<string, object>> list2 = new List<Dictionary<string, object>>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> dic2 = new Dictionary<string, object>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out checkResult);
        fz.setResponseData(true, false);
        List<string> sList = new List<string>();

        // - table参数
        string cond = string.Empty;
        string _page = Request.Form["page"] + "";
        string _p = Request.Form["p"] + "";
        string _s = Request.Form["s"] + "";
        string _key = Request.Form["key"] + "";
        string _opt = Request.Form["opt"] + "";
        string _opt2 = Request.Form["opt2"] + "";
        _opt = SafeSql(_opt);
        _opt2 = SafeSql(_opt2);
        List<string> cols;

        string site_st = Request.Form["st"] + "";
        string site_user = Request.Form["site_user"] + "";
        string site_name = Request.Form["site_name"] + "";
        string site_desc = Request.Form["site_desc"] + "";
        string site_img = Request.Form["site_img"] + "";
        string site_list = Request.Form["site_list"] + "";
        string site_notice = Request.Form["site_notice"] + "";
        string site_status = Request.Form["site_status"] + "";
        string st_status = Request.Form["status"] + "";

        string card_count = Request.Form["count"] + "";
        string card_num = Request.Form["num"] + "";

        string user_token = Request.Form["token"] + "";
        string user_money = Request.Form["money"] + "";


        string user_id = Request.Form["id"] + "";
        string user_rank = Request.Form["rank"] + "";
        string user_nick = Request.Form["nick"] + "";
        string user_pwd = Request.Form["pwd"] + "";
        string user_qq = Request.Form["qq"] + "";
        string user_status = Request.Form["status"] + "";

        string proxy_name = Request.Form["proxyName"] + "";

        string bus_change = Request.Form["change"] + "";
        string bus_rankid = Request.Form["rankid"] + "";

        string order_id = Request.Form["id"] + "";
        string order_url = Request.Form["url"] + "";
        string order_num = Request.Form["num"] + "";
        string order_start = Request.Form["start"] + "";
        string order_now = Request.Form["now"] + "";
        string order_status = Request.Form["status"] + "";

        string _appcode = Request.Form["appcode"] + "";
        string _appname = Request.Form["appname"] + "";
        string _appnotice = Request.Form["appnotice"] + "";
        string _appmincount = Request.Form["appmincount"] + "";
        string _appmincost = Request.Form["appmincost"] + "";
        string _appmaxcount = Request.Form["appmaxcount"] + "";
        string _appsort = Request.Form["appsort"] + "";
        string _appid = Request.Form["appid"] + "";
        string _istest = Request.Form["istest"] + "";
        Dictionary<string, object> pmlist = new Dictionary<string, object>();


        string _name = Request.Form["name"] + "";
        string c_name = Request.Form["c_name"] + "";
        string c_content = Request.Form["c_content"] + "";


        string shop_key = Request.Form["shop_key"] + "";
        string from_user = Request.Form["from_user"] + "";

        string _nick = Request.Form["nick"] + "";
        string _pwd = Request.Form["pwd"] + "";

        string _id = Request.Form["id"] + "";

        string cacheid = string.Empty;

        fz.setResponseLable("msg");

        if ((action != "get_summary" && action != "bus" && !(action == "page" && (fz.req("page") == "orders" || fz.req("page") == "records-list"))) && uConfig.p_userNickAD != "gm-facai")
        {
            fz.sendResponse("page is not found");
        }

        switch (action)
        {
            case "updateConfig":
                jd = JsonMapper.ToObject(fz.req("data"));
                for (int i = 0; i < jd.Count; i++)
                {
                    pams.Add(new SqlParameter("@cfg_name" + i, jd[i]["name"] + ""));
                    pams.Add(new SqlParameter("@cfg_data" + i, jd[i]["data"] + ""));
                    sql += string.Format(" if(Exists(select * from stConfig where name=@cfg_name{0}))begin update stConfig set data=@cfg_data{0} where name=@cfg_name{0} end else begin insert into stConfig values(@cfg_name{0},@cfg_data{0}) end ", i);
                }
                db.ExecuteNonQuery(sql, pams.ToArray());
                src = "{\"code\":1,\"msg\":\"更新成功\"}";
                cae.RemoteCache("stc_");
                break;
            case "edit_create":
                if (fz.empty("id")) temp = "添加";
                else
                    temp = "修改";


                cacheid = fz.req("_app");

                switch (fz.req("_app"))
                {
                    case "opens":
                        sList = fz.getCheckValues("sq");
                        sql = " delete opens where uid=@id ";
                        for (int i = 0; i < sList.Count; i++)
                        {
                            if (IsNumeric(sList[i]))
                            {
                                sql += string.Format(" insert into opens values(@id,{0})", sList[i]);
                            }
                        }
                        break;
                    case "active_list":
                        if (fz.empty("id"))
                        {

                            sql = " insert into active_list values(@name,@startTime,@endTime,GETDATE()) ";
                        }
                        else
                        {
                            sql = " update active_list set name=@name,startTime=@startTime,endTime=@endTime where id=@id ";
                        }
                        break;
                    case "renqi_refund":
                        temp = string.Empty;
                        if (!fz.empty("othernum"))
                        {
                            temp += ",othernum=@othernum ";
                        }
                        sql = " update orders set status='退单中',nowCount=@nownum,refundTime=(case when refundTime is null then GETDATE() else refundTime end)" + temp + " output deleted.* where id=@id and (status='未开始' or status='进行中' or status='订单异常') ";
                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count > 0)
                        {
                            temp = dt.Rows[0]["status"] + "";
                            log.WriteLog("退单", "后台|" + uConfig.p_userNickAD, "id:" + _id + ",status:" + temp);
                            dic.Add("code", 1);



                            if (temp == "未开始")
                            {
                                parames = new SqlParameter[]{
                                    new SqlParameter("@orderid",fz.req("id"))
                                };
                                sql = " [tuidan] @orderid,'售后' ";
                                db.ExecuteNonQuery(sql, parames);

                                dic.Add("msg", "退单成功。" + temp2);
                            }
                            else
                            {
                                dic.Add("msg", "申请成功，系统将在1-2分钟内为您处理退单。" + temp2);
                            }
                        }
                        else
                        {
                            dic.Add("code", -1);
                            dic.Add("msg", "此订单无法发起退单请求");
                        }
                        src = JsonMapper.ToJson(dic);
                        return src;
                        break;
                    case "order_edit":
                        temp = string.Empty;
                        if (!fz.empty("othernum"))
                        {
                            temp += ",othernum=@othernum ";
                        }

                        temp2 = fz.req("plid");
                        if (fz.req("type") == "comment_list")
                        {
                            g = fz.req("plid").Split(new string[] { "\r\n" }, StringSplitOptions.None);
                            for (int i = 0; i < g.Length; i++)
                            {
                                dicArr.Add(g[i]);
                            }
                            temp2 = JsonMapper.ToJson(dicArr);
                        }
                        pams.Add(new SqlParameter("@new_plid", temp2));

                        sql = " update orders set url=@url,startcount=@start,nowcount=@now,status=@status,num=@num,data=@new_plid,api_params=@api_params,remark1=@remark1,app_id=@app_id,state_text=@state_text" + temp + " where id=@id";
                        if (order_status == "已完成")
                        {
                            sql += " update [liveOrders] set status=1 where orderid=@id ";
                        }
                        else if (order_status == "进行中")
                        {
                            sql += " update [liveOrders] set status=0 where orderid=@id ";
                        }
                        res = db.ExecuteNonQuery(sql, pams.ToArray());
                        if (res > 0)
                        {
                            fz.sendResponse("修改成功", 1);
                        }
                        else
                        {
                            fz.sendResponse("订单不存在");
                        }
                        break;
                    case "users":
                        if (fz.empty("id"))
                        {
                            sql = " if(not Exists(select top 1 * from users where nick=@nick))begin insert into users values(REPLACE(NEWID(),'-',''),null,@nick,@pwd,0,0,0,0,0,10,1,GETDATE(),GETDATE(),'','admin') end ";
                        }
                        else
                        {
                            sql = " update users set nick=@nick,pwd=@pwd,status=@status where id=@id  ";
                        }
                        break;
                    case "users_money":
                        if (!uConfig.isSuperAdmin)
                        {
                            fz.sendResponse("无法加款");
                        }
                        if (!IsNumeric(user_money))
                        {
                            fz.sendResponse("请输入正确的加款金额");
                        }
                        if (Convert.ToDouble(fz.req("money")) >= 0)
                        {
                            temp = "充值";
                        }
                        else
                        {
                            temp = "客服售后处理";
                        }
                        pams.Add(new SqlParameter("@title", temp));

                        sql = " declare @score decimal(18,2) update users set @score=score,score=score+@money where id=@id if(@score is not null)begin insert into records values(@id,@title,@money,@score+@money,'','',GETDATE())  end";
                        break;
                    case "app_group":
                        if (fz.empty("id"))
                        {
                            sql = @" 
if(not Exists(select top 1 * from app_group where name=@name))
begin 
    insert into app_group values(@name,@maxnum,@state,getdate()) 
end ";
                        }
                        else
                        {
                            sql = " update app_group set name=@name,maxnum=@maxnum,state=@state where id=@id  ";
                        }
                        break;
                    case "apps":

                        if (fz.empty("id"))
                        {

                            dt = selectDateTable(chelper.gdt("apps"), " appcode='" + fz.req("code") + "' ");
                            if (dt.Rows.Count > 0)
                            {
                                fz.sendResponse("业务代码已存在");
                            }

                            sql = " insert into [apps] values(@media_type,10002,@code,@name,0.01,-1,10,-1,@name+' 即将上线',1,1,1,1,'',null,0,null,0,@type,0,-1,-1,@groupid,@times,@close_not_refund,@close_limit_times) ";
                            sql += " insert into apis values(@code,10001) ";
                            sql += " insert into apis values(@code,10002) ";
                        }
                        else
                        {

                            dt = selectDateTable(chelper.gdt("apps"), " appcode='" + fz.req("code") + "' ");
                            if (dt.Rows.Count > 0 && dt.Rows[0]["id"] + "" != fz.req("id"))
                            {
                                fz.sendResponse("业务代码已存在");
                            }
                            sql = "declare @origin_code varchar(50)";
                            sql += " update apps set @origin_code=appcode,typename=@media_type,type2=@type2,appcode=@code,appname=@name,notice=@notice,mincount=@mincount,maxcount=@maxcount,sort=@sort,refund_order=@refund_order,appid=@appid,status=(case when @appid=-1 then -1 else 1 end),groupid=@groupid,times=@times,close_not_refund=@close_not_refund,close_limit_times=@close_limit_times where id=@id ";
                            sql += " update apis set appcode=@code where appcode=@origin_code ";
                        }
                        res = db.ExecuteNonQuery(sql, pams.ToArray());
                        if (res > 0)
                        {
                            chelper.cdt(fz.req("_app"));

                            cae.RemoteCache("appList");
                            cae.RemoteCache("appListAll");

                        }
                        fz.sendRsp(res, "操作成功", "操作失败");
                        break;

                    case "itemClass_list":
                        if (fz.empty("id"))
                        {
                            res = db.ExecuteNonQuery(String.Format(" insert into {0} values(999,@name,@state,getdate()) ", fz.req("_app")), pams.ToArray());
                        }
                        else
                        {
                            res = db.ExecuteNonQuery(String.Format(" update {0} set sortId=@sortId,name=@name,state=@state where id=@id ", fz.req("_app")), pams.ToArray());
                        }
                        if (res > 0)
                        {
                            chelper.cdt(fz.req("_app"));
                        }
                        fz.sendRsp(res, "操作成功", "操作失败");
                        break;
                    case "item_list":
                        if (fz.empty("id"))
                        {
                            res = db.ExecuteNonQuery(String.Format(" insert into {0} values(null,@classId,@title,@icon,@describe_text,@usd,@state,@limit_min_count,@limit_max_count,@base_style,@theme_style,getdate()) declare @temp_id int set @temp_id=@@identity update {0} set sortId=@temp_id where id=@temp_id ", fz.req("_app")), pams.ToArray());
                        }
                        else
                        {
                            res = db.ExecuteNonQuery(String.Format(" update {0} set classId=@classId,title=@title,icon=@icon,describe_text=@describe_text,usd=@usd,state=@state,limit_min_count=@limit_min_count,limit_max_count=@limit_max_count,base_style=@base_style,theme_style=@theme_style where id=@id ", fz.req("_app")), pams.ToArray());
                        }
                        if (res > 0)
                        {
                            chelper.cdt(fz.req("_app"));
                        }
                        fz.sendRsp(res, "操作成功", "操作失败");
                        break;
                    case "storage_list":
                        g = fz.req("storage_data").Split(new string[] { "\r\n" }, StringSplitOptions.None);

                        dt = new DataTable("temp_dt");
                        dt.Columns.Add(new DataColumn("id", Type.GetType("System.Int16")));
                        dt.Columns.Add(new DataColumn("itemId", Type.GetType("System.Int16")));
                        dt.Columns.Add(new DataColumn("storage_data", Type.GetType("System.String")));
                        dt.Columns.Add(new DataColumn("create_time", Type.GetType("System.DateTime")));

                        temp = fz.req("id");

                        for (int i = 0; i < g.Length; i++)
                        {
                            DataRow temp_dr = dt.NewRow();
                            temp_dr["id"] = 1;
                            temp_dr["itemId"] = temp;
                            temp_dr["storage_data"] = g[i];
                            temp_dr["create_time"] = DateTime.Now;
                            dt.Rows.Add(temp_dr);
                        }

                        //fz.sendResponse(ToJson(dt));

                        res = db.bulkInsert(dt, "storage_list");

                        fz.sendRsp(res, "导入成功 + " + g.Length, "导入失败");
                        break;
                    default:
                        fz.sendResponse("error edit");
                        break;
                }
                if (!string.IsNullOrEmpty(sql))
                {
                    try
                    {
                        res = db.ExecuteNonQuery(sql, pams.ToArray());
                    }
                    catch (Exception ex)
                    {
                        fz.sendResponse("操作异常:" + ex.Message.ToString());
                    }
                    if (res > 0)
                    {
                        if (!string.IsNullOrEmpty(cacheid))
                        {
                            cae.RemoteCache(cacheid);
                        }
                        fz.sendResponse(temp + "成功", 1);
                    }
                }
                fz.sendResponse(temp + "失败");
                break;
            case "event_create":
                if (!uConfig.isSuperAdmin) fz.sendResponse("无法操作");
                switch (fz.req("name"))
                {
                    case "discount":
                        if (fz.req("event") == "delete")
                        {
                            sql = " delete discount where id=@id ";
                            res = db.ExecuteNonQuery(sql, pams.ToArray());
                            if (res > 0) fz.sendResponse("删除成功", 1);
                            fz.sendResponse("信息不存在或无法删除");
                        }
                        break;
                    case "active_list":
                        if (fz.req("event") == "delete")
                        {
                            sql = " delete active_list where id=@id delete [active_rankfee] where active_id=@id delete [active_appfee] where active_id=@id ";
                            res = db.ExecuteNonQuery(sql, pams.ToArray());
                            if (res > 0) fz.sendResponse("活动删除成功", 1);
                            fz.sendResponse("活动不存在");
                        }
                        break;
                    case "users":
                        if (fz.req("event") == "delete")
                        {

                            fz.sendResponse("禁止删除操作");

                            sql = " delete users where id=@id ";
                            res = db.ExecuteNonQuery(sql, pams.ToArray());
                            if (res > 0) fz.sendResponse("删除成功", 1);
                            fz.sendResponse("用户不存在");
                        }
                        break;
                    case "apps":
                        if (fz.req("event") == "delete")
                        {
                            sql = " declare @tb table(appcode varchar(50)) delete apps output deleted.appcode into @tb where id=@id ";
                            sql += " delete apis where appcode in (select appcode from @tb) ";
                            res = db.ExecuteNonQuery(sql, pams.ToArray());
                            if (res > 0)
                            {
                                cae.RemoteCache("appList");
                                cae.RemoteCache("appListAll");
                                fz.sendResponse("删除成功", 1);
                            };
                            fz.sendResponse("业务不存在");
                        }
                        break;
                    case "app_group":
                        if (fz.req("event") == "delete")
                        {
                            sql = " delete app_group where id=@id ";
                            res = db.ExecuteNonQuery(sql, pams.ToArray());
                            if (res > 0) fz.sendResponse("删除成功", 1);
                            fz.sendResponse("删除失败");
                        }
                        break;
                    case "itemClass_list":
                    case "item_list":
                        switch (fz.req("event"))
                        {
                            case "delete":
                                temp = formatOrderids(fz.req("id"));
                                temp = string.IsNullOrEmpty(temp) ? "  where id=@id " : "  where id in (" + temp + ") ";
                                dt = db.getDataTable(String.Format(" delete {0} output deleted.* " + (fz.req("state") == "1000" ? "" : temp), fz.req("name")), pams.ToArray());
                                if (dt.Rows.Count > 0)
                                {
                                    chelper.cdt(fz.req("name"));
                                    //fz.set_log_where_dt(dt);
                                    //fz.log("商品删除 @{name}");
                                }
                                fz.sendRsp(dt.Rows.Count, "删除成功", "操作失败");
                                break;
                            default:
                                break;
                        }
                        break;
                    case "storage_list":
                        switch (fz.req("event"))
                        {
                            case "delete":
                                temp = formatOrderids(fz.req("id"));
                                temp = string.IsNullOrEmpty(temp) ? "  and id=@id " : "  and id in (" + temp + ") ";
                                dt = db.getDataTable(String.Format(" delete {0} output deleted.*  where itemId=@item_id " + (fz.req("state") == "1000" ? "" : temp), fz.req("name")), pams.ToArray());
                                if (dt.Rows.Count > 0)
                                {
                                    chelper.cdt(fz.req("name"));
                                    //fz.set_log_where_dt(dt);
                                    //fz.log("商品删除 @{name}");
                                }
                                fz.sendRsp(dt.Rows.Count, "删除成功", "操作失败");
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        fz.sendResponse("error-name");
                        break;
                }
                fz.sendResponse("error-event");
                break;
            case "s_speedConfig":
                fz.check_exist(new string[] { "speedmax-pint,最高时速", "speeds,自定义速度" });
                g = fz.req("speeds").Split('\n');
                sql = " delete sps ";
                for (int i = 0; i < g.Length; i++)
                {
                    g2 = g[i].Split('-');
                    if (IsNumeric(g2[0]) && IsNumeric(g2[1]))
                    {
                        sql += string.Format(" insert into sps values({0},{1})", g2[0], g2[1]);
                    }
                }
                sql += string.Format(" insert into sps values({0},{1})", "-1", fz.req("speedmax"));
                db.ExecuteNonQuery(sql);
                fz.sendResponse("更新成功", 1);
                break;
            case "addDiscount":
                if (!uConfig.isSuperAdmin) fz.sendResponse("无法操作");
                fz.check_exist(new string[] { "infee-int,当日消费", "rate-int,折扣" });
                sql = " insert into discount values(@infee,@rate) ";
                res = db.ExecuteNonQuery(sql, pams.ToArray());
                if (res > 0) fz.sendResponse("添加成功", 1);
                fz.sendResponse("添加失败");
                break;
            case "changeDiscount":
                if (!uConfig.isSuperAdmin) fz.sendResponse("无法操作");
                fz.check_exist(new string[] { "id", "infee-int,当日消费", "rate-int,折扣" });
                sql = " update discount set infee=@infee,rate=@rate where id=@id ";
                res = db.ExecuteNonQuery(sql, pams.ToArray());
                if (res > 0) fz.sendResponse("修改成功", 1);
                fz.sendResponse("修改失败");
                break;
            case "discount_detail":
                if (!uConfig.isSuperAdmin) fz.sendResponse("无法操作");
                sql = " select * from discount where id=@id ";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    dic.Add("id", dt.Rows[0]["id"] + "");
                    dic.Add("infee", dt.Rows[0]["infee"] + "");
                    dic.Add("rate", dt.Rows[0]["rate"] + "");
                    fz.sendResponse("获取成功", 1, dic);
                }
                fz.sendResponse("获取失败");
                break;
            case "addProxyLevel":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您暂时没有操作权限\"}";
                }

                parames = new SqlParameter[]{
                    new SqlParameter("@proxy_name",proxy_name)
                };

                sql = " if(not Exists(select top 1 * from rkList where name=@proxy_name))begin insert into rkList values(@proxy_name) end ";

                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    src = "{\"code\":1,\"msg\":\"添加成功\"}";
                }
                else
                {
                    src = "{\"code\":1,\"msg\":\"代理名称已存在\"}";
                }
                break;
            case "change":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您暂时没有操作权限\"}";
                }
                parames = new SqlParameter[]{
                    new SqlParameter("@id",user_id),
                    new SqlParameter("@rank",user_rank),
                    new SqlParameter("@nick",user_nick),
                    new SqlParameter("@pwd",user_pwd),
                    new SqlParameter("@qq",user_qq),
                    new SqlParameter("@israte",fz.req("israte")),
                    new SqlParameter("@rateval",fz.req("rateval")),
                    new SqlParameter("@status",user_status),
                    new SqlParameter("@userType",fz.req("userType"))
                };

                sql = " update users set rk=@rank,nick=@nick,pwd=@pwd,qq=@qq,israte=@israte,rateval=@rateval,status=@status,userType=@userType where id=@id  ";
                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    cae.RemoteCache("users");
                    src = "{\"code\":1,\"msg\":\"更新成功\"}";
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"更新失败\"}";
                }
                break;
            case "cz":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您暂时没有加款权限\"}";
                }
                if (!IsNumeric(user_money))
                {
                    return "{\"code\":-1,\"msg\":\"请输入正确的加款金额\"}";
                }
                if (Convert.ToDouble(user_money) >= 0)
                {
                    temp = "充值";
                }
                else
                {
                    temp = "客服售后处理";
                }
                parames = new SqlParameter[]{
                    new SqlParameter("@userid",fz.req("userid")),
                    new SqlParameter("@money",user_money),
                    new SqlParameter("@title",temp)
                };

                sql = " declare @score decimal(18,2) update users set @score=score,score=score+@money where id=@userid if(@score is not null)begin insert into records values(@userid,@title,@money,@score+@money,'','',GETDATE())  end";

                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    src = "{\"code\":1,\"msg\":\"充值成功\"}";
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"用户不存在\"}";
                }

                break;
            case "addUser":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您暂时没有操作权限\"}";
                }
                parames = new SqlParameter[]{
                    new SqlParameter("@rank",user_rank),
                    new SqlParameter("@nick",user_nick),
                    new SqlParameter("@pwd",user_pwd),
                    new SqlParameter("@qq",user_qq),
                    new SqlParameter("@status",user_status)
                };

                sql = " if(not Exists(select top 1 * from users where nick=@nick))begin insert into users values(REPLACE(NEWID(),'-',''),null,@nick,@pwd,@qq,0,0,@rank,0,10,@status,GETDATE(),GETDATE(),'','') end ";
                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    cae.RemoteCache("users");
                    src = "{\"code\":1,\"msg\":\"开通成功！<br>用户名：" + user_nick + "<br>密码：" + user_pwd + "\"}";
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"用户名已存在\"}";
                }
                break;
            case "modorder":
                //if (!uConfig.isSuperAdmin)
                //{
                //    return "{\"code\":-1,\"msg\":\"您暂时没有操作权限\"}";
                //}
                parames = new SqlParameter[]{
                    new SqlParameter("@id",order_id),
                    new SqlParameter("@url",order_url),
                    new SqlParameter("@num",order_num),
                    new SqlParameter("@start",order_start),
                    new SqlParameter("@now",order_now),
                    new SqlParameter("@status",order_status)
                };
                temp = string.Empty;
                temp += ",num=(case when otherinfo is not null then num else @num end)";
                temp += ",othernum=(case when otherinfo is not null then @num else othernum end)";
                sql = " update orders set url=@url,startcount=@start,nowcount=@now,status=@status" + temp + " where id=@id";
                if (order_status == "已完成")
                {
                    sql += " update [liveOrders] set status=1 where orderid=@id ";
                }
                else if (order_status == "进行中")
                {
                    sql += " update [liveOrders] set status=0 where orderid=@id ";
                }
                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    src = "{\"code\":1,\"msg\":\"修改成功\"}";
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"订单不存在\"}";
                }

                break;
            case "bus":
                parames = new SqlParameter[]{
                    new SqlParameter("@rankid",bus_rankid),
                    new SqlParameter("@active_id",fz.req("active_id"))
                };
                if (fz.empty("active_id"))
                {
                    if (bus_rankid != string.Empty)
                    {
                        //代理价格
                        sql = " select ap.*,rkp.price as apPrice from apps ap left join (select * from rkPrice where rankid=@rankid) rkp on ap.appcode=rkp.appCode order by sort desc ";
                        //sql += " select opt.*,rkp.price as optPrice  from options opt left join (select * from rkPrice where rankid=@rankid) rkp on opt.code=rkp.optionCode  ";
                    }
                    else
                    {
                        //普通价格
                        sql = " select *,price as apPrice from apps order by status*-1,sort desc ";
                    }
                }
                else
                {
                    if (bus_rankid != string.Empty)
                    {
                        //代理价格
                        sql = " select ap.*,rkp.total_fee as apPrice from apps ap left join (select * from active_rankfee where active_id=@active_id) rkp on ap.appcode=rkp.appCode order by sort desc ";
                        //sql += " select opt.*,rkp.price as optPrice  from options opt left join (select * from rkPrice where rankid=@rankid) rkp on opt.code=rkp.optionCode  ";
                    }
                    else
                    {
                        //普通价格
                        sql = " select ap.*,rkp.total_fee as apPrice from apps ap left join (select * from active_appfee where active_id=@active_id) rkp on ap.id=rkp.app_id order by sort desc ";
                    }
                }
                sql += " select * from rkList with(nolock) where id=@rankid ";
                sql += " select * from pack_list  with(nolock)  ";
                ds = db.getDataSet(sql, parames);
                dt = ds.Tables[0];
                DataTable packs = ds.Tables[2];

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    List<Dictionary<string, object>> pack_list = new List<Dictionary<string, object>>();
                    if (packs.Rows.Count > 0)
                    {
                        DataTable packs_temp = selectDateTable(packs, "appid=" + dt.Rows[i]["id"]);
                        //return ToJson(packs_temp) + "111111111";
                        for (int t = 0; t < packs_temp.Rows.Count; t++)
                        {
                            Dictionary<string, object> pack_temp = new Dictionary<string, object>();
                            pack_temp.Add("id", packs_temp.Rows[t]["id"] + "");
                            pack_temp.Add("num", packs_temp.Rows[t]["num"] + "");
                            pack_temp.Add("total_fee", packs_temp.Rows[t]["total_fee"] + "");
                            pack_list.Add(pack_temp);
                        }
                    }
                    dic = new Dictionary<string, object>();
                    dic.Add("app_id", dt.Rows[i]["id"] + "");
                    dic.Add("type", dt.Rows[i]["type2"] + "");
                    dic.Add("type2", dt.Rows[i]["typename"] + "");
                    dic.Add("app_code", dt.Rows[i]["appcode"] + "");
                    dic.Add("app_name", dt.Rows[i]["typename"] + "" + dt.Rows[i]["appname"] + "");
                    dic.Add("app_price", dt.Rows[i]["apPrice"] + "");
                    dic.Add("app_notice", dt.Rows[i]["notice"] + "");
                    dic.Add("app_status", dt.Rows[i]["status"] + "");
                    dic.Add("istest", dt.Rows[i]["istest"] + "");
                    dic.Add("modeClassId", dt.Rows[i]["modeClassId"] + "");
                    dic.Add("app_cost", dt.Rows[i]["cost"] + "");
                    dic.Add("refund_order", dt.Rows[i]["refund_order"] + "");
                    dic.Add("groupid", dt.Rows[i]["groupid"] + "");
                    dic.Add("times", dt.Rows[i]["times"] + "");
                    dic.Add("close_not_refund", dt.Rows[i]["close_not_refund"] + "");
                    dic.Add("close_limit_times", dt.Rows[i]["close_limit_times"] + "");



                    dic.Add("packs", pack_list);
                    list2.Add(dic);
                }


                dic = new Dictionary<string, object>();
                dic2 = new Dictionary<string, object>();
                if (bus_rankid != string.Empty)
                {
                    dt = ds.Tables[1];
                    if (dt.Rows.Count > 0)
                    {
                        dic2.Add("res", "查询成功");
                        dic2.Add("name", dt.Rows[0]["name"] + "");
                        dic2.Add("id", bus_rankid);
                    }
                    else
                    {
                        dic2.Add("res", "您的等级分配有误");
                        dic2.Add("name", "");
                        dic2.Add("id", bus_rankid);
                    }
                }
                else
                {
                    dic2.Add("res", "");
                    dic2.Add("name", "");
                    dic2.Add("id", "");
                }

                dic.Add("app_info", list2);
                dic.Add("rank_info", dic2);

                src = JsonMapper.ToJson(dic);

                break;
            case "busSave":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您暂时没有操作权限\"}";
                }
                try
                {
                    sql = " declare @unid int ";
                    jd = JsonMapper.ToObject(bus_change);
                    for (int i = 0; i < jd["app"].Count; i++)
                    {
                        string update_type = jd["app"][i]["type"] + "";



                        id = jd["app"][i]["id"] + "";
                        code = jd["app"][i]["code"] + "";
                        price = jd["app"][i]["price"] + "";
                        if (IsNumeric(id) && IsNumeric(price))
                        {
                            if (update_type == "cost")
                            {
                                sql += string.Format(" update apps set cost={1} where id={0} ", id, price);
                            }
                            else if (update_type == "update_number")
                            {
                                pmlist["userid"] = jd["app"][i]["userid"] + "";
                                pmlist["amount"] = jd["app"][i]["amount"] + "";
                                pmlist["mincount"] = jd["app"][i]["mincount"] + "";
                                pmlist["maxcount"] = jd["app"][i]["maxcount"] + "";
                                if (
                                    IsNumeric(pmlist["userid"] + "") && (IsNumeric(pmlist["mincount"] + "") || pmlist["mincount"] + "" == "" || pmlist["mincount"] + "" == "null")
                                    && (IsNumeric(pmlist["maxcount"] + "") || pmlist["maxcount"] + "" == "" || pmlist["maxcount"] + "" == "null")
                                    && (IsNumeric(pmlist["amount"] + "") || pmlist["amount"] + "" == "" || pmlist["amount"] + "" == "null")

                                    )
                                {
                                    temp = "";

                                    if (pmlist["mincount"] + "" == "")
                                    {
                                        pmlist["mincount"] = "mincount";
                                    }
                                    if (pmlist["maxcount"] + "" == "")
                                    {
                                        pmlist["maxcount"] = "maxcount";
                                    }
                                    if (pmlist["amount"] + "" == "")
                                    {
                                        pmlist["amount"] = "amount";
                                    }

                                    //fz.sendResponse("temp = " + temp);

                                    sql += string.Format(@" 
set @unid=null
select @unid=id from user_number_limit where appid={0} and userid={1}
if(@unid is null)
begin 
    insert into user_number_limit values({1},{0},inser_{2},inser_{3},inser_{4},getdate()) 
end 
else 
begin 
    update user_number_limit set update_time=getdate(),mincount={2},maxcount={3},amount={4} where id=@unid 
end ", id, pmlist["userid"] + "", pmlist["mincount"] + "", pmlist["maxcount"] + "", pmlist["amount"] + "")
                                        .Replace("inser_mincount", "null")
                                        .Replace("inser_maxcount", "null")
                                        .Replace("inser_amount", "null")
                                        .Replace("inser_", "");

                                }
                            }
                            else
                            {
                                if (!IsNumeric(fz.req("active_id")))
                                {
                                    if (bus_rankid != "")
                                    {
                                        sql += string.Format(" if(Exists(select * from rkPrice where rankid={0} and appCode='{1}'))begin update rkPrice set price={2} where rankid={0} and appCode='{1}' end else begin insert into rkPrice values({0},{3},'{1}','',{2}) end ", bus_rankid, code, price, id);
                                    }
                                    else
                                    {
                                        sql += string.Format(" update apps set price={1} where id={0} ", id, price);
                                    }
                                }
                                else
                                {
                                    if (bus_rankid != "")
                                    {
                                        sql += string.Format(" if(Exists(select * from active_rankfee where active_id={3} and rank_id={0} and appCode='{1}'))begin update active_rankfee set total_fee={2} where active_id={3} and rank_id={0} and appCode='{1}' end else begin insert into active_rankfee values({3},{0},{4},'{1}',{2}) end ", bus_rankid, code, price, fz.req("active_id"), id);
                                    }
                                    else
                                    {
                                        sql += string.Format(" if(Exists(select * from active_appfee where active_id={2} and app_id={0}))begin update active_appfee set total_fee={1} where active_id={2} and app_id={0} end else begin insert into active_appfee values({2},{0},{1}) end ", id, price, fz.req("active_id"));
                                    }
                                }
                            }

                        }
                    }


                    //for (int i = 0; i < jd["option"].Count; i++)
                    //{
                    //    id = jd["option"][i]["id"] + "";
                    //    code = jd["option"][i]["code"] + "";
                    //    price = jd["option"][i]["price"] + "";
                    //    if (IsNumeric(id) && IsNumeric(price))
                    //    {
                    //        if (bus_rankid != "")
                    //        {
                    //            sql += string.Format(" if(Exists(select * from rkPrice where rankid={0} and optionCode='{1}'))begin update rkPrice set price={2} where rankid={0} and optionCode='{1}' end else begin insert into rkPrice values({0},'','{1}',{2}) end ", bus_rankid, code, price);
                    //        }
                    //        else
                    //        {
                    //            sql += string.Format("  update [options] set price={1} where id={0} ", id, price);
                    //        }
                    //    }
                    //}


                    for (int i = 0; i < jd["packs"].Count; i++)
                    {
                        string pack_id = jd["packs"][i]["id"] + "";
                        string pack_appid = jd["packs"][i]["appid"] + "";
                        string pack_num = jd["packs"][i]["num"] + "";
                        string pack_total_fee = jd["packs"][i]["total_fee"] + "";
                        if (IsNumeric(pack_appid) && IsNumeric(pack_num) && IsNumeric(pack_total_fee))
                        {
                            if (pack_id != "")
                            {
                                sql += string.Format("  update pack_list set num={1},total_fee={2} where id={3} ", pack_appid, pack_num, pack_total_fee, pack_id);
                            }
                            else
                            {
                                sql += string.Format(" insert into pack_list values({0},{1},{2})  ", pack_appid, pack_num, pack_total_fee);
                            }
                        }
                        else
                        {
                            if (pack_id != "")
                            {
                                if (pack_num == "" || pack_total_fee == "")
                                {
                                    sql += string.Format(" delete pack_list where id={0} ", pack_id);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    return "{\"code\":-1,\"msg\":\"请求参数错误\"}";
                }

                if (sql == string.Empty)
                {
                    return "{\"code\":-1,\"msg\":\"未做任何调整\"}";
                }
                if (bus_rankid != string.Empty)
                {
                    //如果此等级为此分站的才执行
                    sql = " if(Exists(select * from rkList where id=@rankid))begin " + sql + " end ";
                }
                parames = new SqlParameter[]{
                    new SqlParameter("@rankid",bus_rankid)
                };

                res = db.ExecuteNonQuery(sql, parames);

                if (res > 0)
                {

                    src = "{\"code\":1,\"msg\":\"调整成功\"}";
                    cae.RemoteCache("appList");
                    cae.RemoteCache("appListAll");
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"未做任何调整\"}";
                }

                break;
            case "page":
                if (!IsNumeric(_s) || Convert.ToInt32(_s) > 500)
                {
                    if (_page != "items" && IsNumeric(_s))
                    {
                        _s = "20";
                    }
                }
                //_key = SafeSql(_key);
                switch (_page)
                {
                    case "active_list":
                        cols = new List<string>();
                        cols.Add("id");
                        cols.Add("name");
                        cols.Add("startTime");
                        cols.Add("endTime");
                        cols.Add("status");
                        cols.Add("createTime");

                        //if (_key != string.Empty)
                        //{
                        //    cond += cond == string.Empty ? string.Empty : " and ";
                        //    cond += "(" + (IsNumeric(_key) ? " o.id=" + _key + " or " : "") + " url='" + _key + "' )";
                        //}
                        src = jsonClass.queryPager("active_list", cols, cond, "*,(case when startTime>GETDATE() then 0 when endTime<GETDATE() then 2 else 1 end) as status ", "createTime desc", _p, _s);
                        break;
                    case "users":
                        fz.pager_list(new List<string> { "id", "nick", "pwd", "super_user", "rk", "rkname", "uqq", "score", "cost", "status", "fee", "num", "israte", "rateval", "userType" }, true);
                        fz.pager_where(!fz.empty("keyword"), " (u.nick='" + fz.sreq("keyword") + "' or sp.nick='" + fz.sreq("keyword") + "' or u.qq='" + fz.sreq("keyword") + "') ");
                        fz.pager_data("users u left join users sp on u.sp=sp.id left join (select * from feeTotal where datediff(day,indate,getdate())=0)ft on u.id=ft.uid left join rkList rk on u.rk=rk.id", "u.*,sp.nick as super_user,rk.name as rkname,u.pwd as upwd,u.qq as uqq,ft.fee,ft.num", " isnull(ft.fee,0) desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("s"));
                        break;
                    case "orders":
                        fz.pager_list(new List<string> { "online", "rqStart", "rqNum", "rqNow", "father_id", "id", "server", "app_id", "nick", "typename as type_name", "appname as app_name", "url as order_url", "source_url as sUrl", "num as order_num", "plid", "renqi_num", "cost", "total_fee as ftip", "startCount as start", "nowCount as now", "status as order_status", "createTime as order_time", "refundTime as refund_time", "isExpire", "expireTime", "finishTime as order_finish", "orderid", "budan_num", "tempval as temp_num", "shangjia_refund", "loid", "currentPeople", "othernum", "otherinfo", "api_params", "market_money", "remark1", "status_flag", "note", "createIp", "ofrom", "transactionID", "state_text" }, false);


                        fz.pager_where(fz.req("shangjia_refund") == "true", " o.shangjia_refund=1 ");

                        fz.pager_where(!fz.empty("status"), " o.status='" + fz.sreq("status") + "' ");
                        fz.pager_where(!fz.empty("status2"), " isnull(o.status_flag,'')='" + fz.sreq("status2").Replace("无状态", "") + "' ");

                        fz.pager_where(!fz.empty("opt"), " (appcode='@{opt}') ");



                        temp2 = _key;
                        //if (_key != string.Empty)
                        //{
                        //    _key = douyin_short_urlid(_key);
                        //    if (IsNumeric(_key) || _key.IndexOf("MS") == 0)
                        //    {
                        //        temp = getLiveUserIdNew(_key);
                        //        if (!string.IsNullOrEmpty(temp))
                        //        {
                        //            temp = SafeSql(temp);
                        //            temp = " or o.url='" + temp + "' ";
                        //        }
                        //    }


                        //    fz.pager_where(true, " (" + (IsNumeric(_key) && _key.Length < 25 ? " o.id=" + _key + " or o.transactionID='" + _key + "' or " : "") + " o.source_url='" + temp2 + "' or o.url like '%" + _key + "%' " + temp + " ) ");
                        //}


                        if (!fz.empty("s_id"))
                        {
                            fz.pager_where(true, " o.id=@{s_id} ");
                        }
                        if (!fz.empty("s_bid"))
                        {
                            fz.pager_where(true, " o.transactionID=@{s_bid} ");
                        }
                        if (!fz.empty("s_url"))
                        {
                            fz.pager_where(true, " (o.source_url='@{s_url}' or o.url like '%@{s_url}%'  ) ");
                        }


                        temp_dic["db"] = "orders";
                        if (fz.req("record_orders") == "true")
                        {
                            temp_dic["db"] = "record_orders";
                        }

                        dic = fz.pager_data(temp_dic["db"] + " o with(nolock) left join apps ap with(nolock) on o.code=ap.appcode left join users u with(nolock) on u.id=o.uid left join apiname apn with(nolock) on o.app_id=apn.appid left join liveOrders lo with(nolock) on lo.orderid=o.id left join orders o2 with(nolock) on lo.sub_orderid=o2.id", "o.id,o.state_text,o.orderid,o.url,o.source_url,o.num,o.othernum,o.otherinfo,o.cost,o.total_fee,o.startCount,o.nowCount,o.status,o.createTime,o.finishTime,o.refundTime,o.createIp,o.ofrom,'*.*.*.*' as cip,'-' as ofa,o.tempval,o.shangjia_refund,u.nick,ap.appname,ap.typename,apn.name as server,o.data as plid,lo.id as loid,lo.renqi_num as renqi_num,lo.online as online,lo.budan_num,lo.currentPeople,o2.startCount as rqStart,o2.num as rqNum,o2.nowCount as rqNow,isnull(o.father_id,0) as father_id,(case when o.order_type=988 and o.expireTime>GETDATE() then 0 else 1 end) as isExpire,o.expireTime,o.api_params,o.market_money,o.remark1,o.status_flag,o.note,o.app_id,o.transactionID", "o.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("s"));



                        //if (uConfig.isSuperAdmin)
                        //{
                        //    cols.Add("createIp");
                        //    cols.Add("ofrom");
                        //}
                        //else
                        //{
                        //    cols.Add("cip as createIp");
                        //    cols.Add("ofa as ofrom");
                        //}




                        //拓展功能
                        list = (List<Dictionary<string, object>>)dic["data"];

                        if (!uConfig.isSuperAdmin)
                        {
                            for (int i = 0; i < list.Count; i++)
                            {
                                Dictionary<string, object> _tempData = list[i];

                                _tempData["plid"] = "-";
                                _tempData["budan_num"] = "-";
                                _tempData["temp_num"] = "-";
                                _tempData["orderid"] = "-";
                                _tempData["father_id"] = "0";
                                _tempData["createIp"] = "-";
                                _tempData["ofrom"] = "-";
                                _tempData["api_params"] = "-";
                                _tempData["app_id"] = "-";
                                _tempData["ftip"] = "";
                                _tempData["server"] = "";

                                list[i] = _tempData;
                            }
                        }
                        dic["data"] = list;
                        dic = fz.pager_common_response(dic, true);


                        //temp = fz.getCond();
                        //temp = temp == "" ? "" : " where " + temp;
                        //sql = " select count(0) as total_number,sum(amount) as total_amount,sum(income_amount) as total_in_amount from promotion_list a with(nolock)  " + temp;
                        //dt = db.getDataTable(sql);

                        //dic.Add("number", "0");
                        //dic.Add("total_amount", "0");
                        //dic.Add("total_in_amount", "0");
                        //if (dt.Rows.Count > 0)
                        //{
                        //    dic["total_number"] = dt.Rows[0]["total_number"] + "";
                        //    dic["total_amount"] = dt.Rows[0]["total_amount"] + "";
                        //    dic["total_in_amount"] = dt.Rows[0]["total_in_amount"] + "";
                        //}

                        fz.sendResponse("success", 1, dic);
                        break;
                    case "records-list":
                        //cols = new List<string>();
                        //cols.Add("id");
                        //cols.Add("nick");
                        //cols.Add("name");
                        //cols.Add("note");
                        //cols.Add("cost");
                        //cols.Add("rest");
                        //cols.Add("createTime"); ;

                        //if (_opt != string.Empty)
                        //{
                        //    cond += cond == string.Empty ? string.Empty : " and ";
                        //    cond += " r.name like '%" + _opt + "%'";
                        //}
                        //if (_key != string.Empty)
                        //{
                        //    cond += cond == string.Empty ? string.Empty : " and ";
                        //    cond += "(r.name like '%" + _key + "%' or r.note like '%" + _key + "%' or u.nick like '%" + _key + "%' " + (IsNumeric(_key) ? " or r.id=" + _key : "") + " )";
                        //}
                        //src = jsonClass.queryPager("records r with(nolock) left join users u on r.uid=u.id", cols, cond, "r.*,u.nick", "r.id desc", _p, _s);



                        fz.pager_list(new List<string> { "id", "nick", "name", "note", "cost", "rest", "createTime" }, true);
                        fz.pager_where(!fz.empty("start_time"), " CONVERT(VARCHAR(10), r.createTime, 120)>='@{start_time}'");
                        fz.pager_where(!fz.empty("end_time"), " CONVERT(VARCHAR(10), r.createTime, 120)<='@{end_time}'");
                        fz.pager_where(!fz.empty("opt"), "  r.name like '%" + _opt + "%' ");
                        fz.pager_where(!fz.empty("key"), "(r.name like '%" + _key + "%' or r.note like '%" + _key + "%' or u.nick like '%" + _key + "%' " + (IsNumeric(_key) ? " or r.id=" + _key : "") + " )");
                        fz.pager_data("records r with(nolock) left join users u on r.uid=u.id", "r.*,u.nick", " r.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("s"));


                        break;
                    case "discount":
                        if (!uConfig.isSuperAdmin)
                        {
                            return "[]";
                        }
                        cols = new List<string>();
                        cols.Add("id");
                        cols.Add("infee");
                        cols.Add("rate");

                        if (_key != string.Empty)
                        {
                            cond += cond == string.Empty ? string.Empty : " and ";
                            cond += "(t.order_id like '%" + _key + "%' or t.trade_no like '%" + _key + "%'  )";
                        }
                        src = jsonClass.queryPager("discount", cols, cond, "*", "rate desc", _p, _s);
                        break;
                    case "itemClass_list":
                        fz.pager_list(new List<string> { "id", "sortId", "name", "state", "create_time" }, true);

                        fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time}'");
                        fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time}'");
                        fz.pager_where(!fz.empty("status_select"), " a.state='@{status_select}'");

                        fz.pager_where(!fz.empty("keyword"), "(a.name='@{keyword}')");

                        dic = fz.pager_data(String.Format("{0} a with(nolock) ", _page), "a.*", "a.sortId asc,a.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"));
                        //拓展功能
                        list = (List<Dictionary<string, object>>)dic["data"];
                        for (int i = 0; i < list.Count; i++)
                        {
                            Dictionary<string, object> _tempData = list[i];
                            list[i] = _tempData;
                        }
                        dic["data"] = list;
                        fz.pager_common_response(dic);
                        break;
                    case "item_list":
                        fz.pager_list(new List<string> { "id", "sortId", "classId", "className", "title", "icon", "describe_text", "usd", "state", "limit_min_count", "limit_max_count", "base_style", "theme_style", "storage_num", "create_time" }, true);

                        fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time}'");
                        fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time}'");
                        fz.pager_where(!fz.empty("status_select"), " a.state='@{status_select}'");

                        fz.pager_where(!fz.empty("keyword"), "(a.title='@{keyword}')");

                        dic = fz.pager_data(String.Format("{0} a with(nolock) left join (select itemId,count(0) as storage_num from [storage_list] with(nolock) group by itemId)b on a.id=b.itemId left join itemClass_list c with(nolock) on a.classId=c.id  ", _page), "a.*,isnull(b.storage_num,0) as storage_num,c.name as className", "a.sortId asc,a.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"));
                        //拓展功能
                        list = (List<Dictionary<string, object>>)dic["data"];
                        for (int i = 0; i < list.Count; i++)
                        {
                            Dictionary<string, object> _tempData = list[i];
                            list[i] = _tempData;
                        }
                        dic["data"] = list;
                        fz.pager_common_response(dic);
                        break;
                    case "storage_list":
                        fz.pager_list(new List<string> { "id", "storage_data", "create_time" }, true);

                        fz.pager_where(!fz.empty("start_time"), " a.create_time>='@{start_time}'");
                        fz.pager_where(!fz.empty("end_time"), " a.create_time<='@{end_time}'");
                        fz.pager_where(!fz.empty("status_select"), " a.state='@{status_select}'");

                        fz.pager_where(true, " a.itemId='@{itemId}'");

                        fz.pager_where(!fz.empty("keyword"), "(a.storage_data='@{keyword}')");

                        dic = fz.pager_data(String.Format("{0} a with(nolock) ", _page), "a.*", "a.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"));
                        //拓展功能
                        list = (List<Dictionary<string, object>>)dic["data"];
                        for (int i = 0; i < list.Count; i++)
                        {
                            Dictionary<string, object> _tempData = list[i];
                            list[i] = _tempData;
                        }
                        dic["data"] = list;
                        fz.pager_common_response(dic);
                        break;
                    case "item_order_list":
                        fz.pager_list(new List<string> { "id", "orderId", "title", "email", "num", "usd", "state", "create_time@formatDate()", "paytype", "pay_time@formatDate()" }, true);

                        //status_select
                        fz.pager_where(!fz.empty("status_select"), " a.state='@{status_select}'");
                        fz.pager_where(!fz.empty("keyword"), "(a.email='@{keyword}' or a.orderId='@{keyword}')");

                        dic = fz.pager_data(String.Format("{0} a with(nolock)  ", _page), "a.*", "a.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("limit"));
                        //拓展功能
                        list = (List<Dictionary<string, object>>)dic["data"];
                        for (int i = 0; i < list.Count; i++)
                        {
                            Dictionary<string, object> _tempData = list[i];
                            list[i] = _tempData;
                        }
                        dic["data"] = list;
                        fz.pager_common_response(dic);
                        break;
                    case "app_group":
                        fz.pager_list(new List<string> { "id", "name", "maxnum", "state", "create_time" }, true);
                        fz.pager_where(!fz.empty("keyword"), " (u.name='" + fz.sreq("keyword") + "') ");
                        fz.pager_data("app_group a", "a.*", " a.id desc", (Convert.ToInt16(fz.req("p")) + 1).ToString(), fz.req("s"));
                        break;
                    default:
                        src = "{\"code\":-1,\"msg\":\"请求的页面不存在!\"}";
                        break;
                }
                break;
            case "clearCache":
                cae.RemoteCache("appType");
                cae.RemoteCache("appList");
                src = "{\"code\":1,\"msg\":\"已清除缓存\"}";
                break;
            case "appEditQuery":
                //if (!uConfig.isSuperAdmin)
                //{
                //    return "{\"code\":-1,\"msg\":\"您暂时没有操作权限\"}";
                //}
                parames = new SqlParameter[]{
                    new SqlParameter("@appcode",_appcode)
                };
                sql = " select * from apps with(nolock) where appcode=@appcode ";
                sql += " select api.*,aname.name from apis api with(nolock) left join apiname aname on api.appid=aname.appid where api.appcode=@appcode ";
                sql += " select * from pack_list  with(nolock)  ";
                ds = db.getDataSet(sql, parames);
                dt = ds.Tables[1];
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("appid", dt.Rows[i]["appid"] + "");
                    dic.Add("name", dt.Rows[i]["name"] + "");

                    list.Add(dic);
                }
                dt = ds.Tables[0];
                dic = new Dictionary<string, object>();
                if (dt.Rows.Count > 0)
                {
                    dic.Add("code", "1");
                    dic.Add("msg", "");
                    dic.Add("id", (dt.Rows[0]["id"] + ""));
                    dic.Add("appcode", _appcode);
                    dic.Add("media_type", (dt.Rows[0]["typename"] + ""));
                    dic.Add("type2", (dt.Rows[0]["type2"] + ""));

                    dic.Add("istest", (dt.Rows[0]["istest"] + ""));
                    dic.Add("modeClassId", (dt.Rows[0]["modeClassId"] + ""));
                    dic.Add("name", (dt.Rows[0]["appname"] + ""));
                    dic.Add("status", (dt.Rows[0]["status"] + ""));
                    dic.Add("appid", (dt.Rows[0]["appid"] + ""));
                    dic.Add("notice", (dt.Rows[0]["notice"] + "").Replace("<br>", "\n"));
                    dic.Add("mincount", (dt.Rows[0]["mincount"] + ""));
                    dic.Add("maxcount", (dt.Rows[0]["maxcount"] + ""));
                    dic.Add("mincost", (dt.Rows[0]["mincost"] + ""));
                    dic.Add("sort", (dt.Rows[0]["sort"] + ""));
                    dic.Add("limTotalNum", (dt.Rows[0]["limTotalNum"] + ""));
                    dic.Add("refund_order", (dt.Rows[0]["refund_order"] + ""));
                    dic.Add("repair_order_intelval", (dt.Rows[0]["repair_order_intelval"] + ""));
                    dic.Add("repair_order_max", (dt.Rows[0]["repair_order_max"] + ""));
                    dic.Add("groupid", dt.Rows[0]["groupid"] + "");
                    dic.Add("times", dt.Rows[0]["times"] + "");
                    dic.Add("close_not_refund", dt.Rows[0]["close_not_refund"] + "");
                    dic.Add("close_limit_times", dt.Rows[0]["close_limit_times"] + "");

                    dic.Add("apps", list);
                }
                else
                {
                    dic.Add("code", "-1");
                    dic.Add("msg", "appcode不存在");
                }

                src = JsonMapper.ToJson(dic);

                break;
            case "changeEdit":
                //if (!uConfig.isSuperAdmin)
                //{
                //    return "{\"code\":-1,\"msg\":\"您的聪明才智导致您站点将被封停\"}";
                //}
                parames = new SqlParameter[]{
                    new SqlParameter("@origin_appcode",fz.req("origin_appcode")),
                    new SqlParameter("@appcode",_appcode),
                    new SqlParameter("@appname",_appname),
                    new SqlParameter("@appid",_appid),
                    new SqlParameter("@appnotice",_appnotice),
                    new SqlParameter("@mincount",_appmincount),
                    new SqlParameter("@maxcount",_appmaxcount),
                    new SqlParameter("@mincost",_appmincost),
                    new SqlParameter("@appsort",_appsort),
                    new SqlParameter("@istest",_istest),
                    new SqlParameter("@modeClassId",fz.req("modeClassId")),
                    new SqlParameter("@limTotalNum",fz.req("limTotalNum")),
                    new SqlParameter("@maxPushMoney",fz.req("maxPushMoney")),
                    new SqlParameter("@refund_order",fz.req("refund_order"))
                };

                if (_appid == "-1")
                {
                    sql += " update apps set appcode=@appcode,appname=@appname,sort=@appsort,notice=@appnotice,mincount=@mincount,maxcount=@maxcount,minCost=@mincost,status=-1,istest=@istest,modeClassId=@modeClassId,limTotalNum=@limTotalNum,refund_order=@refund_order where appcode=@origin_appcode ";
                }
                else
                {
                    sql += " update apps set appcode=@appcode,appname=@appname,sort=@appsort,notice=@appnotice,mincount=@mincount,maxcount=@maxcount,minCost=@mincost,appid=@appid,status=1,istest=@istest,modeClassId=@modeClassId,limTotalNum=@limTotalNum,refund_order=@refund_order where appcode=@origin_appcode ";
                }
                sql += " update apis set appcode=@appcode where appcode=@origin_appcode ";

                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    src = "{\"code\":1,\"msg\":\"修改成功\"}";
                    cae.RemoteCache("appList");
                    cae.RemoteCache("appType");
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"修改失败\"}";
                }
                break;
            case "select_ip":
                parames = new SqlParameter[]{
                    new SqlParameter("@name",_name)
                };
                sql = " select * from blacklist with(nolock) ";
                dt = db.getDataTable(sql, parames);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dicArr.Add(dt.Rows[i]["ip"] + "");
                }
                src = JsonMapper.ToJson(dicArr);
                break;
            case "baiList":
                parames = new SqlParameter[]{
                    new SqlParameter("@name",_name)
                };
                sql = " select * from baiList with(nolock) ";
                dt = db.getDataTable(sql, parames);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dicArr.Add(dt.Rows[i]["ip"] + "");
                }
                src = JsonMapper.ToJson(dicArr);
                break;
            case "baiList_save":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您的轨迹已被系统记录\"}";
                }
                g = c_content.Split('\n');
                pas.Add(new SqlParameter("@name", c_name));
                sql = " delete baiList  ";
                for (int i = 0; i < g.Length; i++)
                {
                    if (!string.IsNullOrEmpty(g[i]))
                    {
                        sql += string.Format(" insert into baiList values(@data{0}) ", i);
                        pas.Add(new SqlParameter("@data" + i, g[i]));
                    }

                }
                parames = pas.ToArray();
                dt = db.getDataTable(sql, parames);
                cae.RemoteCache("api_bai_list");
                src = "{\"code\":1,\"msg\":\"更新成功\"}";
                break;
            case "select_kwblack":
                parames = new SqlParameter[]{
                    new SqlParameter("@name",_name)
                };
                sql = " select * from keyBlack ";
                dt = db.getDataTable(sql, parames);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dicArr.Add(dt.Rows[i]["kw"] + "");
                }
                src = JsonMapper.ToJson(dicArr);
                break;
            case "s_ip":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您的轨迹已被系统记录\"}";
                }
                g = c_content.Split('\n');
                pas.Add(new SqlParameter("@name", c_name));
                sql = " delete blacklist  ";
                for (int i = 0; i < g.Length; i++)
                {
                    if (!string.IsNullOrEmpty(g[i]))
                    {
                        sql += string.Format(" insert into blacklist values(@data{0}) ", i);
                        pas.Add(new SqlParameter("@data" + i, g[i]));
                    }

                }
                parames = pas.ToArray();
                dt = db.getDataTable(sql, parames);
                src = "{\"code\":1,\"msg\":\"更新成功\"}";
                break;
            case "s_kwblack":
                g = c_content.Split('\n');
                sql = " delete keyBlack  ";
                for (int i = 0; i < g.Length; i++)
                {
                    if (!string.IsNullOrEmpty(g[i]))
                    {
                        sql += string.Format(" insert into keyBlack values(@data{0}) ", i);
                        pas.Add(new SqlParameter("@data" + i, g[i]));
                    }

                }
                parames = pas.ToArray();
                dt = db.getDataTable(sql, parames);
                cae.RemoteCache("keyblack");
                src = "{\"code\":1,\"msg\":\"更新成功\"}";
                break;
            case "select_comment":
                parames = new SqlParameter[]{
                    new SqlParameter("@name",_name)
                };
                sql = " select * from c_comment where name=@name ";
                dt = db.getDataTable(sql, parames);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dicArr.Add(dt.Rows[i]["data"] + "");
                }
                src = JsonMapper.ToJson(dicArr);
                break;
            case "s_comment":
                if (!uConfig.isSuperAdmin)
                {
                    return "{\"code\":-1,\"msg\":\"您的轨迹已被系统记录\"}";
                }
                parames = new SqlParameter[]{
                    new SqlParameter("@name",c_name)
                };
                g = c_content.Split('\n');
                if (g.Length == 0 || g[0] == "")
                {
                    sql = " delete [category] where name=@name and appcode='dy-flpl' ";
                }
                else
                {
                    sql += " if(not Exists( select * from category where name=@name and appcode='dy-flpl' ))begin insert into category values('dy-flpl',@name) end ";
                    sql += " delete c_comment where name=@name ";
                    pas.Add(new SqlParameter("@name", c_name));
                    for (int i = 0; i < g.Length; i++)
                    {
                        if (!string.IsNullOrEmpty(g[i]))
                        {
                            sql += string.Format(" insert into c_comment values(@name,@data{0}) ", i);
                            pas.Add(new SqlParameter("@data" + i, g[i]));
                        }

                    }
                    parames = pas.ToArray();
                }
                dt = db.getDataTable(sql, parames);
                cae.RemoteCache("category");
                src = "{\"code\":1,\"msg\":\"更新成功\"}";
                break;

            case "systemSave":
                parames = new SqlParameter[]{
                    new SqlParameter("@u",uConfig.p_userNickAD),
                    new SqlParameter("@p",uConfig.p_passWordAD),
                    new SqlParameter("@nick",_nick),
                    new SqlParameter("@pwd",_pwd)
                };

                sql = " update account set nick=@nick,pwd=@pwd where nick=@u and pwd=@p  ";
                res = db.ExecuteNonQuery(sql, parames);

                if (res > 0)
                {
                    src = "{\"code\":1,\"msg\":\"修改成功\"}";

                    Response.Cookies[uConfig.loginParames_nick_admin].Expires = DateTime.Now.AddDays(-1);
                    Response.Cookies[uConfig.loginParames_pwd_admin].Expires = DateTime.Now.AddDays(-1);

                    newCookie(uConfig.loginParames_nick_admin, _nick);
                    newCookie(uConfig.loginParames_pwd_admin, _pwd);
                }
                else
                {
                    src = "{\"code\":1,\"msg\":\"修改失败\"}";
                }
                break;
            case "refund_order":
                parames = new SqlParameter[]{
                    new SqlParameter("@id",_id)
                };
                sql = " update orders set status='退单中',refundTime=(case when refundTime is null then GETDATE() else refundTime end) output deleted.* where id=@id and (status='未开始' or status='进行中' or status='订单异常') ";
                dt = db.getDataTable(sql, parames);
                if (dt.Rows.Count > 0)
                {
                    temp = dt.Rows[0]["status"] + "";
                    log.WriteLog("退单", "后台|" + uConfig.p_userNickAD, "id:" + _id + ",status:" + temp);
                    dic.Add("code", 1);
                    if (temp == "未开始")
                    {
                        parames = new SqlParameter[]{
                                new SqlParameter("@orderid",_id)
                            };
                        sql = " [tuidan] @orderid,'售后' ";
                        db.ExecuteNonQuery(sql, parames);

                        dic.Add("msg", "退单成功。");
                    }
                    else
                    {
                        dic.Add("msg", "申请成功，系统将在1-2分钟内为您处理退单。");
                    }
                }
                else
                {
                    dic.Add("code", -1);
                    dic.Add("msg", "此订单无法发起退单请求");
                }
                src = JsonMapper.ToJson(dic);
                break;
            case "refund_fast":
                log.WriteLog("快速退单", "后台|" + uConfig.p_userNickAD, "id:" + _id);
                parames = new SqlParameter[]{
                    new SqlParameter("@orderid",_id)
                };
                sql = " [tuidan] @orderid,'售后-快' ";
                res = db.ExecuteNonQuery(sql, parames);
                if (res > 0)
                {
                    dic.Add("code", 1);
                    dic.Add("msg", "退单成功");
                }
                else
                {
                    dic.Add("code", -1);
                    dic.Add("msg", "当前状态不允许退单");
                }
                src = JsonMapper.ToJson(dic);
                break;
            case "activity_create":
                parames = new SqlParameter[]{
                    new SqlParameter("@act","order_q"),
                    new SqlParameter("@token",user_token)
                };

                sql = " [newActivity] @token,@act ";
                dt = db.getDataTable(sql, parames);
                if (dt.Rows.Count > 0)
                {
                    src = "{\"code\":1,\"msg\":\"" + dt.Rows[0]["msg"] + "\",\"url\":\"../cha/order_list.aspx?sign=" + dt.Rows[0]["sign"] + "\"}";
                }
                else
                {
                    src = "{\"code\":-1,\"msg\":\"地址查询失败\"}";
                }

                break;
            case "opens_q":
                sql = " select ap.*,(case when op.id is null then 0 else 1 end) as isopen from [apps] ap left join (select * from opens where uid=@uid) op on ap.id=op.appid where istest=2 ";
                dt = db.getDataTable(sql, pams.ToArray());
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["id"] + "");
                    dic.Add("name", dt.Rows[i]["appname"] + "");
                    dic.Add("isopen", dt.Rows[i]["isopen"] + "");
                    list.Add(dic);
                }

                dic = new Dictionary<string, object>();
                dic.Add("apps", list);
                fz.sendResponse("获取成功", 1, dic);
                break;
            case "check_tuidan_orders":
                temp = formatOrderids(fz.req("ids"), true);
                if (fz.req("view_all") != "true")
                {
                    if (string.IsNullOrEmpty(temp))
                    {
                        fz.sendResponse("请输入要退单的id");
                    }
                    temp = " and url in (" + temp + ") ";
                }
                else
                {
                    temp = string.Empty;
                }
                sql = " select o.*,ap.appname,u.nick,(o.cost/o.num) * (o.startCount+o.num-o.nowCount) as refund_fee from orders o with(nolock) left join apps ap with(nolock) on o.code=ap.appcode left join users u with(nolock) on o.uid=u.id where o.status='退单中' " + temp + " order by refundTime ";
                dt = db.getDataTable(sql);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["id"] + "");
                    dic.Add("nick", dt.Rows[i]["nick"] + "");
                    dic.Add("url", dt.Rows[i]["url"] + "");
                    dic.Add("name", dt.Rows[i]["appname"] + "");
                    dic.Add("startCount", dt.Rows[i]["startCount"] + "");
                    dic.Add("nowCount", dt.Rows[i]["nowCount"] + "");
                    dic.Add("num", dt.Rows[i]["num"] + "");
                    dic.Add("total_fee", dt.Rows[i]["cost"] + "");
                    dic.Add("refund_fee", dt.Rows[i]["refund_fee"] + "");
                    dic.Add("createTime", dt.Rows[i]["createTime"] + "");
                    dic.Add("refundTime", dt.Rows[i]["refundTime"] + "");

                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("data", list);
                fz.sendResponse("success", 1, dic);
                break;
            case "check_budan_orders":
                temp = formatOrderids(fz.req("ids"), true);

                if (string.IsNullOrEmpty(temp))
                {
                    fz.sendResponse("请输入要补单的id");
                }
                temp = " o.id in (" + temp + ") ";

                sql = " select o.*,ap.appname,u.nick,(o.cost/o.num) * (o.startCount+o.num-o.nowCount) as refund_fee,lv.renqi_num,dateadd(hour,total_day,lv.createTime) as rqExpireTime from orders o with(nolock) left join apps ap with(nolock) on o.code=ap.appcode left join users u with(nolock) on o.uid=u.id left join liveOrders lv with(nolock) on o.id=lv.orderid where " + temp + " and lv.id is not null order by refundTime ";

                dt = db.getDataTable(sql);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["id"] + "");
                    dic.Add("nick", dt.Rows[i]["nick"] + "");
                    dic.Add("url", dt.Rows[i]["url"] + "");
                    dic.Add("name", dt.Rows[i]["appname"] + "");
                    dic.Add("startCount", dt.Rows[i]["startCount"] + "");
                    dic.Add("nowCount", dt.Rows[i]["nowCount"] + "");
                    dic.Add("num", dt.Rows[i]["num"] + "");
                    dic.Add("total_fee", dt.Rows[i]["cost"] + "");
                    dic.Add("refund_fee", dt.Rows[i]["refund_fee"] + "");
                    dic.Add("createTime", dt.Rows[i]["createTime"] + "");
                    dic.Add("refundTime", dt.Rows[i]["refundTime"] + "");
                    dic.Add("renqi_num", dt.Rows[i]["renqi_num"] + "");
                    dic.Add("expireTime", dt.Rows[i]["rqExpireTime"] + "");
                    dic.Add("status", dt.Rows[i]["status"] + "");

                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("data", list);
                fz.sendResponse("success", 1, dic);
                break;
            case "pl_order_tuidan":
                temp = formatOrderids(fz.req("ids"));
                log.WriteLog("批量退单", "后台|" + uConfig.p_userNickAD, "id:" + temp);
                g = temp.Split(',');
                for (int i = 0; i < g.Length; i++)
                {
                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter("@orderid", g[i]));
                    sql = " [tuidan] @orderid,'售后_批' ";
                    res = db.ExecuteNonQuery(sql, pams.ToArray());

                    dic = new Dictionary<string, object>();
                    dic.Add("id", g[i]);
                    if (res > 0)
                    {
                        dic.Add("status", "success");
                    }
                    else
                    {
                        dic.Add("status", "fail");
                    }
                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("data", list);
                fz.sendResponse("success", 1, dic);
                break;
            case "pl_order_budan":
                temp = formatOrderids(fz.req("ids"));
                log.WriteLog("批量补单", "后台|" + uConfig.p_userNickAD, "id:" + temp + "|hour:" + fz.req("hour"));
                g = temp.Split(',');

                if (!IsNumeric(fz.req("hour")))
                {
                    fz.sendResponse("请输入补单时长");
                }
                for (int i = 0; i < g.Length; i++)
                {
                    temp = "@orderid";
                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter(temp, g[i]));
                    pams.Add(new SqlParameter("@hour", fz.req("hour")));
                    sql = " [budan] @orderid,@hour ";
                    res = db.ExecuteNonQuery(sql, pams.ToArray());

                    dic = new Dictionary<string, object>();
                    dic.Add("id", g[i]);
                    if (res > 0)
                    {
                        dic.Add("status", "success");
                    }
                    else
                    {
                        dic.Add("status", "fail");
                    }
                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("data", list);
                fz.sendResponse("success", 1, dic);
                break;
            case "o_status":
                if (fz.req("type") == "restatus")
                {
                    sql = " update orders set status=(case when status='已完成' then '进行中' else '已完成' end) where id=@id and order_type=988 and expireTime>GETDATE() ";
                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql, pams.ToArray());
                    if (res > 0)
                    {
                        fz.sendResponse("操作成功", 1);
                    }
                    else
                    {
                        fz.sendResponse("当前订单状态不可更改", 0);
                    }
                }
                else
                {
                    fz.sendResponse("您的操作有误");
                }
                break;
            case "order_update":
                sql = " update orders set note=@note where id=@id ";

                res = db.ExecuteNonQuery(sql, pams.ToArray());
                if (res > 0)
                {
                    fz.sendResponse("操作成功", 1);
                }
                else
                {
                    fz.sendResponse("当前订单不可更改");
                }
                break;
            case "leaveRoom":
                temp = fz.req("roomid").Replace("【补单】", "").Replace("【新订单重下】", "");
                //{"name":"leaveRoom","param":{"room_id":"6866759728639249160"}}
                temp = SendRequest(uConfig._static_dyApiurl + "v1/notice/add", "{\"name\":\"leaveRoom\",\"param\":{\"room_id\":\"" + temp + "\"}}", "application/json");
                fz.sendResponse("通知结果:" + temp, 1);
                break;
            case "get_summary":
                fz.pager_where(!fz.empty("start_time"), " inDate>='@{start_time}'");
                fz.pager_where(!fz.empty("end_time"), " inDate<='@{end_time}'");

                temp = fz.getCond();
                temp = temp == "" ? "" : " where " + temp;


                //sql = " select type,SUM((case when state=1 then amount+amountRate else 0 end)) as amount,SUM((case when state=1 then 1 else 0 end)) as totalNum,SUM((case when state=1 then 0 else 1 end)) as fail_num from [feeTotal] with(nolock)  " + temp + " group by type ";
                //dt = db.getDataTable(sql);
                //for (int i = 0; i < dt.Rows.Count; i++)
                //{
                //    dic.Add("type_" + dt.Rows[i]["type"] + "", dt.Rows[i]["amount"] + "");
                //    dic.Add("type_" + dt.Rows[i]["type"] + "_totalNum", dt.Rows[i]["totalNum"] + "");
                //    dic.Add("type_" + dt.Rows[i]["type"] + "_failNum", dt.Rows[i]["fail_num"] + "");
                //}


                // sql = " select isnull(SUM((case when uid<>" + server_userId + " then fee else 0 end)),0)/" + devide_num + " as rxf,sum((case when uid<>" + server_userId + " then num else 0 end))/" + devide_num + " as oCount,isnull(SUM((case when uid=" + server_userId + " then fee else 0 end)),0)/" + devide_num + " as bd_rxf,sum((case when uid=" + server_userId + " then num else 0 end))/" + devide_num + " as bd_oCount from feeTotal with(nolock) where DATEDIFF(DAY,indate,GETDATE())=0  ";
                sql = " select isnull(SUM(fee),0) as amount,isnull(SUM(num),0) as numbers from [feeTotal] with(nolock)  " + temp;
                dt = db.getDataTable(sql);


                if (dt.Rows.Count > 0)
                {
                    dic.Add("amount", dt.Rows[0]["amount"] + "");
                    dic.Add("numbers", dt.Rows[0]["numbers"] + "");
                }
                else
                {
                    dic.Add("amount", "0");
                    dic.Add("numbers", "0");
                }



                sql = " select appid,isnull(SUM(amount),0) as total_amount,isnull(SUM(refund_amount),0) as total_refund_amount,isnull(SUM(num),0) as total_numbers,isnull(SUM(refund_num),0) as total_refund_numbers from [transaction_total] with(nolock)  " + temp + "  group by appid  order by isnull(SUM(amount),0) desc";
                sql += " select t.*,u.nick from (select uid,isnull(SUM(amount),0) as total_amount,isnull(SUM(refund_amount),0) as total_refund_amount,isnull(SUM(num),0) as total_numbers,isnull(SUM(refund_num),0) as total_refund_numbers from [transaction_total] with(nolock)  " + temp + "  group by uid)t left join users u with(nolock) on t.uid=u.id  order by t.total_amount desc";

                ds = db.getDataSet(sql);
                dt = ds.Tables[0];
                dt2 = db.getDataTable(" select * from apps with(nolock) order by typename,sort ");


                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    temp_dt = selectDateTable(dt2, "id=" + dt.Rows[i]["appid"] + "");

                    if (temp_dt.Rows.Count > 0)
                    {
                        temp_dic = new Dictionary<string, object>();
                        temp_dic.Add("id", temp_dt.Rows[0]["id"]);
                        temp_dic.Add("name", temp_dt.Rows[0]["appname"]);
                        temp_dic.Add("tp", temp_dt.Rows[0]["typename"]);
                        temp_dic.Add("total_amount", dt.Rows[i]["total_amount"]);
                        temp_dic.Add("total_numbers", dt.Rows[i]["total_numbers"]);
                        temp_dic.Add("total_refund_amount", dt.Rows[i]["total_refund_amount"]);
                        temp_dic.Add("total_refund_numbers", dt.Rows[i]["total_refund_numbers"]);
                        list.Add(temp_dic);
                    }

                }
                dic.Add("list", list);

                list = new List<Dictionary<string, object>>();
                dt = ds.Tables[1];
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    temp_dic = new Dictionary<string, object>();
                    temp_dic.Add("name", dt.Rows[i]["nick"]);
                    temp_dic.Add("total_amount", dt.Rows[i]["total_amount"]);
                    temp_dic.Add("total_numbers", dt.Rows[i]["total_numbers"]);
                    temp_dic.Add("total_refund_amount", dt.Rows[i]["total_refund_amount"]);
                    temp_dic.Add("total_refund_numbers", dt.Rows[i]["total_refund_numbers"]);
                    list.Add(temp_dic);


                }
                dic.Add("users", list);

                fz.sendResponse("SUCCESS", 1, dic);

                break;
            default:
                src = "{\"code\":-1,\"msg\":\"您的请求已沉入海底~\"}";
                break;
        }

        return src;
    }




    public string formatOrderids(string orderids, bool withSqlString = false)
    {
        string[] s = orderids.Split(',');

        orderids = "";
        for (int i = 0; i < s.Length; i++)
        {
            if (IsNumeric(s[i]))
            {
                if (orderids != "")
                {
                    orderids += ",";
                }
                if (withSqlString)
                {
                    s[i] = "'" + s[i] + "'";
                }
                orderids += s[i];
            }
        }
        return orderids;
    }
}