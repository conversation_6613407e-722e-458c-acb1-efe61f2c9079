using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class admin_proxyUser : baseClass
{
    public DataTable rank_list = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            dbClass db = new dbClass();

            string sql = " select * from rkList with(nolock) ";
            rank_list = db.getDataTable(sql);
        }
    }
}