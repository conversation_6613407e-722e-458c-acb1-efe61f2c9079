using LitJson;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

/// <summary>
/// unified 的摘要说明
/// </summary>
public class unified : globalClass
{
    public unified()
    {
        //所有方法统一接入
    }

    //创建订单
    public string OrderCreate(string token, string code, string option_id, string url, string num, string order_data, string yzid, string param, string orderid, string orderFrom, string father_id = "0", string now_count = "0", string tempval = "", string rqinfo = "")
    {
        string _tmp, _tmp2 = string.Empty;
        string _code = "code";
        string _message = "msg";
        if (orderFrom == "API")
        {
            _code = "status";
            _message = "message";
        }

        Dictionary<string, object> dic = new Dictionary<string, object>();
        string appid = code;
        DataTable dtApp;
        if (code.IndexOf("fromcode_") != -1)
        {
            dtApp = selectDateTable(uConfig.appListAll, "appcode='" + code.Replace("fromcode_", "").Replace("_msorder", "") + "'");
        }
        else
        {
            dtApp = selectDateTable(uConfig.appListAll, "id=" + appid);
        }
        if (dtApp.Rows.Count == 0)
        {
            return getResBody(_config("errorType"), -1, null, _code, _message);
        }
        appid = dtApp.Rows[0]["id"] + "";
        code = dtApp.Rows[0]["appcode"] + "";

        string source_url = string.Empty;

        source_url = url;

        string user_ip = getUserIP(HttpContext.Current);
        if (!IsValidIP(user_ip))
        {
            return getResBody(_config("ipLimit"), -1, null, _code, _message);
        }

        code = code.Replace(" ", "");
        option_id = option_id.Replace(" ", "");


        if (url.IndexOf("://v.douyin.com/") != -1)
        {
            string code_temp = getContainer(url + "/", "//v.douyin.com/", "/");
            url = "http://v.douyin.com/" + code_temp + "/";
            if (source_url.IndexOf("https://v.douyin.com/") != 0)
            {
                log.WriteLog("order-Exp", "short_url", source_url + " >> " + url);
                source_url = url.Replace("http://v.douyin", "https://v.douyin");
            }
            string rsp_temp = GetHtmlCon(url, "", false);
            url = getContainer(rsp_temp, "<a href=\"", "\"");
            if (url == "")
            {
                log.WriteLog("order-UrlError", "url空", source_url);
                return getResBody(_config("errorUrl"), -1, null, _code, _message);
            }
        }

        //tk短连接转换

        string base_url = url;

        if (tiktokShortUrl(url, out _tmp, out _tmp2))
        {
            url = _tmp;
            source_url = _tmp2;
        }

        string check_id = string.Empty;
        string check_msg = string.Empty;
        string new_url = string.Empty;

        //if (!OrderUrlCheck(code, url, out check_id, out check_msg, out new_url))
        //{
        //    log.WriteLog("order-check-error", "error", "code:" + code + ",url:" + url + ",check_id:" + check_id + ",source_url:" + source_url + ",base_url:" + base_url);
        //    return getResBody(_config("errorUrl"), -1, null, _code, _message);
        //}
        //url = new_url;



        string sql = string.Empty;
        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        DataTable dt = new DataTable();


        string start_count = "0";
        if (token == "1" && param.IndexOf("startnum_") != -1)
        {
            param = param.Replace("startnum_", "");
            if (!IsNumeric(param))
            {
                param = "0";
            }
            start_count = param;
        }

        string[] g;
        List<string> list = new List<string>();
        string orderdata_temp = string.Empty;
        if (code.IndexOf("-plzdy") != -1 || code.IndexOf("-dmzdy") != -1)
        {
            if (order_data.IndexOf("#换行#") != -1)
            {
                g = order_data.Split(new string[] { "#换行#" }, StringSplitOptions.None);
            }
            else if (order_data.IndexOf("#") != -1)
            {
                g = order_data.Split(new string[] { "#" }, StringSplitOptions.None);
            }
            else
            {
                g = order_data.Split(new string[] { "\n" }, StringSplitOptions.None);
            }


            for (int i = 0; i < g.Length; i++)
            {
                list.Add(g[i]);
                orderdata_temp += g[i] + ",";
            }
            order_data = orderdata_temp;


            if (order_data == "")
            {
                return getResBody("please input comment", 1001);
            }

            order_data = JsonMapper.ToJson(list);

        }


        pams.Add(new SqlParameter("@token", token));
        pams.Add(new SqlParameter("@appid", appid));
        pams.Add(new SqlParameter("@opction_id", option_id));
        pams.Add(new SqlParameter("@url", url));
        pams.Add(new SqlParameter("@source_url", source_url));
        pams.Add(new SqlParameter("@num", num));
        pams.Add(new SqlParameter("@startCount", start_count));
        pams.Add(new SqlParameter("@nowCount", now_count));
        pams.Add(new SqlParameter("@data", order_data));
        pams.Add(new SqlParameter("@note", yzid));
        pams.Add(new SqlParameter("@orderid", orderid));
        pams.Add(new SqlParameter("@ofrom", orderFrom));
        pams.Add(new SqlParameter("@ip", ""));
        pams.Add(new SqlParameter("@father_id", father_id));
        pams.Add(new SqlParameter("@rqinfo", rqinfo));


        sql = " [createNew] @token,@appid,@opction_id,@url,@source_url,@num,@startCount,@nowCount,@data,@note,@orderid,@father_id,0,@ofrom,@ip,@rqinfo ";
        try
        {
            dt = db.getDataTable(sql, pams.ToArray());
        }
        catch (Exception ex)
        {
            log.WriteLog("订单异常", "errmsg", ex.Message.ToString() + "|" + string.Format("code:{0},url:{1},num:{2},data:{3},note:{4},ip:{5}", code, url, num, order_data, yzid, ""));
            return getResBody(_config("orderEx"), -1, null, _code, _message);
        }

        string result = string.Empty;
        if (dt.Rows.Count > 0)
        {
            dic = new Dictionary<string, object>();
            string errcode = string.Empty;
            string errmsg = string.Empty;
            string m_OrderId = string.Empty;
            string m_Appid = string.Empty;

            errcode = dt.Rows[0]["code"] + "";
            errmsg = dt.Rows[0]["errmsg"] + "";

            try { dic.Add("OrderId", dt.Rows[0]["transactionID"] + ""); }
            catch (Exception) { }

            try { dic.Add("user_score", dt.Rows[0]["user_score"] + ""); }
            catch (Exception) { }
            if (errcode == "1")
            {
                m_Appid = dt.Rows[0]["appid"] + "";
                num = dt.Rows[0]["order_num"] + "";
                if (Convert.ToDouble(m_Appid) >= 10100)
                {
                    //api下单
                    Dictionary<string, object> _obj = new Dictionary<string, object>();
                    Dictionary<string, object> _return = new Dictionary<string, object>();

                    _obj.Add("orderid", dt.Rows[0]["orderid"] + "");
                    _obj.Add("appid", m_Appid);
                    _obj.Add("code", code);
                    _obj.Add("url", url);
                    _obj.Add("source_url", source_url);
                    _obj.Add("num", num);
                    _obj.Add("order_data", order_data);

                    _return = Api_Order(_obj);
                    if (Convert.ToInt16(_return["code"] + "") != 1)
                    {
                        sql = " delete [orders] where id=" + dt.Rows[0]["orderid"] + " ";
                        db.ExecuteNonQuery(sql);
                        return getResBody(_return["message"] + "", -1, dic, _code, _message);
                    }

                    pams = new List<SqlParameter>();
                    pams.Add(new SqlParameter("@orderid", dt.Rows[0]["orderid"] + ""));
                    pams.Add(new SqlParameter("@id", _return["id"] + ""));
                    sql = " update orders set orderid=@id where id=@orderid ";
                    db.ExecuteNonQuery(sql, pams.ToArray());
                    //orderid
                }
                result = getResBody("success", 1, dic, _code, _message);
            }
            else
            {
                result = getResBody(errmsg, -1, dic, _code, _message);
            }
        }
        else
        {
            result = getResBody(_config("orderEx"), -1, null, _code, _message);
        }


        return result;
    }

    public Dictionary<string, object> Api_Order(Dictionary<string, object> _obj = null)
    {
        Dictionary<string, object> _data = new Dictionary<string, object>();
        string tempid = string.Empty;
        string token = string.Empty;
        string apiurl = string.Empty;
        string apidata = string.Empty;
        string _api = string.Empty;
        string result = string.Empty;
        string post_data = string.Empty;

        string result_code = string.Empty;
        string result_message = string.Empty;
        string result_OrderId = string.Empty;

        string localApi = "http://*************:12239/index?type=http&";
        bool isLocal = false;
        if (HttpContext.Current.Request.Url.Host == "localhost")
        {
            isLocal = true;
        }

        _data.Add("code", -1);
        _data.Add("message", "下单下单失败");

        string _orderUrl = _obj["url"] + "";
        string _orderNum = _obj["num"] + "";
        string _orderData = _obj["order_data"] + "";


        SortedDictionary<string, object> _dic = new SortedDictionary<string, object>();

        string _Appid = _obj["appid"] + "";
        string _Type = _obj["code"] + "";
        switch (_Appid)
        {
            case "10100":
                _api = "http://**************:5005";
                tempid = "token_" + _obj["appid"] + "appid";
                token = cae.GetCache(tempid) + "";
                if (string.IsNullOrEmpty(token))
                {
                    //get token
                    apiurl = _api + "/admin/user/getToken?loginAccount=b115&loginPwd=5386cce622b5cdeed8353bec3053649d";
                    if (isLocal)
                    {
                        apiurl = localApi + "m=GET&url=" + Uri.EscapeDataString(apiurl);
                    }
                    result = GetHtml(apiurl, "", "");
                    log.WriteLog("api_log", _Appid, "login " + _Type + " " + _orderUrl + " " + _orderNum + " " + apiurl + "," + apidata + " " + result);
                    result_code = get_json_val(result, "resCode");
                    result_message = get_json_val(result, "resDesc");
                    token = get_json_val(result, "data.token");

                    if (string.IsNullOrEmpty(token))
                    {
                        _data["message"] = "token获取失败";
                        return _data;
                    }
                    cae.SetCache(tempid, token);
                }


                //create Order
                apiurl = _api + "/admin/service/addServiceOrder";
                switch (_Type)
                {
                    case "tk-gz":
                    case "tk-gz2":
                        _Type = "1";
                        apidata = "taskTypeId=" + _Type + "&follow_list=" + Uri.EscapeDataString(_orderUrl + "," + _orderNum) + "&YXXT=" + token + "&taskStartTime=2022-01-01 00:00&taskEndTime=2090-01-01 00:00";
                        break;
                    case "tk-dz":
                    case "tk-dz2":
                        _Type = "2";
                        apidata = "taskTypeId=" + _Type + "&like_list=" + Uri.EscapeDataString(_orderUrl + "," + _orderNum) + "&YXXT=" + token + "&taskStartTime=2022-01-01 00:00&taskEndTime=2090-01-01 00:00";
                        break;
                    case "tk-bf":
                    case "tk-bf2":
                        _Type = "3";
                        apidata = "taskTypeId=" + _Type + "&view_list=" + Uri.EscapeDataString(_orderUrl + "," + _orderNum) + "&YXXT=" + token + "&taskStartTime=2022-01-01 00:00&taskEndTime=2090-01-01 00:00";
                        break;
                    case "tk-pldz":
                    case "tk-pldz2":
                        _Type = "9";
                        apidata = "taskTypeId=" + _Type + "&like_comment_list=" + Uri.EscapeDataString(_orderUrl + "," + _orderNum) + "&prize_comment_content=" + Uri.EscapeDataString(_orderData) + "&YXXT=" + token + "&taskStartTime=2022-01-01 00:00&taskEndTime=2090-01-01 00:00";
                        break;
                    default:
                        _data["message"] = "type获取失败";
                        return _data;
                        break;
                }

                post_data = apidata;

                if (isLocal)
                {
                    post_data = "m=POST&url=" + Uri.EscapeDataString(apiurl) + "&data=" + Uri.EscapeDataString(apidata);
                    apiurl = localApi;
                }
                //result = GetHtml(apiurl, "", "");
                result = SendRequestC(apiurl, post_data, "");

                log.WriteLog("api_log", _Appid, "create " + _Type + " " + _orderUrl + " " + _orderNum + " " + apiurl + "（DATA）：" + post_data + "|====|" + apidata + " " + result);


                result_code = get_json_val(result, "resCode");
                result_message = get_json_val(result, "resDesc");
                result_OrderId = get_json_val(result, "data[0][0].order_no");
                break;
            case "10101":
                string xcfans_username = "a2265620";
                string xcfans_pwd = "a2265620";
                _api = "https://xcfans.com/api/dyzb";

                //create Order
                _dic.Add("username", xcfans_username);
                _dic.Add("pwd", xcfans_username);
                _dic.Add("yk_id", _orderUrl);
                _dic.Add("shu", _orderNum);

                apiurl = _api + "/insert.aspx";
                switch (_Type)
                {
                    case "tk-zb1":
                        _Type = "2小时人气";
                        break;
                    case "tk-zb4":
                        _Type = "4小时人气";
                        break;
                    case "tk-zb6":
                        _Type = "6小时人气";
                        break;
                    case "tk-zbday":
                        _Type = "包天人气";
                        break;
                    case "tk-zbdz":
                        _Type = "直播间点赞";
                        break;
                    default:
                        _data["message"] = "type获取失败";
                        return _data;
                        break;
                }
                _dic.Add("yewu_name", _Type);

                post_data = toXml(_dic);

                //if (isLocal)
                //{
                //    post_data = "m=POST&url=" + Uri.EscapeDataString(apiurl) + "&data=" + Uri.EscapeDataString(apidata);
                //    apiurl = localApi;
                //}
                result = GetHtml(apiurl, "", "");
                result = SendRequestC(apiurl, post_data, "");


                log.WriteLog("api_log", _Appid, "create " + _Type + " " + _orderUrl + " " + _orderNum + " " + apiurl + "（DATA）：" + post_data + "|====|" + apidata + " " + result);

                result = xmlToJson(result);

                result_code = get_json_val(result, "error");
                result_code = result_code == "success" ? "0" : "-1";
                result_message = get_json_val(result, "error");
                result_OrderId = get_json_val(result, "sid");
                break;
            default:
                return _data;
                break;
        }


        if (result_code == "0")
        {
            _data["code"] = "1";
            _data.Add("id", result_OrderId);
        }
        else
        {
            _data["message"] = result_message;
        }


        return _data;
    }

    public string get_json_val(string text, string code)
    {
        text = text.Replace("\0", "");
        JsonData jd;
        try
        {
            jd = JsonMapper.ToObject(text);
            string[] arr = code.Split('.');
            for (int i = 0; i < arr.Length; i++)
            {
                int num = -1;
                List<string> arr_list = new List<string>();
                if (arr[i].IndexOf('[') != -1)
                {
                    num = 1;
                    arr_list = getContainerMul(arr[i], "[", "]", false);
                    arr[i] = getContainer("@" + arr[i], "@", "[");
                }

                if (i == arr.Length - 1)
                {
                    if (num == -1)
                    {
                        return jd[arr[i]] + "";
                    }
                    else
                    {
                        for (int a = 0; a < arr_list.Count; a++)
                        {
                            if (a == arr_list.Count - 1)
                            {
                                return jd[Convert.ToInt16(arr_list[a])] + "";
                            }
                            jd = jd[Convert.ToInt16(arr_list[a])];
                        }
                    }
                }

                jd = jd[arr[i]];

                if (num != -1)
                {
                    for (int a = 0; a < arr_list.Count; a++)
                    {
                        jd = jd[Convert.ToInt16(arr_list[a])];
                    }
                }
            }
            return string.Empty;
        }
        catch (Exception)
        {
        }
        return string.Empty;
    }


    public bool OrderUrlCheck(string code, string url, out string id, out string checkmsg, out string newurl)
    {
        bool checkRes = false;
        string temp = string.Empty;
        bool res = false;
        id = string.Empty;
        checkmsg = string.Empty;
        newurl = url;
        JsonData jd;

        List<string> _urls = new List<string>();
        bool check_result = false;

        if (!IsNumeric(url))
        {
            if (url.IndexOf("http") != 0)
            {
                url = "https://" + url;
            }
        }

        if (code.IndexOf("ins-") == 0 || code.IndexOf("fb-") == 0 || code.IndexOf("tw-") == 0 || code.IndexOf("tg-") == 0)
        {
            return true;
        }

        _urls.Add("https://www.iesdouyin.com/");
        _urls.Add("https://www.douyin.com/");
        _urls.Add("https://webcast.amemv.com/");
        _urls.Add("https://v.douyin.com/");
        _urls.Add("https://live.douyin.com/");
        _urls.Add("https://www.youtube.com/");
        _urls.Add("https://www.tiktok.com/");
        _urls.Add("https://tiktok.com/");

        _urls.Add("https://vm.tiktok.com/");
        _urls.Add("https://vt.tiktok.com/");


        //tk链接处理
        if (url.IndexOf("@") == 0 && IsNaturalNumber(url.Replace("@", "")) && code.IndexOf("tk-") == 0)
        {
            url = "https://www.tiktok.com/" + url;
        }


        if (!IsNumeric(url))
        {
            for (int i = 0; i < _urls.Count; i++)
            {
                if (url.IndexOf(_urls[i]) == 0 || url.IndexOf(_urls[i].Replace("https", "http")) == 0)
                {
                    check_result = true;
                    break;
                }
            }
            if (check_result == false)
            {
                checkmsg = "链接不正确（如正常请联系管理）";
                log.WriteLog("order-Exp", "url", url);
                return check_result;
            }
        }

        if (code.IndexOf("dy-") != -1 || code.Replace("2", "").Replace("3", "").IndexOf("dyzb-") != -1)
        {


            string dy_user_id = string.Empty;
            checkmsg = "视频链接不正确";
            if (code.Replace("2", "").Replace("3", "").IndexOf("dyzb") != -1)
            {
                checkmsg = "主页链接不正确";
                checkRes = checkDouyinPersonUrl(code, url, out id, out dy_user_id);
                if (url.IndexOf("/share/user/") == -1 || (code == "dyzb-rc" || code == "dyzb-dz" || code == "dyzb-gzzs"))
                {
                    newurl = id;
                }
                else
                {
                    newurl = getContainer(newurl + "?", "share/user/", "?");
                    string newurl_result = string.Empty;
                    if (newurl.IndexOf("MS") == 0)
                    {
                        newurl = getContent("https://www.iesdouyin.com/web/api/v2/user/info/?sec_uid=" + newurl);
                        try
                        {
                            newurl_result = newurl;
                            jd = JsonMapper.ToObject(newurl);
                            newurl = jd["user_info"]["uid"] + "";
                        }
                        catch (Exception)
                        {

                        }
                    }

                    if (string.IsNullOrEmpty(newurl))
                    {
                        log.WriteLog("order-UrlError", "newurl空", "newurl:" + newurl + ",newurl_result:" + newurl_result);
                    }

                    checkRes = !string.IsNullOrEmpty(newurl);
                }
                id = dy_user_id;
                return checkRes;
            }
            else
            {
                checkRes = checkDouyinUrl(url, code, out id, out dy_user_id);
                if (code.IndexOf("dy-gz") != -1 || code.IndexOf("dy-fs") != -1 || code.IndexOf("dy-gzm") != -1)
                {
                    checkmsg = "视频(或主页)链接不正确";
                    checkRes = checkRes ? checkRes : checkDouyinPersonUrl(code, url, out id, out dy_user_id);
                }
                if (url.IndexOf("/share/video/") != -1 || url.IndexOf("/share/user/") != -1 || url.IndexOf("webcast/reflow/") != -1)
                {
                    newurl = id;
                }
                id = dy_user_id;
                return checkRes;
            }
        } if (code.IndexOf("tk-") != -1)
        {
            res = checkTiktokUrl(url, code, out id);
            newurl = id;
            checkmsg = "主页链接不正确";
        }
        //else if (code.IndexOf("ytb-") != -1)
        //{
        //    checkmsg = "链接不正确";
        //    checkRes = checkYtbUrl(code, url, out id);
        //    newurl = id;
        //    return checkRes;
        //}
        else
        {
            return true;
        }

        return res;
    }


    public static string apiurl
    {
        get
        {
            string _apiurl = "json/";
            if (HttpContext.Current.Request.Url.Host == "localhost")
            {
                _apiurl = "json.aspx?do=";
            }
            return _apiurl;
        }
    }
}