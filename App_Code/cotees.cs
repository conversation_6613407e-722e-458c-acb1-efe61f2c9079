using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using LitJson;
using System.Collections;

/// <summary>
/// cotees 的摘要说明
/// </summary>
namespace com.cotees
{
    public class jsonClass
    {
        private static bool isMysql = false;
        private static bool isDebug = false;
        private static string tableStyle = "paginate_button ";
        public static string queryPager(string dName, List<string> cols, string where, string col, string orderBy, string _page, string _size, string attach = "{}", string groupby = "", string type = "")
        {
            return JsonMapper.ToJson(queryPagerD(dName, cols, where, col, orderBy, _page, _size, attach = "{}", groupby, type));
        }
        public static Dictionary<string, object> queryPagerD(string dName, List<string> cols, string where, string col, string orderBy, string _page, string _size, string attach = "{}", string groupby = "", string type = "", bool showNull = false)
        {
            // step1：初始化参数
            DataTable dt;
            string page = string.Empty;
            string size = string.Empty;
            Int64 total = 0;

            string normalWhere = where;
            string normalDname = dName;

            if (!string.IsNullOrEmpty(groupby))
            {
                if (!string.IsNullOrEmpty(where))
                {
                    where += " group by " + groupby;
                }
                else
                {
                    dName += " group by " + groupby;
                }
            }

            // step3：查询获取并转化为json
            jsonClass.checkPageAndSize(_page, _size, out page, out size);
            dt = jsonClass.getData(dName, where, col, orderBy, page, size, out total, normalWhere, normalDname);
            return jsonClass.toJsonList(dt, cols, total, Convert.ToInt16(page), Convert.ToInt16(size), attach, type, showNull);
        }
        public static void checkPageAndSize(string page, string size, out string p, out string s)
        {
            if (page == "" || !globalClass.IsNumeric(page) || Convert.ToInt64(page) < 0)
            {
                page = "0";
            }

            if (size == "" || !globalClass.IsNumeric(size) || Convert.ToInt64(size) < 1)
            {
                size = "1";
            }
            s = size;
            p = page;
        }
        public static DataTable getData(string name, string where, string col, string ob, string page, string size, out Int64 total, string normalWhere, string normalDname)
        {
            total = 0;
            page = (Convert.ToInt64(page) + 1).ToString();
            //mysql db = new mysql();
            dbClass db = new dbClass();

            name = name.Replace("'", "''");
            col = col.Replace("'", "''");

            string sql = " exec [Common_PageList] '" + normalDname + "','',@where,1,0,'',1 ";
            if (isMysql)
            {
                sql = " CALL Common_PageList('" + normalDname + "','',@where,1,0,'',1); ";
            }
            //log.WriteLog("sql", "-", sql + "_" + normalWhere);
            SqlParameter[] parames = new SqlParameter[]{
                new SqlParameter("@where",normalWhere)
            };
            if (isDebug)
            {
            }

            DataTable dt = db.getDataTable(sql, parames);

            if (dt.Rows.Count > 0)
            {
                total = Convert.ToInt64(dt.Rows[0][0] + "");
            }
            else
            {
                total = 0;
            }

            if (ob == "")
            {
                ob = "createTime desc";
            }
            sql = " exec [Common_PageList] '" + name + "','" + col + "',@where,@index,@pageerCount,'" + ob + "',0  ";
            if (isMysql)
            {
                sql = " CALL Common_PageList('" + name + "','" + col + "',@where,@index,@pageerCount,'" + ob + "',0); ";
            }
            //log.WriteLog("sql", "", sql);
            parames = new SqlParameter[]{
                new SqlParameter("@where",where),
                new SqlParameter("@index",page),
                new SqlParameter("@pageerCount",size)
            };
            if (isDebug)
            {
            }

            return db.getDataTable(sql, parames);
        }

        public static Dictionary<string, object> toJsonList(DataTable dt, List<string> cols, Int64 total, int page, int size, string attach, string type, bool showNull)
        {
            string data = string.Empty;
            string str = string.Empty;
            string colName = string.Empty;
            string colName2 = string.Empty;
            List<object> arr = new List<object>();
            Int64 pager = 0;
            string[] g;
            Dictionary<string, object> _temp = new Dictionary<string, object>();
            Dictionary<string, object> dic = new Dictionary<string, object>();
            List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

            globalClass gc = new globalClass();
            string tempstr = string.Empty;
            int isSub = 0;
            int subnum = 0;

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                _temp = new Dictionary<string, object>();
                for (int c = 0; c < cols.Count; c++)
                {
                    isSub = 0;
                    subnum = 0;
                    arr = new List<object>();

                    if (cols[c].IndexOf(" as ") != -1)
                    {
                        g = cols[c].Split(new string[] { " as " }, StringSplitOptions.None);
                        colName = g[0];
                        colName2 = g[1];
                    }
                    else
                    {
                        colName = cols[c];
                        colName2 = colName;
                    }

                    if (colName.IndexOf("-sub[") != -1)
                    {
                        tempstr = gc.getContainer(colName, "-sub[", "]");
                        colName = colName.Replace("-sub[" + tempstr + "]", "");
                        colName2 = colName;
                        subnum = Convert.ToInt16(tempstr);
                        isSub = 1;
                    }
                    else if (colName.IndexOf("-sub") != -1)
                    {
                        colName = colName.Replace("-sub", "");
                        subnum = 30;
                        isSub = 1;
                    }
                    object obj_value = null;
                    if (colName.IndexOf("-function ") != -1 || colName.IndexOf("@") != -1)
                    {
                        if (colName.IndexOf("@") != -1)
                        {
                            tempstr = gc.getContainer(colName, "@", "(");
                            str = gc.getContainer(colName, "@" + tempstr + "(", ")");
                            colName2 = colName2.Replace("@" + tempstr + "(" + str + ")", "");
                            colName = colName.Replace("@" + tempstr + "(" + str + ")", "");
                        }
                        else
                        {
                            tempstr = gc.getContainer(colName, "-function ", "(");
                            str = gc.getContainer(colName, "-function " + tempstr + "(", ")");
                            colName2 = colName2.Replace("-function " + tempstr + "(" + str + ")", "");
                            colName = colName.Replace("-function " + tempstr + "(" + str + ")", "");
                        }
                        if (!string.IsNullOrEmpty(str))
                        {
                            g = str.Split(',');
                            for (int t = 0; t < g.Length; t++)
                            {
                                try
                                {
                                    arr.Add(dt.Rows[i][g[t]]);
                                }
                                catch (Exception)
                                {
                                    arr.Add(g[t]);
                                }
                            }
                        }
                        else
                        {
                            //空参数（传入自身为参数）
                            arr.Add(dt.Rows[i][colName]);
                        }
                        obj_value = pageFunction(tempstr, arr);
                    }
                    else
                    {
                        obj_value = dt.Rows[i][colName];
                    }
                    if (isSub == 1)
                    {
                        obj_value = globalClass.GetContentSummary(str, subnum, true);
                    }
                    if (obj_value == DBNull.Value)
                    {
                        obj_value = null;
                    }
                    if (obj_value != null && obj_value.GetType() == (new DateTime()).GetType())
                    {
                        obj_value = Convert.ToDateTime(obj_value).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    if (showNull == false)
                    {
                        if (obj_value == null)
                        {
                            obj_value = "";
                        }
                    }
                    if (colName2 == "order_url" || colName2 == "plid" || colName2 == "sUrl")
                    {
                        obj_value = (obj_value + "").Replace("\0", "");
                    }
                    _temp.Add(colName2, obj_value);
                }
                list.Add(_temp);
            }
            if (type == "user")
            {
                getPageHtml(total, page, size, out pager);
                dic.Add("status", 0);
                dic.Add("count", total);
                dic.Add("index", page);
                dic.Add("pager", pager);
                dic.Add("result_list", list);
            }
            else
            {
                dic = new Dictionary<string, object>();
                dic.Add("code", 1);
                dic.Add("msg", "");
                dic.Add("count", dt.Rows.Count);
                dic.Add("data", list);
                dic.Add("total", total);
                dic.Add("page", page);
                dic.Add("pageHtml", getPageHtml(total, page, size, out pager));
                dic.Add("pager", pager);
                dic.Add("limit", size);
                dic.Add("attach", attach);
            }
            return dic;
        }

        public static object pageFunction(string funcitonName, List<object> para)
        {
            object str = string.Empty;
            int tempNum = 0;
            string temp = string.Empty;
            string[] g;
            JsonData jd;
            DataRow[] dr;
            Dictionary<string, object> dic = new Dictionary<string, object>();

            try
            {
                if (para[0] == DBNull.Value || para[0] == null)
                {
                    return null;
                }
                switch (funcitonName)
                {
                    case "split":
                        g = para[0].ToString().Split(',');
                        str = g[Convert.ToInt16(para[1])];
                        break;
                    case "date":
                    case "formatDate":
                        str = chelper.formatDate(para[0].ToString());
                        break;
                    case "time":
                        str = (long)(Convert.ToDateTime(para[0]) - new DateTime(1970, 1, 1, 8, 0, 0, 0)).TotalSeconds;
                        break;
                    default:
                        break;
                }
            }
            catch (Exception)
            {
                str = "";
            }
            return str;
        }


        public static string getPageHtml(Int64 total, int page, int size, out Int64 _pager)
        {
            string _data = string.Empty;
            _pager = 1;
            if (total > 0)
            {
                int pagerCount = 0;

                if (total != 0)
                {
                    Int64 a = total / size;
                    if (total > a * size)
                    {
                        a = a + 1;
                    }
                    pagerCount = (Int16)a;
                    _pager = pagerCount;
                }


                if (page + 1 > pagerCount && page != 0)
                {
                    page = pagerCount - 1;
                }
                if (page < 0)
                {
                    page = 0;
                }

                if (page > 0)
                {
                    //_data += "<li class='" + tableStyle + "'><a onclick='_pt(" + (page - 1).ToString() + ")'>上一页</a></li>";
                    _data += "<li><a onclick='_pt(" + (page - 1).ToString() + ")' class='layui-laypage-prev' data-page='0'><i class='layui-icon'></i></a></li>";
                }
                else
                {
                    //_data += "<li class='" + tableStyle + "disabled'><a>上一页</a></li>";
                    _data += "<li class='disabled'><a href='javascript:;' class='layui-laypage-prev' data-page='0'><i class='layui-icon'></i></a></li>";
                }

                if (page > 1)
                {//第3页开始
                    int startIndex = page - 1;
                    if (page + 1 < pagerCount)
                    {//当前页小于总页
                        _data += "<li class='" + tableStyle + "'><a onclick='_pt(0)'>1</a></li>";
                        if (page > 2)
                        {
                            _data += "<li class='" + tableStyle + "disabled'><span>...</span></li>";
                        }
                    }
                    else
                    {//当前页等于总页，或大于总页
                        if (page > 2 && pagerCount > 6)
                        {
                            _data += "<li class='" + tableStyle + "'><a onclick='_pt(0)'>1</a></li><li class='disabled'><span>...</span></li>";
                        }
                        startIndex = startIndex - (page + 2 - pagerCount);
                        if (startIndex < 0)
                        {
                            startIndex = 0;
                        }
                    }
                    for (int i = startIndex; i < pagerCount && i < page + 2; i++)
                    {
                        if (i == page)
                        {
                            _data += "<li class='" + tableStyle + "active'><a>" + (i + 1).ToString() + "</a></li>";
                        }
                        else
                        {
                            _data += "<li class='" + tableStyle + "'><a onclick='_pt(" + i + ")'>" + (i + 1).ToString() + "</a></li>";
                        }
                    }


                    if (page + 2 >= pagerCount)
                    {
                        if (page + 1 == pagerCount)
                        {
                            //_data += "<li class='" + tableStyle + "'><a>下一页</a></li>";
                            _data += "<li class='disabled'><a href='javascript:;' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                        }
                        else
                        {
                            //_data += "<li class='" + tableStyle + "'><a onclick='_pt(" + (page + 1).ToString() + ")'>下一页</a></li>";
                            _data += "<li><a  onclick='_pt(" + (page + 1).ToString() + ")' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                        }
                    }
                    else
                    {

                        if (page + 2 < pagerCount)
                        {
                            if (page + 3 < pagerCount)
                            {
                                _data += "<li class='" + tableStyle + "disabled'><span>...</span></li>";
                            }
                            _data += "<li class='" + tableStyle + "'><a onclick='_pt(" + (pagerCount - 1).ToString() + ")'>" + pagerCount + "</a></li>";

                            //_data += "<li><a onclick='_pt(" + (page + 1).ToString() + ")'>下一页</a></li>";
                            _data += "<li><a  onclick='_pt(" + (page + 1).ToString() + ")' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                        }
                        else
                        {
                            //_data += "<li class='" + tableStyle + "'><a onclick='_pt(" + (page + 1).ToString() + ")'>下一页</a></li>";
                            _data += "<li class='disabled'><a href='javascript:;' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < pagerCount && i < 3; i++)
                    {
                        if (i == page)
                        {
                            _data += "<li class='" + tableStyle + "active'><a>" + (i + 1).ToString() + "</a></li>";
                        }
                        else
                        {
                            _data += "<li class='" + tableStyle + "'><a onclick='_pt(" + i + ")'>" + (i + 1).ToString() + "</a></li>";
                        }
                    }

                    if (pagerCount <= 3)
                    {
                        if (page + 1 == pagerCount)
                        {
                            //_data += "<li class='" + tableStyle + "disabled'><a>下一页</a></li>";
                            _data += "<li class='disabled'><a href='javascript:;' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                        }
                        else
                        {
                            //_data += "<li class='" + tableStyle + "'><a onclick='_pt(" + (page + 1).ToString() + ")'>下一页</a></li>";
                            _data += "<li><a  onclick='_pt(" + (page + 1).ToString() + ")' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                        }
                    }
                    else
                    {
                        _data += "<li class='" + tableStyle + "disabled'><span>...</span></li><li><a onclick='_pt(" + (pagerCount - 1).ToString() + ")'>" + pagerCount + "</a></li>";

                        //_data += "<li><a onclick='_pt(" + (page + 1).ToString() + ")'>下一页</a></li>";
                        _data += "<li><a  onclick='_pt(" + (page + 1).ToString() + ")' class='layui-laypage-next' data-page='0'><i class='layui-icon'></i></a></li>";
                    }
                }
            }

            return _data;
        }



        #region unicode 字符转义
        /// <summary>  
        /// 转换输入字符串中的任何转义字符。如：Unicode 的中文 \u8be5  
        /// </summary>  
        /// <param name="str"></param>  
        /// <returns></returns>  
        public static string deUnicode(string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return str;
            return Regex.Unescape(str);
        }
        /// <summary>  
        /// 将字符串进行 unicode 编码  
        /// </summary>  
        /// <param name="str"></param>  
        /// <returns></returns>  
        public static string enUnicode(string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return str;
            StringBuilder strResult = new StringBuilder();
            if (!string.IsNullOrEmpty(str))
            {
                for (int i = 0; i < str.Length; i++)
                {
                    strResult.Append("\\u");
                    strResult.Append(((int)str[i]).ToString("x4"));
                }
            }
            return strResult.ToString();
        }
        #endregion

        public static bool sendPhoneCode(string phone, out string errmsg)
        {
            if (phone.Length != 11)
            {
                errmsg = "手机号有误";
                return false;
            }
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            string sql = string.Empty;
            SqlParameter[] parames = new SqlParameter[] { };
            errmsg = "发送验证码时出现错误，请联系客服处理！";

            parames = new SqlParameter[]{
                new SqlParameter("@phone",phone)
            };
            sql = " [sendPhonenum] @phone ";
            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count > 0)
            {
                string temp = dt.Rows[0]["code"] + "";
                if (temp == "1")
                {
                    errmsg = dt.Rows[0]["pcode"] + "";
                    return true;
                }
                errmsg = dt.Rows[0]["msg"] + "";
            }
            return false;

        }
        public static bool checkPhoneCode(string phone, string code, out string errmsg)
        {
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            string sql = string.Empty;
            SqlParameter[] parames = new SqlParameter[] { };
            errmsg = "验证出错";
            if (string.IsNullOrEmpty(code))
            {
                errmsg = "验证码不能为空";
                return false;
            }

            parames = new SqlParameter[]{
                new SqlParameter("@phone",phone),
                new SqlParameter("@code",code)
            };
            sql = " [checkCode] @phone,@code ";
            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count > 0)
            {
                string temp = dt.Rows[0]["code"] + "";
                errmsg = dt.Rows[0]["msg"] + "";
                if (temp == "1")
                {
                    return true;
                }
            }
            return false;

        }

    }
}