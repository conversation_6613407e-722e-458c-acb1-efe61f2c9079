using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class jk_unifyOrder : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        Response.Write(main(""));
        Response.End();
    }

    public string main(string action)
    {
        string str = string.Empty;

        string createUrl = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["servHttpUrl"].ConnectionString;

        string createData =
            "AccessToken=" + req("AccessToken")
            + "&ItemIds=" + req("ItemIds")
            + "&do=" + req("do")
            + "&itemId=" + req("itemId")
            + "&itemUrl=" + req("itemUrl")
            + "&itemNum=" + req("itemNum")
            + "&itemData1=" + req("itemData1")
            + "&itemData2=" + req("itemData2")
            + "&itemOrderId=" + req("itemOrderId")
            + "&rqorder=" + req("rqorder");

        str = _Http(createUrl, createData, "", "");

        return str;
    }

    public string req(string name, bool checkAll = true)
    {
        string res = string.Empty;
        res = Request.Form[name] + "";
        if (checkAll && string.IsNullOrEmpty(res))
        {
            res = Request.QueryString[name] + "";
        }
        return res;
    }


    public static string _Http(string formUrl, string formData, string cookieStr, string header)
    {
        try
        {
            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/x-www-form-urlencoded";
            request.UserAgent = (header == "" ? "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36" : header);
            request.Headers.Add("Cookie", cookieStr);
            request.ContentLength = postData.Length;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            //HttpWebResponse response;
            //Stream responseStream;
            //StreamReader reader;
            //string srcString;
            //response = request.GetResponse() as HttpWebResponse;
            //responseStream = response.GetResponseStream();
            //reader = new System.IO.StreamReader(responseStream, Encoding.UTF8);
            //srcString = reader.ReadToEnd();
            //string result = srcString;   //返回值赋值
            //reader.Close();

            string htmlCode = string.Empty;
            HttpWebResponse response = (System.Net.HttpWebResponse)request.GetResponse();
            //获取目标网站的编码格式
            string contentype = response.Headers["Content-Type"];
            Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);
            if (response.ContentEncoding.ToLower() == "gzip")
            {
                using (System.IO.Stream streamReceive = response.GetResponseStream())
                {
                    using (var zipStream = new System.IO.Compression.GZipStream(streamReceive, System.IO.Compression.CompressionMode.Decompress))
                    {
                        //匹配编码格式
                        if (regex.IsMatch(contentype))
                        {
                            Encoding ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, ending))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                        else
                        {
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, Encoding.UTF8))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            else
            {
                using (System.IO.Stream streamReceive = response.GetResponseStream())
                {
                    //using (System.IO.StreamReader sr = new System.IO.StreamReader(streamReceive, Encoding.UTF8))
                    //{
                    //    htmlCode = sr.ReadToEnd();
                    //}
                    if (regex.IsMatch(contentype))
                    {
                        Encoding ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                        using (StreamReader sr = new System.IO.StreamReader(streamReceive, ending))
                        {
                            htmlCode = sr.ReadToEnd();
                        }
                    }
                    else
                    {
                        using (StreamReader sr = new System.IO.StreamReader(streamReceive, Encoding.UTF8))
                        {
                            htmlCode = sr.ReadToEnd();
                        }
                    }
                }
            }

            return htmlCode;
        }
        catch (Exception ex)
        {
            string a = ex.ToString();
            return a;
        }
    }
}