.pagination ul {
    list-style: none;
    padding-left: 0;
}

.pagination ul li {
    padding: 0 10px;
    vertical-align: top;
    display: inline-block;
    font-size: 14px;
    min-width: 36px;
    min-height: 28px;
    line-height: 28px;
    cursor: pointer;
    box-sizing: border-box;
    text-align: center;
    background-color: #ffffff;
    color: #606266;
    border-radius: 6px;
    margin: 0 1px;
    border: 1px solid #ebebeb;
    height: 30px;
}

.pagination ul li:hover {
    transform: scale(1.1);
    background-color: #F4F6F8;
}

.pagination  li.active {
    background: #98A6AD;
    color: white;
    cursor: not-allowed;
}

.pagination li.disabled {
    cursor: not-allowed;
}
.pagination li.totalPage {
    background: transparent;
    cursor: default;
    border: none;
    padding: 0 6px;
}

.pagination li.totalPage:hover {
    transform: none;
    background-color: #ffffff;
}


.pagination li input {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    outline: none;
    padding: 3px 5px;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1);
    width: 40px;
    height: 25px;
    margin: 0 6px;
}

.pagination li input:focus{
    border-color: #98A6AD;
}
.pagination{
    user-select: none;
}

.pagination ul:nth-child(2){
    border-radius: 6px;
}

input[type=number] {
    -moz-appearance:textfield;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
