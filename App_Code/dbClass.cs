using System;
using System.Data;
using System.Data.SqlClient;
using System.Web;
public class dbClass
{
    public dbClass()    {    }
    private SqlConnection Cn;
    private SqlCommand cmd;
    private DataSet ds;
    #region 取得链接
    private void Open()
    {
        string s = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnectionSql"].ConnectionString;
        Cn = new SqlConnection(s);
        Cn.Open();
    }
    #endregion

    #region 关闭连接
    private void Close()
    {
        if (Cn != null)
        {
            Cn.Close();
            Cn.Dispose();
        }
    }
    #endregion

    #region 执行简单的SQL语句
    public int ExecuteNonQuery(string Sql)
    {
        int res = 0;
        try
        {
            Open();
            cmd = new SqlCommand(Sql, Cn);
            res = cmd.ExecuteNonQuery();
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return res;
    }
    #endregion

    #region 执行简单的SQL语句（带参数）
    public int ExecuteNonQuery(string Sql, SqlParameter[] parames)
    {
        int res = 0;
        try
        {
            Open();
            cmd = new SqlCommand(Sql, Cn);
            cmd.Parameters.AddRange(parames);
            res = cmd.ExecuteNonQuery();
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return res;
    }
    #endregion

    #region 返回首列单行的SQL语句
    public string ExecuteScalar(string Sql)
    {
        string Returnstring = "";
        try
        {
            Open();
            cmd = new SqlCommand(Sql, Cn);
            Returnstring = cmd.ExecuteScalar().ToString();
        }
        catch
        {
        }
        Close();
        return Returnstring;
    }
    #endregion

    #region 返回SQL语句的DateSet数据集
    public DataSet getDataSet(string SQL)
    {
        try
        {
            Open();
            SqlDataAdapter myAdapter = new SqlDataAdapter(SQL, Cn);
            ds = new DataSet();
            myAdapter.Fill(ds);
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return ds;
    }
    #endregion

    public int bulkInsert(DataTable dt, string targetTable)
    {
        string s = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnectionSql"].ConnectionString;
        SqlBulkCopy bulkCopy = new SqlBulkCopy(s);
        try
        {
            bulkCopy.DestinationTableName = targetTable;
            bulkCopy.BatchSize = dt.Rows.Count;
            if (dt != null && dt.Rows.Count != 0)
            {
                bulkCopy.WriteToServer(dt);
            }
        }
        catch (Exception)
        {
            //throw;
            bulkCopy.Close();
            return 0;
        }
        bulkCopy.Close();
        return dt.Rows.Count;
    }

    #region 返回SQL语句的DateSet数据集（带参数）
    public DataSet getDataSet(string SQL, SqlParameter[] parames)
    {
        try
        {
            Open();
            SqlDataAdapter myAdapter = new SqlDataAdapter(SQL, Cn);
            foreach (SqlParameter ps in parames)
            {
                myAdapter.SelectCommand.Parameters.Add(ps);
            }
            ds = new DataSet();
            myAdapter.Fill(ds);
        }
        catch
        {
            Close();
            throw;
        }
        Close();
        return ds;
    }
    #endregion

    #region 返回一个DataTable对象
    public DataTable getDataTable(string SQL)
    {
        DataSet ds = getDataSet(SQL);
        DataTable dt = new DataTable();
        if (ds.Tables.Count > 0)
        {
            dt = ds.Tables[0];
        }
        return dt;
    }
    #endregion

    #region 返回一个DataTable对象(带参数)
    public DataTable getDataTable(string SQL, SqlParameter[] parames)
    {
        DataSet ds = getDataSet(SQL, parames);
        DataTable dt = new DataTable();
        if (ds.Tables.Count>0)
        {
            dt = ds.Tables[0];
        }
        return dt;
    }
    #endregion

}