using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;

/// <summary>
/// x_helper 的摘要说明
/// </summary>
public class x_helper
{
    public x_helper()
    {
        //
        // TODO: 在此处添加构造函数逻辑
        //
    }
}

namespace x.Encry
{
    public class rsa
    {
        string public_key = @"<RSAKeyValue>
  <Exponent>AQAB</Exponent>
  <Modulus>zLACUeoGfaZVPQXe2zRA5+o5CqMdfCpInjsLSHnEX9DGcPmkBs1lkmpBF8xzD+80nZsRR4Dnb/P6vHY2vIuWNebAmELqd9gWLgCqMH2NjFa4/K+uMtG0paZ2gPiXDfXGoeEN53tenC+Q5l20xibOLN2yxnKJehF/ydD/BXe8Ol8=</Modulus>
</RSAKeyValue>";
        string private_key = @"<RSAKeyValue>
  <D>Kz6MXu4IjQdVPt08pic2B0lO39ZyZkcwrG1hBuDjAjNDmZ7EbsN6UCKuue5x6M3q2dlnHyo2u0pakPKQwejjxQKm4/Y942Pues1gaoAJG5DJpSx1jPyvh0dLCBqOtNXXWtiTFzKZ+WH0E4TvEbGDPJGdpJVrL8hR6ks/TT1EQgE=</D>
  <DP>AyyQlHO4jMT4iFq2vTS9liuX09TSSzkcm1sE1bncatDkuGkTndI0f11y37xQfiU5ielkBNzTQV6H2H1cuwEoDQ==</DP>
  <DQ>mRn7dmwWswID+R/cvwlOrHfD2gSksZQVg4vuUQMTo6hahDrK2wnn9u5sPuGnZUiAmubDtlG+t6+bUvJptcrSgQ==</DQ>
  <Exponent>AQAB</Exponent>
  <InverseQ>3RvhLFeXyxk9SANqOPozhObLsckwdDB8Cjldaa897F4pWti4ERnSzQqTD5OF91Z4yTQHInSfTEFcILa3MpGIAw==</InverseQ>
  <Modulus>zLACUeoGfaZVPQXe2zRA5+o5CqMdfCpInjsLSHnEX9DGcPmkBs1lkmpBF8xzD+80nZsRR4Dnb/P6vHY2vIuWNebAmELqd9gWLgCqMH2NjFa4/K+uMtG0paZ2gPiXDfXGoeEN53tenC+Q5l20xibOLN2yxnKJehF/ydD/BXe8Ol8=</Modulus>
  <P>8nKAAUiBCpknLurX/qa521q0Cu9UJzUPDs6HFbCtQW9zjyf9SL+lw7asNqhZDQs+ZlG7c2wLZWGhbF6mAJAg3w==</P>
  <Q>2CEnp8TqTrFM29HpOFFKLqc0mXQvIE0mkf4lfb/lCg1vCHXpgDdsh+3MYfGkcjup7hM8tmuLhkH6cdXMIkeWgQ==</Q>
</RSAKeyValue>";
//        string public_key = @"<RSAKeyValue>
//  <Exponent>AQAB</Exponent>
//  <Modulus>sWwpdeBVgY8yLnWBXqi+Djdp9D6NFrvOaB/4eElc9483yJLjhEK4anvWjZunSDbSqQT7wfoJglmoiKDi77Vr/kuGuFTs4wFLvzycvaiMiO1pIZmnmdYNnU0XSjMXWy1BS7JglrX+/uhvs5QAfUhWLbY2EUAgS+i1tRjW3JGBGj8=</Modulus>
//</RSAKeyValue>";
//        string private_key = @"<RSAKeyValue>
//  <D>XHAPFeQgiJTzvzLniUr/YmbKpD9mwN5uht+JVBpc/no2xSCKu2ELtib5PORI/p3ZmcgH5yN/itNttGmmct+h+IfvroI1y+9Gs7aRy9XI23/RNePJFvw/hR3OCHHpSW/TMXwGxnKizMdRC5zXgbJl6pvqiK/KjjPLASe4YQTHwYE=</D>
//  <DP>3j3wzzbjgFlhr+Dh9Gfmk4VA6A2X02lPvnZHFcx5/dfxYtObF/6D6viA7ymk+yt1ssjMhQr1NLGwIzAO2GLSAQ==</DP>
//  <DQ>UidG/2Kyv8uYaTmcFdflzX47swA7TyMjNrSC5tWYsbdefB18s9SFUy7qEUaUMsYt1DsJ3pL//l2hJtOJozsi9w==</DQ>
//  <Exponent>AQAB</Exponent>
//  <InverseQ>dvRwS5k/6nf13X3rH28Clsk89NUgLsTqo6PgeiVeaCjbFgn8UP94wQM7TAwecYdgyi4KAjbuC81CJSWi5k8tZQ==</InverseQ>
//  <Modulus>sWwpdeBVgY8yLnWBXqi+Djdp9D6NFrvOaB/4eElc9483yJLjhEK4anvWjZunSDbSqQT7wfoJglmoiKDi77Vr/kuGuFTs4wFLvzycvaiMiO1pIZmnmdYNnU0XSjMXWy1BS7JglrX+/uhvs5QAfUhWLbY2EUAgS+i1tRjW3JGBGj8=</Modulus>
//  <P>5YBVw0xlCP2nCTUXi93nARzVz9jCRWK6OxQw+G5MhavWBw4X/cejvsy0vpD5iHkEWXJ1tpVCUGF5xn7ECywXQQ==</P>
//  <Q>xeh3iFIAFHXtsh9sS4GGmtVC7SarM0K62Bu34BX6NzzM1TGIX4rMqlyM0ETbkVKdypP23l4HyRkq1FDRV8ZRfw==</Q>
//</RSAKeyValue>";


        public static string RsaEncrypt(string rawInput, string publicKey = "")
        {
            if (string.IsNullOrEmpty(publicKey))
            {
                rsa r = new rsa();
                publicKey = r.public_key;
            }

            if (string.IsNullOrEmpty(rawInput))
            {
                return string.Empty;
            }

            if (string.IsNullOrWhiteSpace(publicKey))
            {
                throw new ArgumentException("Invalid Public Key");
            }

            using (var rsaProvider = new RSACryptoServiceProvider())
            {
                var inputBytes = Encoding.UTF8.GetBytes(rawInput);//有含义的字符串转化为字节流
                rsaProvider.FromXmlString(publicKey);//载入公钥
                int bufferSize = (rsaProvider.KeySize / 8) - 11;//单块最大长度
                var buffer = new byte[bufferSize];
                using (MemoryStream inputStream = new MemoryStream(inputBytes),
                     outputStream = new MemoryStream())
                {
                    while (true)
                    { //分段加密
                        int readSize = inputStream.Read(buffer, 0, bufferSize);
                        if (readSize <= 0)
                        {
                            break;
                        }

                        var temp = new byte[readSize];
                        Array.Copy(buffer, 0, temp, 0, readSize);
                        var encryptedBytes = rsaProvider.Encrypt(temp, false);
                        outputStream.Write(encryptedBytes, 0, encryptedBytes.Length);
                    }
                    return Convert.ToBase64String(outputStream.ToArray());//转化为字节流方便传输
                }
            }
        }

        public static string RsaDecrypt(string encryptedInput, string privateKey = "")
        {
            if (string.IsNullOrEmpty(privateKey))
            {
                rsa r = new rsa();
                privateKey = r.private_key;
            }
            if (string.IsNullOrEmpty(encryptedInput))
            {
                return string.Empty;
            }

            if (string.IsNullOrWhiteSpace(privateKey))
            {
                throw new ArgumentException("Invalid Private Key");
            }

            using (var rsaProvider = new RSACryptoServiceProvider())
            {
                var inputBytes = Convert.FromBase64String(encryptedInput);
                rsaProvider.FromXmlString(privateKey);
                int bufferSize = rsaProvider.KeySize / 8;
                var buffer = new byte[bufferSize];
                using (MemoryStream inputStream = new MemoryStream(inputBytes),
                     outputStream = new MemoryStream())
                {
                    while (true)
                    {
                        int readSize = inputStream.Read(buffer, 0, bufferSize);
                        if (readSize <= 0)
                        {
                            break;
                        }

                        var temp = new byte[readSize];
                        Array.Copy(buffer, 0, temp, 0, readSize);
                        var rawBytes = rsaProvider.Decrypt(temp, false);
                        outputStream.Write(rawBytes, 0, rawBytes.Length);
                    }
                    return Encoding.UTF8.GetString(outputStream.ToArray());
                }
            }
        }
    }

    public class aes
    {
        public static string AESEncrypt(string text, string password, string iv)
        {
            RijndaelManaged rijndaelCipher = new RijndaelManaged();
            rijndaelCipher.Mode = CipherMode.CBC;
            rijndaelCipher.Padding = PaddingMode.PKCS7;
            rijndaelCipher.KeySize = 128;
            rijndaelCipher.BlockSize = 128;
            byte[] pwdBytes = System.Text.Encoding.UTF8.GetBytes(password);
            byte[] keyBytes = new byte[16];
            int len = pwdBytes.Length;
            if (len > keyBytes.Length) len = keyBytes.Length;
            System.Array.Copy(pwdBytes, keyBytes, len);
            rijndaelCipher.Key = keyBytes;
            byte[] ivBytes = System.Text.Encoding.UTF8.GetBytes(iv);
            rijndaelCipher.IV = ivBytes;
            ICryptoTransform transform = rijndaelCipher.CreateEncryptor();
            byte[] plainText = Encoding.UTF8.GetBytes(text);
            byte[] cipherBytes = transform.TransformFinalBlock(plainText, 0, plainText.Length);
            return Convert.ToBase64String(cipherBytes);
        }
        public static string AESDecrypt(string text, string password, string iv)
        {
            RijndaelManaged rijndaelCipher = new RijndaelManaged();
            rijndaelCipher.Mode = CipherMode.CBC;
            rijndaelCipher.Padding = PaddingMode.PKCS7;
            rijndaelCipher.KeySize = 128;
            rijndaelCipher.BlockSize = 128;
            byte[] encryptedData = Convert.FromBase64String(text);
            byte[] pwdBytes = System.Text.Encoding.UTF8.GetBytes(password);
            byte[] keyBytes = new byte[16];
            int len = pwdBytes.Length;
            if (len > keyBytes.Length) len = keyBytes.Length;
            System.Array.Copy(pwdBytes, keyBytes, len);
            rijndaelCipher.Key = keyBytes;
            byte[] ivBytes = System.Text.Encoding.UTF8.GetBytes(iv);
            rijndaelCipher.IV = ivBytes;
            ICryptoTransform transform = rijndaelCipher.CreateDecryptor();
            byte[] plainText = transform.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
            return Encoding.UTF8.GetString(plainText);
        }
    }
}