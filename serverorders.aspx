<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="serverorders.aspx.cs" Inherits="serverorders" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    我的订单 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <style>
        .abctext {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .mw-50{
            max-width:50px;
        }
        .mw-100{
            max-width:100px;
        }
        .mw-150{
            max-width:150px;
        }
        .mw-200{
            max-width:200px;
        }
        .mw-250{
            max-width:250px;
        }
    </style>
    <div class="" style="margin-bottom: 0px;">
        <div style="padding: 20px 10px;">


            <div style="margin-bottom: 20px;">

                <label class="search_wrapper" style="width: 100%;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#999" class="ZDI ZDI--Search24 css-15ro776">
                        <g fill-rule="evenodd" clip-rule="evenodd">
                            <path d="M11.5 18.389c3.875 0 7-3.118 7-6.945 0-3.826-3.125-6.944-7-6.944s-7 3.118-7 6.944 3.125 6.945 7 6.945Zm0 1.5c4.694 0 8.5-3.78 8.5-8.445C20 6.781 16.194 3 11.5 3S3 6.78 3 11.444c0 4.664 3.806 8.445 8.5 8.445Z"></path>
                            <path d="M16.47 16.97a.75.75 0 0 1 1.06 0l3.5 3.5a.75.75 0 1 1-1.06 1.06l-3.5-3.5a.75.75 0 0 1 0-1.06Z"></path>
                        </g></svg>
                    <input placeholder="输入搜索的内容" value="" id="search_text">



                    <a style="width: 50px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 13px; background: #7bf37a; color: #333; border-radius: 4px; padding: 3px 8px;" onclick="touch();">搜索
                    </a>
                </label>

            </div>


            <div style="padding: 5px;">
                <input type="checkbox" class="record_orders" />&nbsp;查看7天前的订单
            </div>

            <div style="overflow-y: auto;">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th class="table-cell">ID</th>
                            <th>订单类型</th>
                            <th>订单链接</th>
                            <th>订单备注</th>
                            <th>操作状态</th>
                            <th>订单点数</th>
                            <th>订单数量</th>
                            <th>初始量</th>
                            <th>当前量</th>
                            <th>关闭订单</th>
                            <th>下单时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="list">
                    </tbody>
                </table>
            </div>

            <div id="example" style="text-align: center">
                <ul id="pageLimit"></ul>
            </div>


        </div>
    </div>

    <script>
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }

        function request(paras, url) {
            var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
            var paraObj = {}
            for (i = 0; j = paraString[i]; i++) {
                paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = j.substring(j.indexOf("=") + 1, j.length);
            }
            var returnValue = paraObj[paras.toLowerCase()];
            if (typeof (returnValue) == "undefined") {
                return "";
            } else {
                return returnValue;
            }
        }


        var clearTableData = function () {
            $('#list').html('');
        }
        var pushTableData = function (data) {
            var tbody = $('#list');
            var tr = $('<tr></tr>');
            tbody.append(tr);

            tr.append("<td class='table-cell'>" + data.taskid + "</td>");
            tr.append("<td class='abctext mw-100' title='" + data.type +"'>" + data.type + (data.otherinfo != "" ? '（' + data.otherinfo + '）' : "") + "</td>");
            tr.append("<td class='link_td abctext mw-150' title='" + data.taskurl + "' style='background: #eef3f1;'>" + cJumpUrl(data.taskurl) + "<p style='color:#999;font-size: 12px;'>" + cJumpUrl(data.order_url, '#999'));
            //+ (data.remark1 != "" ? '<p style="margin-top: 12px;"><img src="../static/images/warm99.png" style="width: 12px;"><span style="font-size: 12px;color: #e2ac45;    cursor: pointer;"> ' + data.remark1 + '</span>' + "</p></td>" : "")
            tr.append("<td>" + data.param + "</td>");
            tr.append("<td>" + data.order_status + "</td>");
            tr.append("<td>" + data.total_fee + "</td>");
            tr.append("<td>" + data.order_count + "</td>");
            tr.append("<td>" + data.order_start + "</td>");
            tr.append("<td>" + data.order_current + "</td>");
            tr.append("<td>" + data.state_text + "</td>");
            tr.append("<td>" + data.time + "</td>");
            tr.append('<td style="background: #f0f6fb;">'
                + (data.allow_refund == 1 && data.close_not_refund == 1 && data.state_text == "" ? '<a class="gb_9d gb_error gbs btn-close" data-id="' + data.taskid + '" style="    min-width: 62px;">关闭订单</a>' : "")
                + (data.refund_order == 1 ? '<a class="gb_9d gbs btn-refund" data-id="' + data.taskid + '" style="    min-width: 62px;">取消订单</a>' : "")
                //+ (data.replace_people == 1 ? '<a class="gb_9d gbs btn-replace" data-id="' + data.taskid + '">切换观众</a>' : "")
                //+ (data.switch_name != '' ? '<a class="gb_9d gbs btn-switch" data-id="' + data.taskid + '">' + data.switch_name + '</a>' : "")
                + '</td>');
            if (request('agent', location.href) == "1") {
                tr.append("<td>" + (data.market_status == "1" ? "<span style='color:green;'>已结算 " + data.market_money + "</span>" : "<span style='color:#ccc;'>未结算 " + data.market_money + "</span>") + "</td>");
            }
        }

        var touch_data = {
            data: {
                table: 'orders',
                num: 10,
                index: 0,
                agent: request('agent', location.href)
            }
        }
        var touch = function () {
            touch_data.data.search_text = $("#search_text").val();
            touch_data.data.record_orders = checkIsSelect($(".record_orders"));
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '<%=unified.apiurl%>list',
                data: touch_data.data,
                success: function (result) {
                    if (result.pager < 0) {
                        result.pager = 100
                    }
                    $('#pageLimit').bootstrapPaginator({
                        currentPage: result.index + 1,
                        totalPages: result.pager,
                        size: "normal",
                        bootstrapMajorVersion: 3,
                        alignment: "right",
                        numberOfPages: 5,
                        itemTexts: function (type, page, current) {
                            switch (type) {
                                case "first": return "首页";
                                case "prev": return "上一页";
                                case "next": return "下一页";
                                case "last": return "末页";
                                case "page": return page;
                            }//默认显示的是第一页。
                        },
                        onPageClicked: function (event, originalEvent, type, page) {//给每个页眉绑定一个事件，其实就是ajax请求，其中page变量为当前点击的页上的数字。
                            touch_data.data.index = page - 1;
                            touch();
                        }
                    });



                    clearTableData();

                    if (result.status == 0) {

                        for (var i = 0; i < result.result_list.length; i++) {

                            pushTableData(result.result_list[i]);

                        }

                    }

                    function operator(type, order_id) {
                        $.ajax({
                            type: 'post', dataType: "json", url: '<%=unified.apiurl%>operator', data: { type: type, order_id: order_id }, success: function (result) {
                                if (result.info) {
                                    alert(result.info);
                                } else if (result.msg) {
                                    alert(result.msg);
                                }
                                touch();
                            }
                        });
                    }

                    $('.btn-close').unbind('click').on('click', function () {
                        if (confirm("是否确认关闭订单？")) {
                            operator('close_order', $(this).data("id"));
                        }
                    })
                    $('.btn-refund').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('cancel_order', $(this).data("id"));
                        }
                    })

                    $('.btn-replace').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('replace_people', $(this).data("id"));
                        }
                    })

                    $('.btn-switch').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('switch_order', $(this).data("id"));
                        }
                    })

                    $('.btn-repair').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('repair_order', $(this).data("id"));
                        }
                    })
                }
            });
        }
        touch();
    </script>

    <script>
        var cJumpUrl = function (url, color) {
            return url.indexOf("http") != -1 ? "<a target='_blank' href='" + url + "' style='color:" + color + "'>" + url + "</a>" : url;
        }
    </script>
</asp:Content>

