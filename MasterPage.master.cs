using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class MasterPage : System.Web.UI.MasterPage
{
    public DataTable dt = new DataTable();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            dbClass db = new dbClass();
            string sql = string.Empty;
            List<SqlParameter> pams = new List<SqlParameter>();

            pams.Add(new SqlParameter("@userid", uConfig.p_uid));
            sql = " select * from users where id=@userid ";
            dt = db.getDataTable(sql, pams.ToArray());
        }
    }
}
