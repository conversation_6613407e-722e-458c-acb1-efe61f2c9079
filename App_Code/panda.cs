using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using LitJson;
using System.Text.RegularExpressions;

/// <summary>
/// changba 的摘要说明
/// </summary>
public class panda : globalClass
{
    public panda()
    {

    }

    public string getDyCommList(string vid, string page, string comment_id, string count, out Dictionary<string, object> data)
    {
        string str = string.Empty;
        string url = string.Empty;
        string res = string.Empty;
        string cid = string.Empty;
        string imgurl = string.Empty;
        string uid = string.Empty;
        string nickname = string.Empty;
        string text = string.Empty;
        string digg_count = string.Empty;
        string reply_comment_total = string.Empty;
        int comment_type = 0;
        var reply_html = "";

        string server_api_url = "http://139.9.206.186:8333/";

        data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        JsonData jd;
        string proxy = "&proxy=1";

        //proxy = string.Empty;//关闭代理

        Int64 page_index = Convert.ToInt64(page);

        page = (Convert.ToInt64(page) - 1).ToString();
        page = (Convert.ToInt64(page) * 20).ToString();

        url = server_api_url + "douyin_comment_list?token=dyfuck&aweme_id=" + vid + "&cursor=" + page + proxy;
        if (!string.IsNullOrEmpty(comment_id))
        {
            if (!IsNumeric(comment_id))
            {
                try
                {
                    comment_id = encryUtility.AESDecrypt(comment_id);
                }
                catch (Exception)
                {
                }
            }
            comment_type = 1;
            url = server_api_url + "douyin_comment_reply_list?token=dyfuck&aweme_id=" + vid + "&cursor=" + page + "&comment_id=" + comment_id + "&count=" + count + proxy;
        }
        //log.WriteLog("test", "pldz_url", getUserIP(HttpContext.Current) + "," + url);
        try
        {
            str = getContent(url);
            //log.WriteLog("dy_plid", url, str);
            jd = JsonMapper.ToObject(str);

            for (int i = 0; i < (jd["comments"].Count); i++)
            {
                cid = jd["comments"][i]["cid"] + "";
                try
                {
                    cid = encryUtility.AESEncrypt(cid);
                }
                catch (Exception)
                {
                }
                if (!string.IsNullOrEmpty(cid))
                {
                    imgurl = jd["comments"][i]["user"]["avatar_thumb"]["url_list"][0] + "";
                    uid = jd["comments"][i]["user"]["uid"] + "";
                    nickname = jd["comments"][i]["user"]["nickname"] + "";
                    text = jd["comments"][i]["text"] + "";
                    digg_count = jd["comments"][i]["digg_count"] + "";

                    reply_comment_total = "-1";
                    if (comment_type == 0)
                    {
                        reply_comment_total = jd["comments"][i]["reply_comment_total"] + "";
                        reply_html = "&nbsp;&nbsp;<span style='color: midnightblue;text-decoration: underline;'>" + reply_comment_total + "回复</span>";
                    }

                    res += "<a onclick=\"pl_click('" + cid + "'," + reply_comment_total + ",this)\"><span class='comment_content'><img src='" + imgurl + "' width=20 height=20> @" + nickname + ":" + text + "</span>&nbsp;&nbsp;<span style='color: midnightblue;text-decoration: underline;'>" + digg_count + "赞</span>" + reply_html + "</a>";
                    dic = new Dictionary<string, object>();
                    dic.Add("cid", cid);
                    dic.Add("uid", uid);
                    dic.Add("nickname", nickname);
                    dic.Add("text", text);
                    dic.Add("digg_count", digg_count);
                    dic.Add("reply_comment_total", reply_comment_total);
                    dic.Add("avatar_thumb", imgurl);
                    list.Add(dic);
                }
            }

            Int64 count_total = Convert.ToInt64(jd["total"] + "");

            data.Add("comments", list);
            data.Add("count", count_total);
            data.Add("index", page_index);

            Int64 a = count_total / 20;
            if (count_total > a * 20)
            {
                a = a + 1;
            }
            data.Add("pager", a);

        }
        catch (Exception ex)
        {
            log.WriteLog("dyapi", "dy_plidApi_error", "url=" + url + ",err:" + ex.Message.ToString());
            return "";
        }

        return res;

    }


    public string dyPhotoIDToUserID(string photo_id)
    {
        string str = string.Empty;
        string url = string.Empty;
        url = "http://" + uConfig._static_dyApiurl + "/douyin_uid?token=dyfuck&aweme_id=" + photo_id;
        str = getContent(url, 10000);
        return str;
    }
}