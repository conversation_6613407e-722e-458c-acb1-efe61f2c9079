.none {
    display:none;
}

html,body,p,ul {
    margin:0px;
    padding:0px;
    font-size:14px;
    /*font-family:'Microsoft YaHei';*/
    /*font-family:FangSong;*/
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.noSelectText {
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
}

h1, h2, h3 {
    font-size: 16px;
    font-weight: 600;
}

/*=> ctTable*/

.ctTable {
    padding:0px;border-collapse:collapse;border-top:1px solid #DCDCDC;font: 12px/1.5 "Lucida Grande",Helvetica,Arial,sans-serif;width:100%;border:1px solid #e2e2e2;margin-top:-1px;margin-top:10px;box-shadow: 0 0 1px #0a0f1c;BORDER-RADIUS: 4px;
}

.ctTable th {
    color:#555;font-weight:100;background:#f1f1f1;padding:13px 10px;text-align:center;font-weight:bold;
    /*border:1px solid #e2e2e2;*/
}

.ctTable td {
    color:#444;padding:10px;text-align:center;border:1px solid #e2e2e2;
}

.ctTable a{
    color:#800;text-decoration:none;cursor:pointer;outline:none;text-decoration:underline;
}
.ctTable a:hover{
    color:#201;
}

.ctTable a:not(:last-child){
    margin-right:5px;
}

.ctTable tr {
    transition:all 0.5s;
}


.ctTable tr:hover {
    background:#f6f6f6;
}

.pagerLine .tgPg {
    border:1px solid #eee;padding:5px 10px;display:inline-block;cursor:pointer;transition:all 0.2s;color:#666;
}

.pagerLine .tgPg:not(:first-child){
    margin-left:5px;
}

.pagerLine .tgPg.ban {
    color:#bbb;cursor:default;display:inline-block;
}

.pagerLine .ellipsis {
    display:inline-block;font-weight:700;margin:0px 3px;font-size:14px;
}

.pagerLine .tgPg:not(.ban):hover {
    border:1px solid #f40;color:#f40;
}

.pagerLine .tgPgNumGroup {
    display:inline-block;font-size:0px;margin:0px 5px;
}

.pagerLine .tgPgNumGroup a{
    border:1px solid #eee;padding:4px 15px;display:inline-block;cursor:pointer;text-decoration:none;font-size:16px;color:#666;transition:all 0.3s;
}

.pagerLine .tgPgNumGroup a:hover{
    border:1px solid #f40;color:#f40;
}

.pagerLine .tgPgNumGroup a.act{
    border:1px solid #f40;background:#f40;color:#fff;
}


.modal .close{width: 36px; height: 36px; position:absolute;top: -10px; right: -10px; background: url(../images/close.png) no-repeat;background-size: contain; cursor: pointer; opacity: 1;z-index: 10000;}

.col-3 {
    display: inline-block;
    width: 33.3333%;
    float: left;
    box-sizing: border-box;
    text-align:center;
    cursor:pointer;
}

.cotees-input {
    padding:3px 8px;border:1px solid #f1f1f1;margin:12px 0;border-radius: 10rem;background:#fff;font-size:16px;color:#646464;
}

.cotees-select {
    border:1px solid #f1f1f1;border-radius: 10rem;border-top-right-radius:0px;border-bottom-right-radius:0px;
}

.cotees-input span{
    background:#fff;border:0px;padding-right:3px;
}

.cotees-input input{
    border:0px!important;padding: 6px 12px;width:100%;outline:none;
}

.cotees-btn-blue {
    background-color: #389eff;color: #fff!important;border-radius: 10rem;padding:12px;width:100%;display:inline-block;text-align:center;cursor:pointer;
}

.cotees-btn-origin {
    background-color: #F06000;color: #fff!important;border-radius: 10rem;padding:12px;width:100%;display:inline-block;text-align:center;cursor:pointer;
}

.cotees-btn-green {
    background-color: #06c1ae;color: #fff!important;border-radius: 10rem;padding:12px;width:100%;display:inline-block;text-align:center;cursor:pointer;
}

.cotees-list-info{
    background:#fff;padding:8px;margin:10px 0;color:#666;cursor:pointer;
}
        
.cotees-list-info .list1{
    float:left;
}
        
.cotees-list-info .list1 img{
    width:18px;display:inline-block;position:relative;top:-2px;
}
        
.cotees-list-info .list2{
    float:right;
}

.cotees-list-info .list2 img{
    width:10px;display:inline-block;position:relative;top:-2px;
}
        
.cotees-list-info .list2 span{
    color:#888;
}

.layui-input {
    height: 38px;
    line-height: 1.3;
    line-height: 38px\9;
    border-width: 1px;
    border-style: solid;
    background-color: #fff;
    border-radius: 2px;
        display: block;
    width: 100%;
    padding-left: 10px;
    outline:none;
    border-color: #e6e6e6;
}

.layui-input:hover {
    border-color: #D2D2D2!important;
}

.clist,.reply_comments {
    max-height: 200px;
    overflow-y: auto;
    margin:6px 0;
}

.clist a,.reply_comments a {
    display: block;
    background: #fbfbfb;
    border-bottom: 1px dashed #ddd;
    padding: 6px 0;
    font-size: 12px;
    color: #222;
    transition: all 0.1s;
    padding-left: 8px;
}

.clist a:hover,.reply_comments a:hover {
    background-color: rgb(0, 179, 161);
    color: #fff;
}

.clist a.on,.reply_comments a.on {
    background-color: rgb(0, 179, 161);
    color: #fff;
}

.layui-input{height:38px;line-height:1.3;line-height:38px\9;border-width:1px;border-style:solid;background-color:#fff;border-radius:2px;display:block;width:100%;padding:0 6px;outline:0;border-color:#e6e6e6;color:#000;}
.layui-input:hover{border-color:#D2D2D2!important}

.input-group-addon {
	background-color: #fbfbfb;
	border: 1px solid #eee;
}

.btn-primary {
    color: #fff;
    background-color: #5c90d2;
    border-color: #3675c5;
    box-shadow: 0 0 2px #00a0f0;
    transition:all 0.3s;
}

.btn-primary:hover {
    background-color: #3c7ac9;
    border-color: #295995;
}

.btn-success.btn-outline {
    color: #62a8ea;
}

.btn-info.btn-outline {
    color: #57c7d4;
}

.btn-warning.btn-outline {
    color: #f2a654;
}

.btn-danger.btn-outline {
    color: #f96868;
}

.btn-primary.btn-outline:hover,
.btn-success.btn-outline:hover,
.btn-info.btn-outline:hover,
.btn-warning.btn-outline:hover,
.btn-danger.btn-outline:hover {
    color: #fff;
}

.btn-primary {
    background-color: #46be8a;
    border-color: #46be8a;
    color: #FFFFFF;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
    background-color: #36ab7a;
    border-color: #36ab7a;
    color: #FFFFFF;
}

.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
    background-image: none;
}

.btn-primary.disabled,
.btn-primary.disabled:hover,
.btn-primary.disabled:focus,
.btn-primary.disabled:active,
.btn-primary.disabled.active,
.btn-primary[disabled],
.btn-primary[disabled]:hover,
.btn-primary[disabled]:focus,
.btn-primary[disabled]:active,
.btn-primary.active[disabled],
fieldset[disabled] .btn-primary,
fieldset[disabled] .btn-primary:hover,
fieldset[disabled] .btn-primary:focus,
fieldset[disabled] .btn-primary:active,
fieldset[disabled] .btn-primary.active {
    background-color: #1dc5a3;
    border-color: #1dc5a3;
}

.btn-success {
    background-color: #62a8ea;
    border-color: #62a8ea;
    color: #FFFFFF;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
    background-color: #4e97d9;
    border-color: #4e97d9;
    color: #FFFFFF;
}

.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
    background-image: none;
}

.btn-success.disabled,
.btn-success.disabled:hover,
.btn-success.disabled:focus,
.btn-success.disabled:active,
.btn-success.disabled.active,
.btn-success[disabled],
.btn-success[disabled]:hover,
.btn-success[disabled]:focus,
.btn-success[disabled]:active,
.btn-success.active[disabled],
fieldset[disabled] .btn-success,
fieldset[disabled] .btn-success:hover,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-success:active,
fieldset[disabled] .btn-success.active {
    background-color: #1f90d8;
    border-color: #1f90d8;
}

.btn-info {
    background-color: #57c7d4;
    border-color: #57c7d4;
    color: #FFFFFF;
}

.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
    background-color: #21b9bb;
    border-color: #21b9bb;
    color: #FFFFFF;
}

.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
    background-image: none;
}

.btn {
	display:inline-block;
	padding:6px 12px;
	margin-bottom:0;
	font-size:14px;
	font-weight:400;
	line-height:1.42857143;
	text-align:center;
	white-space:nowrap;
	vertical-align:middle;
	-ms-touch-action:manipulation;
	touch-action:manipulation;
	cursor:pointer;
	-webkit-user-select:none;
	-moz-user-select:none;
	-ms-user-select:none;
	user-select:none;
	background-image:none;
	border:1px solid transparent;
	border-radius:4px
}
.btn.active.focus,.btn.active:focus,.btn.focus,.btn:active.focus,.btn:active:focus,.btn:focus {
	outline:thin dotted;
	outline:5px auto -webkit-focus-ring-color;
	outline-offset:-2px
}
.btn.focus,.btn:focus,.btn:hover {
	color:#333;
	text-decoration:none
}
.btn.active,.btn:active {
	background-image:none;
	outline:0;
	-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,.125);
	box-shadow:inset 0 3px 5px rgba(0,0,0,.125)
}
.btn.disabled,.btn[disabled],fieldset[disabled] .btn {
	pointer-events:none;
	cursor:not-allowed;
	filter:alpha(opacity=65);
	-webkit-box-shadow:none;
	box-shadow:none;
	opacity:.65
}
.btn-default {
	color:#333;
	background-color:#fff;
	border-color:#ccc
}
.btn-default.active,.btn-default.focus,.btn-default:active,.btn-default:focus,.btn-default:hover,.open>.dropdown-toggle.btn-default {
	color:#333;
	background-color:#e6e6e6;
	border-color:#adadad
}
.btn-default.active,.btn-default:active,.open>.dropdown-toggle.btn-default {
	background-image:none
}
.btn-default.disabled,.btn-default.disabled.active,.btn-default.disabled.focus,.btn-default.disabled:active,.btn-default.disabled:focus,.btn-default.disabled:hover,.btn-default[disabled],.btn-default[disabled].active,.btn-default[disabled].focus,.btn-default[disabled]:active,.btn-default[disabled]:focus,.btn-default[disabled]:hover,fieldset[disabled] .btn-default,fieldset[disabled] .btn-default.active,fieldset[disabled] .btn-default.focus,fieldset[disabled] .btn-default:active,fieldset[disabled] .btn-default:focus,fieldset[disabled] .btn-default:hover {
	background-color:#fff;
	border-color:#ccc
}
.btn-default .badge {
	color:#fff;
	background-color:#333
}
.btn-primary {
	color:#fff;
	background-color:#337ab7;
	border-color:#2e6da4
}
.btn-primary.active,.btn-primary.focus,.btn-primary:active,.btn-primary:focus,.btn-primary:hover,.open>.dropdown-toggle.btn-primary {
	color:#fff;
	background-color:#286090;
	border-color:#204d74
}
.btn-primary.active,.btn-primary:active,.open>.dropdown-toggle.btn-primary {
	background-image:none
}
.btn-primary.disabled,.btn-primary.disabled.active,.btn-primary.disabled.focus,.btn-primary.disabled:active,.btn-primary.disabled:focus,.btn-primary.disabled:hover,.btn-primary[disabled],.btn-primary[disabled].active,.btn-primary[disabled].focus,.btn-primary[disabled]:active,.btn-primary[disabled]:focus,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary,fieldset[disabled] .btn-primary.active,fieldset[disabled] .btn-primary.focus,fieldset[disabled] .btn-primary:active,fieldset[disabled] .btn-primary:focus,fieldset[disabled] .btn-primary:hover {
	background-color:#337ab7;
	border-color:#2e6da4
}
.btn-primary .badge {
	color:#337ab7;
	background-color:#fff
}
.btn-success {
	color:#fff;
	background-color:#5cb85c;
	border-color:#4cae4c
}
.btn-success.active,.btn-success.focus,.btn-success:active,.btn-success:focus,.btn-success:hover,.open>.dropdown-toggle.btn-success {
	color:#fff;
	background-color:#449d44;
	border-color:#398439
}
.btn-success.active,.btn-success:active,.open>.dropdown-toggle.btn-success {
	background-image:none
}
.btn-success.disabled,.btn-success.disabled.active,.btn-success.disabled.focus,.btn-success.disabled:active,.btn-success.disabled:focus,.btn-success.disabled:hover,.btn-success[disabled],.btn-success[disabled].active,.btn-success[disabled].focus,.btn-success[disabled]:active,.btn-success[disabled]:focus,.btn-success[disabled]:hover,fieldset[disabled] .btn-success,fieldset[disabled] .btn-success.active,fieldset[disabled] .btn-success.focus,fieldset[disabled] .btn-success:active,fieldset[disabled] .btn-success:focus,fieldset[disabled] .btn-success:hover {
	background-color:#5cb85c;
	border-color:#4cae4c
}
.btn-success .badge {
	color:#5cb85c;
	background-color:#fff
}
.btn-info {
	color:#fff;
	background-color:#5bc0de;
	border-color:#46b8da
}
.btn-info.active,.btn-info.focus,.btn-info:active,.btn-info:focus,.btn-info:hover,.open>.dropdown-toggle.btn-info {
	color:#fff;
	background-color:#31b0d5;
	border-color:#269abc
}
.btn-info.active,.btn-info:active,.open>.dropdown-toggle.btn-info {
	background-image:none
}
.btn-info.disabled,.btn-info.disabled.active,.btn-info.disabled.focus,.btn-info.disabled:active,.btn-info.disabled:focus,.btn-info.disabled:hover,.btn-info[disabled],.btn-info[disabled].active,.btn-info[disabled].focus,.btn-info[disabled]:active,.btn-info[disabled]:focus,.btn-info[disabled]:hover,fieldset[disabled] .btn-info,fieldset[disabled] .btn-info.active,fieldset[disabled] .btn-info.focus,fieldset[disabled] .btn-info:active,fieldset[disabled] .btn-info:focus,fieldset[disabled] .btn-info:hover {
	background-color:#5bc0de;
	border-color:#46b8da
}
.btn-info .badge {
	color:#5bc0de;
	background-color:#fff
}
.btn-warning {
	color:#fff;
	background-color:#f0ad4e;
	border-color:#eea236
}
.btn-warning.active,.btn-warning.focus,.btn-warning:active,.btn-warning:focus,.btn-warning:hover,.open>.dropdown-toggle.btn-warning {
	color:#fff;
	background-color:#ec971f;
	border-color:#d58512
}
.btn-warning.active,.btn-warning:active,.open>.dropdown-toggle.btn-warning {
	background-image:none
}
.btn-warning.disabled,.btn-warning.disabled.active,.btn-warning.disabled.focus,.btn-warning.disabled:active,.btn-warning.disabled:focus,.btn-warning.disabled:hover,.btn-warning[disabled],.btn-warning[disabled].active,.btn-warning[disabled].focus,.btn-warning[disabled]:active,.btn-warning[disabled]:focus,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning,fieldset[disabled] .btn-warning.active,fieldset[disabled] .btn-warning.focus,fieldset[disabled] .btn-warning:active,fieldset[disabled] .btn-warning:focus,fieldset[disabled] .btn-warning:hover {
	background-color:#f0ad4e;
	border-color:#eea236
}
.btn-warning .badge {
	color:#f0ad4e;
	background-color:#fff
}
.btn-danger {
	color:#fff;
	background-color:#d9534f;
	border-color:#d43f3a
}
.btn-danger.active,.btn-danger.focus,.btn-danger:active,.btn-danger:focus,.btn-danger:hover,.open>.dropdown-toggle.btn-danger {
	color:#fff;
	background-color:#c9302c;
	border-color:#ac2925
}
.btn-danger.active,.btn-danger:active,.open>.dropdown-toggle.btn-danger {
	background-image:none
}
.btn-danger.disabled,.btn-danger.disabled.active,.btn-danger.disabled.focus,.btn-danger.disabled:active,.btn-danger.disabled:focus,.btn-danger.disabled:hover,.btn-danger[disabled],.btn-danger[disabled].active,.btn-danger[disabled].focus,.btn-danger[disabled]:active,.btn-danger[disabled]:focus,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger,fieldset[disabled] .btn-danger.active,fieldset[disabled] .btn-danger.focus,fieldset[disabled] .btn-danger:active,fieldset[disabled] .btn-danger:focus,fieldset[disabled] .btn-danger:hover {
	background-color:#d9534f;
	border-color:#d43f3a
}
.btn-danger .badge {
	color:#d9534f;
	background-color:#fff
}
.btn-link {
	font-weight:400;
	color:#337ab7;
	border-radius:0
}
.btn-link,.btn-link.active,.btn-link:active,.btn-link[disabled],fieldset[disabled] .btn-link {
	background-color:transparent;
	-webkit-box-shadow:none;
	box-shadow:none
}
.btn-link,.btn-link:active,.btn-link:focus,.btn-link:hover {
	border-color:transparent
}
.btn-link:focus,.btn-link:hover {
	color:#23527c;
	text-decoration:underline;
	background-color:transparent
}
.btn-link[disabled]:focus,.btn-link[disabled]:hover,fieldset[disabled] .btn-link:focus,fieldset[disabled] .btn-link:hover {
	color:#777;
	text-decoration:none
}
.btn-group-lg>.btn,.btn-lg {
	padding:10px 16px;
	font-size:18px;
	line-height:1.3333333;
	border-radius:6px
}
.btn-group-sm>.btn,.btn-sm {
	padding:5px 10px;
	font-size:12px;
	line-height:1.5;
	border-radius:3px
}
.btn-group-xs>.btn,.btn-xs {
	padding:1px 5px;
	font-size:12px;
	line-height:1.5;
	border-radius:3px
}
.btn-block {
	display:block;
	width:100%
}
.btn-block+.btn-block {
	margin-top:5px
}

.btn-s1 {    background-color: #7D13BF;
    border-color: #7D13BF;
  color: #FFFFFF!important;
}
.btn-s1:hover,
.btn-s1:focus,
.btn-s1:active,
.btn-s1.active,
.open .dropdown-toggle.btn-s1,
.btn-s1:active:focus,
.btn-s1:active:hover,
.btn-s1.active:hover,
.btn-s1.active:focus {
    background-color: #631494;
    border-color: #631494;
  color: #FFFFFF!important;
}

.btn-s2 {    background-color: slategrey;
    border-color: slategrey;
  color: #FFFFFF!important;
}
.btn-s2:hover,
.btn-s2:focus,
.btn-s2:active,
.btn-s2.active,
.open .dropdown-toggle.btn-s2,
.btn-s2:active:focus,
.btn-s2:active:hover,
.btn-s2.active:hover,
.btn-s2.active:focus {
    background-color: #5a6c7d;
    border-color: #5a6c7d;
  color: #FFFFFF;
}

.cpages li {
    display: inline-block;
    cursor:pointer;
}




.layui-laypage {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: middle;
    margin: 10px 0;
    font-size: 0;
}

    .layui-laypage > a:first-child, .layui-laypage > a:first-child em {
        border-radius: 2px 0 0 2px;
    }

    .layui-laypage > a:last-child, .layui-laypage > a:last-child em {
        border-radius: 0 2px 2px 0;
    }

    .layui-laypage > :first-child {
        margin-left: 0!important;
    }

    .layui-laypage > :last-child {
        margin-right: 0!important;
    }

    .layui-laypage button, .layui-laypage input, .layui-laypage select {
        border: 1px solid #e2e2e2;
    }

    .layui-laypage a, .layui-laypage span {
        display: inline-block;
        *display: inline;
        *zoom: 1;
        vertical-align: middle;
        padding: 0 15px;
        height: 28px;
        line-height: 28px;
        margin: 0 -1px 5px 0;
        background-color: #fff;
        color: #333;
        font-size: 12px;
        border:0;
    }

        .layui-laypage a:hover {
            color: #009688;
        }

    .layui-laypage em {
        font-style: normal;
    }

    .layui-laypage .layui-laypage-spr {
        color: #999;
        font-weight: 700;
    }

    .layui-laypage a {
        text-decoration: none;
    }

    .layui-laypage .layui-laypage-curr {
        position: relative;
    }

        .layui-laypage .layui-laypage-curr em {
            position: relative;
            color: #fff;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            position: absolute;
            left: -1px;
            top: -1px;
            padding: 1px;
            width: 100%;
            height: 100%;
            background-color: #009688;
        }

.layui-laypage-em {
    border-radius: 2px;
}

.layui-laypage-next em, .layui-laypage-prev em {
    font-family: Sim sun;
    font-size: 16px;
}

.layui-laypage .layui-laypage-count, .layui-laypage .layui-laypage-limits, .layui-laypage .layui-laypage-skip {
    margin-left: 10px;
    margin-right: 10px;
    padding: 0;
    border: none;
}

.layui-laypage .layui-laypage-limits {
    vertical-align: top;
}

.layui-laypage select {
    height: 22px;
    padding: 3px;
    border-radius: 2px;
    cursor: pointer;
}

.layui-laypage .layui-laypage-skip {
    height: 30px;
    line-height: 30px;
    color: #999;
}

.layui-laypage button, .layui-laypage input {
    height: 30px;
    line-height: 30px;
    border: 1px solid #e2e2e2;
    border-radius: 2px;
    vertical-align: top;
    background-color: #fff;
    box-sizing: border-box;
    color: #333;
}

.layui-laypage input {
    display: inline-block;
    width: 40px;
    margin: 0 10px;
    padding: 0 3px;
    text-align: center;
}

    .layui-laypage input:focus, .layui-laypage select:focus {
        border-color: #009688!important;
    }

.layui-laypage button {
    margin-left: 10px;
    padding: 0 10px;
    cursor: pointer;
}



    .cpages li a {
        display: inline-block;
        vertical-align: middle;
        padding: 0 15px;
        height: 28px;
        line-height: 28px;
        margin: 0 -1px 5px 0;
        background-color: #fff;
        color: #333;
        font-size: 12px;
    }

    .cpages li.active a {
        background-color: #009688;
        color: #fff;
    }

    .cpages li.disabled a {
        color: #d2d2d2!important;
        cursor: not-allowed!important;
    }

