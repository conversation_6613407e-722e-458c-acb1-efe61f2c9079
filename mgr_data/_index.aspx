<%@ Page Title="数据报表" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="_index.aspx.cs" Inherits="serv_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div style="margin: 1rem auto; background: #fefefe; border: 1px solid #eee; border-radius: 6px; padding: 2rem;">

        <style>
            .wrap {
                line-height: 32px;
                height: 32px;
                border: 1px solid #dcdfe6;
                /*width: 400px;*/
                display: flex;
                transition: border-color .2s;
                border-radius: 4px;
                margin-left: 10px;
            }

                .wrap:hover {
                    border-color: #c0c4cc;
                }

                .wrap:active {
                    border-color: #66b1ff;
                }

                .wrap.active {
                    border-color: #66b1ff;
                }

                .wrap > div {
                    flex: 1;
                    padding: 0 10px;
                    display: flex;
                    cursor: pointer;
                }

                .wrap .ali-iconfont {
                    display: inline-block;
                    width: 20px;
                    text-align: center;
                    margin-right: 5px;
                }

            .wrap-l-starTime, .wrap-l-endTime {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .wrap [name="startTime"], .wrap [name="endTime"] {
                flex: 1;
                width: 100%;
            }

                .wrap [name="startTime"], .wrap [name="endTime"], .wrap [name="startTime"]:focus, .wrap [name="endTime"]:focus {
                    border: none;
                    outline: none;
                    cursor: pointer;
                    line-height: normal;
                }

            [name="datePicker"].is-double {
                position: absolute;
                background: #fff;
            }


            .wrap-l::after {
                content: "";
                position: relative;
                top: 20%;
                width: 1px;
                height: 60%;
                background-color: #e1e0e0;
            }

            [name="datePicker"].is-fast .datePicker-content-fast {
                display: none!important;
            }
        </style>

        <style>
            .jbFIoI {
                display: flex;
                -webkit-box-pack: justify;
                justify-content: space-between;
                -webkit-box-align: center;
                align-items: center;
                padding: 3px 0;
                cursor: pointer;
                white-space: nowrap;
                padding-left: 9px;
                gap: 10px;
            }

                .jbFIoI > div:nth-child(1) {
                    -webkit-box-align: center;
                    align-items: center;
                    display: grid;
                    grid-template-columns: 8px minmax(62px, auto) minmax(40px, 1fr);
                    overflow: hidden;
                    min-width: 130px;
                }

                .jbFIoI .rank {
                    font-size: 12px;
                    line-height: 18px;
                    color: var(--color-light-neutral-5);
                    font-family: sans-serif;
                }

            .iULUNk {
                box-sizing: border-box;
                margin: 0px;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
                overflow: hidden;
            }

            .jbFIoI .coin-wrapper {
                margin-left: 16px;
            }

            .jbFIoI .coin-logo {
                width: 16px;
                height: 16px;
            }

            .jbFIoI > div:nth-child(1) .name-area {
                flex-direction: row !important;
                -webkit-box-align: center !important;
                align-items: center !important;
            }

            .jbFIoI .coin-wrapper p {
                font-size: 12px;
                font-weight: 600;
                margin-left: 8px;
                flex: 1 1 0%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .jbFIoI .alias {
                margin-left: 8px;
                color: var(--color-light-neutral-5);
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 12px;
            }

            .jbFIoI .price-change {
                font-size: 12px;
            }


            .iATetF p {
                margin: 0px;
                margin-inline-start: 5px;
                line-height: 24px;
            }

            .statistics-wrapper {
                width: 100%;
                box-shadow: rgba(88, 102, 126, 0.08) 0px 4px 24px, rgba(88, 102, 126, 0.12) 0px 1px 2px;
                padding: 16px;
                width: 25%;
                margin: 0 5px;
            }




                .statistics-wrapper .statistics-title span {
                    font-size: 12px;
                    font-weight: 400;
                    color: #58667e;
                }

                .statistics-wrapper .statistics-content {
                    font-size: 24px;
                    font-weight: 700;
                    color: #000;
                    margin: 0px;
                }

                .statistics-wrapper .statistics-content2 {
                    color: green;
                }


            #refund_list .statistics-wrapper .statistics-title span {
                color: #de7d9f;
            }

            #refund_list .statistics-wrapper .statistics-content {
                color: #E91E63;
            }



            .ant-input-kit {
                padding: 10px 20px;
                background: rgb(242, 243, 245);
                border: 0 solid transparent;
                outline: none;
                width: 100%;
                box-sizing: border-box;
                font-size: 12px;
            }

            .ant-input-kit-title {
                background: rgb(242, 243, 245);
                width: 100px;
                text-align: center; /* vertical-align: middle; */ /* text-align: justify; */ /* display: flex; */
                display: flex; /*实现垂直居中*/
                align-items: center; /*实现水平居中*/
                justify-content: center;
                text-align: justify;
                color: #000;
                border-right: 1px solid rgb(229,230,235);
                border-top-left-radius: 3px;
                border-bottom-left-radius: 3px;
            }

            .ant-input-kit-button {
                padding: 10px 28px;
                font-weight: bold;
                font-size: 12px;
                background: #3C89FF;
                color: #fff;
                border: 0 solid transparent;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
                cursor: pointer;
                outline: none;
                text-decoration: none;
                display: inline-block;
                box-sizing: border-box;
                transition: all 0.2s;
                height: 100%!important;
            }


            .statistics-wrapper {
                width: 100%;
                box-shadow: rgba(88, 102, 126, 0.08) 0px 4px 24px, rgba(88, 102, 126, 0.12) 0px 1px 2px;
                padding: 16px;
                width: 50%;
                margin: 0 5px;
            }




                .statistics-wrapper .statistics-title span {
                    font-size: 12px;
                    font-weight: 400;
                    color: #58667e;
                }

                .statistics-wrapper .statistics-content {
                    font-size: 24px;
                    font-weight: 700;
                    color: #000;
                    margin: 0px;
                }

            .select_lab {
                background: #fff;
                color: #333;
                padding: 5px 0;
                width: 70px;
                text-align: center;
                border: 1px solid #eee;
                margin-left: -1px;
                cursor: pointer;
            }

                .select_lab.active {
                    background: #5c87ff;
                    color: #fff;
                    cursor: default;
                }
        </style>



        <%--<div style="display: flex; margin: 20px 0;">

            <div class="statistics-wrapper" style="width:100%;background: #eaf9f4;">
                <div class="statistics-title"><span style="    color: #4bb591;">今日统计</span></div>
                <div class="statistics-content" style="    color: #4bb591;">0次</div>
                <div class="statistics-content2" style="color:#4babb5;">当日 0次数量</div>
            </div>

        </div>--%>


        <div style="margin-bottom: 10px;">
            <h3>数据统计</h3>
        </div>


        <div style="display: flex; flex-wrap: wrap;">

            <div class="select_lab active">今 天</div>
            <div class="select_lab">昨 天</div>
            <div class="select_lab">7天内</div>
            <div class="select_lab">30天内</div>
            <div class="select_lab">自定义</div>



            <div class="timer-wrap" style="position: relative; display: none;">

                <div class="wrap">
                    <div class="wrap-l">
                        <div class="wrap-l-starTime">
                            <input name="startTime" type="text" placeholder="开始日期">
                        </div>
                    </div>
                    <div class="wrap-r">
                        <div class="wrap-l-endTime">
                            <input name="endTime" type="text" placeholder="结束日期">
                        </div>
                    </div>
                </div>

            </div>


        </div>


        <link href="../css/jquery-datePicker.css" rel="stylesheet" />
        <script src="../js/moment.js"></script>
        <script src="../js/jquery-datePicker.min.js"></script>
        <script>
            var fastTime = {
                true: {
                    4: {
                        '最近7天': {
                            startTime: moment()
            					.subtract(7, 'day')
            					.format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment()
            					.format('YYYY-MM-DD HH:mm:ss')
                        },
                        '最近一个月': {
                            startTime: moment()
            					.subtract(1, 'month')
            					.format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment()
            					.format('YYYY-MM-DD HH:mm:ss')
                        },
                        '最近三个月': {
                            startTime: moment()
            					.subtract(3, 'month')
            					.format('YYYY-MM-DD HH:mm:ss'),
                            endTime: moment()
            					.format('YYYY-MM-DD HH:mm:ss')
                        }
                    }
                }
            }
            $('.wrap')
            	.on('click', function () {
            	    var _this = this;
            	    var reportTimeType = 4;
            	    var double = true;
            	    if (!$(this).next('[name="datePicker"]').length) {
            	        $(this).after("<div class='datePicker-x' name='datePicker'></div>");
            	        datePicker = $('.datePicker-x').datePicker({
            	            reportTimeType: reportTimeType,
            	            startDom: $(_this)
                                .find('input[name="startTime"]'),
            	            endDom: $(_this)
                                .find('input[name="endTime"]'),
            	            format: 'YYYY-MM-DD HH:mm:ss',
            	            fastTime: fastTime[double][reportTimeType],
            	            isFast: true,
            	            isDouble: double,
            	            disabledDate: false,
            	            yes: function (startTime, endTime) {
            	                console.log('时间设置', startTime, endTime);
            	                var g = startTime.split(' ');
            	                startTime = g[0] + " 00:00:00";

            	                g = endTime.split(' ');
            	                endTime = g[0] + " 23:59:59";

            	                getdata(startTime, endTime);
            	            },
            	        });
            	    } else {
            	        if ($(this)
            				.next('[name="datePicker"]')
            				.hasClass('hide')) {
            	            $(this)
            					.next('[name="datePicker"]')
            					.removeClass('hide');
            	            datePicker.render();
            	        } else {
            	            $(this)
            					.next('[name="datePicker"]')
            					.addClass('hide');
            	        }
            	    }
            	});

        </script>


        <div style="display: flex; margin: 20px 0;">

            <div class="statistics-wrapper">
                <div class="statistics-title"><span>任务总点</span></div>
                <div class="statistics-content" style="color: green;" id="amounts">￥<%=yesterdayAmount %></div>
            </div>

            <div class="statistics-wrapper">
                <div class="statistics-title"><span>任务总数</span></div>
                <div class="statistics-content" id="numbers"><%=todayAmount %></div>
            </div>

        </div>


        <style>
            .transaction_total {
                display: flex;
                width: 100%;
                max-width: 500px;
                align-items: center;
                padding: 10px 0;
            }

                .transaction_total .sortId {
                    padding: 0 10px;
                }

                    .transaction_total .sortId span {
                        display: inline-block;
                        border-radius: 50%;
                        background: #FF5635;
                        color: #fff;
                        width: 25px;
                        height: 25px;
                        text-align: center;
                        line-height: 25px;
                        font-size: 12px;
                    }

                .transaction_total .amounts {
                    width: 100%;
                }

                    .transaction_total .amounts .amount_total {
                        color: gray;
                        display: flex;
                        margin-top: 8px;
                    }

                        .transaction_total .amounts .amount_total > div {
                            width: 50%;
                        }


            .total_list {
                display: flex;
                flex-wrap: wrap;
            }


                .total_list .statistics-wrapper {
                    margin: 0px;
                    width: 100%;
                }

                .total_list > div {
                    width: 50%;
                    padding: 0 5px;
                    margin-bottom: 10px;
                }

            @media screen and (max-width:900px) {
                .total_list > div {
                    width: 100%;
                }
            }

            @media screen and (min-width:1500px) {
                .total_list > div {
                    width: 33.333%;
                }
            }

            @media screen and (min-width:2000px) {
                .total_list > div {
                    width: 25%;
                }
            }

            @media screen and (min-width:2500px) {
                .total_list > div {
                    width: 20%;
                }
            }


            .total_list .logo-wrapper {
                padding: 10px 0;
                display: flex;
                align-items: center;
            }

            .logo-wrapper .logo {
                height: 30px;
            }

            .logo-wrapper .total_amount {
                margin-left: auto;
                font-weight: bold;
                background: #d6e7ff9e;
                padding: 2px 16px;
                border-radius: 8px;
            }
        </style>

        <div class="total_list">
            <div>
                <div class="statistics-wrapper">
                    <b style="margin-bottom: 10px;">任务统计</b>
                    <div id="transaction_list">
                    </div>
                </div>
            </div>

            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/tiktok.JPG">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="tk_list">
                    </div>
                </div>
            </div>


            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/FB.JPG">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="fb_list">
                    </div>
                </div>
            </div>


            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/INS.JPG">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="ins_list">
                    </div>
                </div>
            </div>


            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/youtube.JPG">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="ytb_list">
                    </div>
                </div>
            </div>


            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/twitch.png">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="twitch_list">
                    </div>
                </div>
            </div>


            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/shopee.png">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="shopee_list">
                    </div>
                </div>
            </div>


            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/twitter.jpg">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="tw_list">
                    </div>
                </div>
            </div>

            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/bigo.png">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="bigo_list">
                    </div>
                </div>
            </div>

            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/steam.png">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="steam_list">
                    </div>
                </div>
            </div>

            
            <div>
                <div class="statistics-wrapper">
                    <div class="logo-wrapper">
                        <img class="logo" src="../static/images/b/lazada.png">
                        <div class="total_amount">
                            0 点                       
                        </div>
                    </div>
                    <div id="lazada_list">
                    </div>
                </div>
            </div>


        </div>


        <div>
            <div class="statistics-wrapper" style="width: 100%;">
                <b style="margin-bottom: 10px;">用户统计</b>
                <div id="usertotal_list">
                </div>
            </div>
        </div>

        

        <script>
            function formatDateTimeToYYYYMMDDHHMMSS(date) {
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, "0");
                var day = date.getDate().toString().padStart(2, "0");
                var hours = date.getHours().toString().padStart(2, "0");
                var minutes = date.getMinutes().toString().padStart(2, "0");
                var seconds = date.getSeconds().toString().padStart(2, "0");
                return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
            }

            function getDateRange(type) {
                var now = new Date();
                var startDate = new Date();
                var endDate = new Date();

                var year = now.getFullYear();
                var month = now.getMonth();
                var day = now.getDate();

                if (type === "today") {
                    startDate.setHours(0, 0, 0, 0);
                    endDate.setHours(23, 59, 59, 999);
                } else if (type === "yesterday") {
                    startDate.setFullYear(year, month, day - 1);
                    startDate.setHours(0, 0, 0, 0);
                    endDate.setFullYear(year, month, day - 1);
                    endDate.setHours(23, 59, 59, 999);
                } else if (type === "sevenDaysAgo") {
                    startDate.setFullYear(year, month, day - 7);
                    startDate.setHours(0, 0, 0, 0);
                    endDate.setHours(23, 59, 59, 999);
                } else if (type === "thirtyDaysAgo") {
                    startDate.setFullYear(year, month, day - 30);
                    startDate.setHours(0, 0, 0, 0);
                    endDate.setHours(23, 59, 59, 999);
                } else {
                    // 默认获取今日的时间范围
                    startDate.setHours(0, 0, 0, 0);
                    endDate.setHours(23, 59, 59, 999);
                }

                return { start: startDate, end: endDate };
            }

            $('.select_lab').on('click', function () {
                var text = $(this).text();
                $(this).addClass("active").siblings().removeClass("active");
                if (text == "自定义") {
                    $('.timer-wrap').show();
                } else {
                    $('.timer-wrap').hide();
                    console.log('getdata', text);
                    var _day = {};
                    switch (text) {
                        case "今 天":
                            _day = getDateRange('today');
                            break;
                        case "昨 天":
                            _day = getDateRange('yesterday');
                            break;
                        case "7天内":
                            _day = getDateRange('sevenDaysAgo');
                            break;
                        case "30天内":
                            _day = getDateRange('thirtyDaysAgo');
                            break;
                        default:
                            return;
                            break;

                    }
                    console.log('ok', _day);

                    getdata(formatDateTimeToYYYYMMDDHHMMSS(_day.start), formatDateTimeToYYYYMMDDHHMMSS(_day.end))
                }
            })

            $('.select_lab.active').click();


            var map = {};

            function getdata(start_time, end_time) {
                $("#numbers").html("~")
                $("#amounts").html("~")

                $.ajax({
                    type: "POST",
                    url: "apidata.aspx?do=get_summary",
                    data: { start_time: start_time, end_time: end_time },
                    datatype: "json",
                    success: function (json) {
                        if (json.code == 1) {

                            $("#numbers").html(json.numbers);
                            $("#amounts").html(json.amount);

                            map = {
                                tk: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , ins: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , fb: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , ytb: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , twitch: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , shopee: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , tw: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , bigo: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , steam: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                                , lazada: {
                                    amount: 0,
                                    numbers: 0,
                                    refund_numbers: 0,
                                    list: ''
                                }
                            };

                            $('#transaction_list').html('');
                            for (var i = 0; i < json.list.length; i++) {

                                var D = json.list[i];
                                var _html = '<div class="transaction_total">                    <div class="sortId" style="padding: 0 10px;">                        <span><b>' + (i + 1) + '</b></span>                    </div>                    <div class="amounts">                        <div><span>' + D.name + '</span><span style="font-size: 12px; background: #63686e8c; color: #fff8e0; border-radius: 10px; font-weight: 100; padding: 0 6px; margin-left: 5px;">' + D.total_numbers + ' 个</span><span style="font-size: 12px;background: #dddeffcf;color: #485407;border-radius: 10px;font-weight: 100;padding: 0 6px;margin-left: 5px;">' + D.total_refund_numbers + ' 退</span></div>                        <div class="amount_total">                            <div>                                任务点数：' + D.total_amount + '                            </div>                            <div>                                <div>                                    取消点数：' + D.total_refund_amount + '                                </div>                            </div>                        </div>                    </div>                </div>';
                                $('#transaction_list').append(_html);


                                map[D.tp].amount += (parseFloat(D.total_amount) - parseFloat(D.total_refund_amount));
                                map[D.tp].numbers += parseFloat(D.total_numbers);
                                map[D.tp].refund_numbers += parseFloat(D.total_refund_numbers);
                                map[D.tp].list += _html;

                            }

                            for (var k in map) {
                                console.log('k', k);

                                $('#' + k + '_list').closest('.statistics-wrapper').find(".total_amount").html(map[k].amount.toFixed(2) + ' 点' + '<span style="font-size: 12px; background: #63686e8c; color: #fff8e0; border-radius: 10px; font-weight: 100; padding: 0 6px; margin-left: 5px;">' + map[k].numbers + ' 个</span><span style="font-size: 12px;background: #fff;color: #5b7eaf;border-radius: 10px;font-weight: 100;padding: 0 6px;margin-left: 5px;">' + map[k].refund_numbers + ' 退</span>');
                                $('#' + k + '_list').html(map[k].list);
                            }



                            $('#usertotal_list').html('');
                            for (var i = 0; i < json.users.length; i++) {

                                var D = json.users[i];
                                var _html = '<div class="transaction_total">                    <div class="sortId" style="padding: 0 10px;">                        <span style="color: #eee;background: #6f83e9;"><b>' + (i + 1) + '</b></span>                    </div>                    <div class="amounts">                        <div><span>' + D.name + '</span><span style="font-size: 12px; background: #63686e8c; color: #fff8e0; border-radius: 10px; font-weight: 100; padding: 0 6px; margin-left: 5px;">' + D.total_numbers + ' 个</span><span style="font-size: 12px;background: #dddeffcf;color: #485407;border-radius: 10px;font-weight: 100;padding: 0 6px;margin-left: 5px;">' + D.total_refund_numbers + ' 退</span></div>                        <div class="amount_total">                            <div>                                任务点数：' + D.total_amount + '                            </div>                            <div>                                <div>                                    取消点数：' + D.total_refund_amount + '                                </div>                            </div>                        </div>                    </div>                </div>';
                                $('#usertotal_list').append(_html);

                            }

                        } else {
                            alert(json.msg);
                        }
                    },
                    error: function () {
                    }
                });
            }
        </script>
</asp:Content>

