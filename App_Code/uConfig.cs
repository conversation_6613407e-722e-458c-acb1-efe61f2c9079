using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using LitJson;

/// <summary>
/// uConfig 的摘要说明
/// </summary>
public class uConfig:globalClass
{
    public static string loginParames_nick = "usernick";
    public static string loginParames_pwd = "passsword";
    public static string loginParames_uid = "userid";
    public static string loginParames_usp = "usersp";
    public static string loginParames_utp = "usertp";
    public static string loginParames_nick_admin = "mgr_usernick";
    public static string loginParames_pwd_admin = "mgr_password";
    public static string loginParames_token_admin = "mgr_tokenid";
    public static string siteDomain = "";
    public static string superAdminPrefix = "";

    public static string p_userNick { get { return globalClass.getCookie(loginParames_nick); } }
    public static string p_passWord { get { return globalClass.getCookie(loginParames_pwd); } }
    public static string p_uid { get { return globalClass.getCookie(loginParames_uid); } }
    public static string p_usp { get { return globalClass.getCookie(loginParames_usp); } }
    public static string p_utp { get { return globalClass.getCookie(loginParames_utp); } }
    public static string p_userNickAD { get { return globalClass.getCookie(loginParames_nick_admin); } }
    public static string p_passWordAD { get { return globalClass.getCookie(loginParames_pwd_admin); } }
    public static string p_tokenAD { get { return globalClass.getCookie(loginParames_token_admin); } }

    public static string _static_dyApiurl = "http://43.228.125.193:8999/";

	public uConfig()
	{}

    public static bool _userIsLogin
    {
        get
        {
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            SqlParameter[] parames = new SqlParameter[]{
                    new SqlParameter("@u",uConfig.p_userNick),
                    new SqlParameter("@p",uConfig.p_passWord),
                    new SqlParameter("@uid",uConfig.p_uid),
                    new SqlParameter("@sp",uConfig.p_usp),
                    new SqlParameter("@userType",uConfig.p_utp)
                };
            string sql = " select top 1 * from users with(nolock) where id=@uid and nick=@u and pwd=@p and isnull(sp,'')=@sp and status=1 and isnull(userType,'')=@userType ";
            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            return true;
        }
    }

    public static bool _adminIsLogin
    {
        get
        {
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            SqlParameter[] parames = new SqlParameter[]{
                    new SqlParameter("@u",uConfig.p_userNickAD),
                    new SqlParameter("@p",uConfig.p_passWordAD)
                };
            string sql = " select * from account with(nolock) where nick=@u and pwd=@p ";
            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            return true;
        }
    }

    public static bool isSuperAdmin
    {
        get
        {
            dbClass db = new dbClass();
            DataTable dt = new DataTable();
            SqlParameter[] parames = new SqlParameter[]{
                    new SqlParameter("@u",uConfig.p_userNickAD),
                    new SqlParameter("@p",uConfig.p_passWordAD)
                };
            string sql = " select * from account with(nolock) where nick=@u and pwd=@p and rk=1 ";
            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count == 0)
            {
                return false;
            }
            return true;
        }
    }

    public static DataTable appType
    {
        get
        {
            DataTable dt = (DataTable)cae.GetCache("appType");
            if (dt == null)
            {
                string sql = " select * from tps where hd=0 order by sort ";
                dbClass db = new dbClass();
                dt = db.getDataTable(sql);
                cae.SetCache("appType", dt);
            }
            return dt;
        }
    }

    public static DataTable appList
    {
        get
        {
            DataTable dt = (DataTable)cae.GetCache("appList");
            if (dt == null)
            {
                string sql = " select * from apps with(nolock) where status=1 order by typename,sort ";
                dbClass db = new dbClass();
                dt = db.getDataTable(sql);
                cae.SetCache("appList", dt);
            }
            return dt;
        }
    }

    public static DataTable appListAll
    {
        get
        {
            DataTable dt = (DataTable)cae.GetCache("appListAll");
            dt = null;
            if (dt == null)
            {
                string sql = " select * from apps with(nolock) order by typename,(case when status=1 then 0 else 999 end) ";
                dbClass db = new dbClass();
                dt = db.getDataTable(sql);
                cae.SetCache("appListAll", dt);
            }
            return dt;
        }
    }

    public static DataTable cgs
    {
        get
        {
            DataTable dt = (DataTable)cae.GetCache("category");
            if (dt == null)
            {
                string sql = " select * from category order by appcode ";
                dbClass db = new dbClass();
                dt = db.getDataTable(sql);
                cae.SetCache("category", dt);
            }
            return dt;
        }
    }

    public static DataTable users
    {
        get
        {
            DataTable dt = (DataTable)cae.GetCache("users");
            if (dt == null)
            {
                string sql = " select * from users with(nolock) ";
                dbClass db = new dbClass();
                dt = db.getDataTable(sql);
                cae.SetCache("users", dt);
            }
            return dt;
        }
    }

    public static DataTable _stc(string site_id = "")
    {
        DataTable dt;
        dt = (DataTable)cae.GetCache("stc_" + site_id);
        if (dt == null)
        {
            dbClass db = new dbClass();
            string sql = " select * from [stConfig] with(nolock) ";
            dt = db.getDataTable(sql);
            cae.SetCache("stc_" + site_id, dt);
        }
        return dt;

    }
    public static string stcdata(string name, string site_id = "", string def = "")
    {
        string res = "";
        try
        {
            DataTable dt = _stc(site_id);
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if ((dt.Rows[i]["name"] + "") == name)
                {
                    return dt.Rows[i]["data"] + "";
                }
            }
        }
        catch (Exception)
        {
            res = "";
        }
        if (string.IsNullOrEmpty(res))
        {
            res = def;
        }
        return res;
    }


    public static string gd(DataTable dt, string name, string defcontent = "")
    {
        string res = "";
        try
        {
            res = dt.Rows[0][name] + "";
        }
        catch (Exception)
        {
            res = "";
        }
        if (res == "")
        {
            res = defcontent;
        }
        return res;
    }

    public static string gdr(DataTable dt, string name, string defcontent = "")
    {
        string res = gd(dt, name, defcontent);
        return res.Replace("\"", "&quot;");
    }

}