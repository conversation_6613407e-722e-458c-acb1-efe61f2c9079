using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;

public partial class Api_Api : globalClass
{
    public string apikey = "aigh9_DktW3BnMyQmKF8";//一号技术
    public string apikey2 = "JkBUKSYqVGc0ODAydDdn";//转单
    protected void Page_Load(object sender, EventArgs e)
    {
        //Response.ContentType = "text/plain";
        string key = Request.QueryString["key"] + "";
        string action = Request.QueryString["do"] + "";
        //if (key != apikey && action != "change")
        //{
        //    Response.Write("{\"code\":10000,\"msg\":\"您没有操作权限!\"}");
        //    Response.End();
        //}
        Response.Write(main(action));
        Response.End();
    }

    public string main(string act)
    {
        fuzhu fz = new fuzhu();
        List<SqlParameter> pams = fz.collectReqParames();
        Dictionary<string, object> tdic = new Dictionary<string, object>();

        string key = Request.QueryString["key"] + "";
        string request_url = Request.Url.ToString();
        string cache_name = "req-" + request_url;

        string back = "";
        string tempstr = string.Empty;
        Dictionary<string, object> dic = new Dictionary<string, object>();

        string sql = "";
        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        DataTable dt2 = new DataTable();
        DataSet ds = new DataSet();
        int res = 0;
        SqlParameter[] parames = null;
        List<string> ls = new List<string>();
        string[] g;

        //parames
        string json = "";
        string temp = "";

        string all = Request.QueryString["all"] + "";
        string id = Request.QueryString["id"] + "";
        string _code = Request.QueryString["code"] + "";
        string _date = Request.QueryString["date"] + "";
        string startCount = Request.QueryString["start"] + "";
        string nowCount = Request.QueryString["now"] + "";
        string status = gs(Request.QueryString["status"] + "");
        string max = Request.QueryString["max"] + "";
        string order = Request.QueryString["order"] + "";
        string api_params = Request.QueryString["api_params"] + "";
        string state_text = Request.QueryString["state_text"] + "";
        string remark1 = Request.QueryString["remark1"] + "";
        string status_flag = Request.QueryString["status_flag"] + "";
        string note = Request.QueryString["note"] + "";
        string count = "0";
        string rspJson = string.Empty;

        order = order == "esc" ? "  " : " desc ";

        bool oldOrder = false;
        int softid = 10000;
        if (key == apikey)
        {
            softid = 10001;
        }
        else if (key == apikey2)
        {
            softid = 10002;
        }
        else
        {
            return "{\"code\":10000,\"msg\":\"您没有操作权限!\"}";
        }

        //if (key != apikey)
        //{
        //    oldOrder = true;

        //    log.WriteLog("警告警告_非正常API", "", "act:" + act + ",完整url:" + Request.Url.ToString() + ",ip:" + getUserIP(HttpContext.Current));


        //    dic = new Dictionary<string, object>();

        //    dic.Add("code", "10003");
        //    dic.Add("msg", "订单不存在");

        //    back = JsonMapper.ToJson(dic);
        //    return back;
        //}
        List<string> business_list = new List<string>() { "dy-plzdy", "dy-pl", "dy-dz", "dy-gz", "dy-bf", "dy-fx", "dy-kz", "dy-flpl", "dy-pldz", "dy-zf", "hs-gz", "hs-dz", "hs-pl", "hs-bf", "hs-fx", "hs-plzdy", "hs-flpl", "dyzb-dc", "dyzb-dr", "dyzb-bz", "dyzb-by", "hszb-dc", "hszb-bt", "hszb-bz", "hszb-by", "xhs-gz", "xhs-sc", "xhs-dz", "xhs-pl", "xhs-bf", "xhs-fx", "xhs-flpl", "xhs-plzdy", "xhs-sjgz", "dy-dzm", "dy-gzm", "dy-dzwz" };
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        bool check_status = false;

        if (!string.IsNullOrEmpty(max))
        {
            max = " top " + max + " ";
        }



        string cachename = Request.QueryString["cache"] + "";
        string request_type = Request.QueryString["request_type"] + "";
        int MaxTopnum = 100;
        string cache_index = "-1";
        string end_id = "";
        int leng = 0;
        string order_by = string.Empty;
        bool check_exists = false;
        string codeString = string.Empty;
        string temp2 = string.Empty;
        string temp3 = string.Empty;
        string cond = string.Empty;
        string table = string.Empty;
        string url = string.Empty;
        string plid = string.Empty;
        string sqltemp = string.Empty;
        string tempurl = string.Empty;
        string orderid = string.Empty;
        string order_type = string.Empty;
        string cachetimeStr = Request.QueryString["cachetime"] + "";
        string extra = Request.QueryString["extra"] + "";
        int cachetime = 2;
        string errnum = string.Empty;
        string updateSql = string.Empty;
        int errtotalnum = 3;
        Int64 tempInt = 0;
        string yzid = string.Empty;
        bool dyzbdm = false;

        indexT idxT = new indexT();
        List<indexT> idxTs = new List<indexT>();


        order_type = Request.QueryString["order_type"] + "";


        //配置

        int dyzbBcMinute = 2;//单次最大补单间隔时间
        int dyzbBdNum = 9999;//单次最大补单数量

        int dyzbCheckNumSecond = 60;//抖音直播检测数量检测
        int dyzbCheckNumDiffNum = 100;//抖音直播检测低于n个数量采用最低当前值（满足达到dyzbCheckNumSecond的情况下）

        Dictionary<string, object> ss = new Dictionary<string, object>();



        //log.WriteLog("API日志", "", "act:" + act + ",完整url:" + Request.Url.ToString() + ",ip:" + getUserIP(HttpContext.Current));
        switch (act)
        {
            case "obtainOrder":
                //back = (string)cae.GetCache(cache_name);
                //if (!string.IsNullOrEmpty(back))
                //{
                //    try
                //    {
                //        JsonData jd = JsonMapper.ToObject(back);
                //        DateTime d1 = Convert.ToDateTime(jd["cache_next_time"] + "");
                //        DateTime d2 = DateTime.Now;
                //        if (d1 >= d2)
                //        {
                //            return back.Replace("\"total", "\"cache\":1,\"total");
                //        }
                //    }
                //    catch (Exception)
                //    {

                //    }
                //}

                //if (!string.IsNullOrEmpty(back))
                //{
                //    return back.Replace("\"total","\"cache\":1,\"total");
                //}
                try
                {
                    cachetime = Convert.ToInt16(cachetimeStr);
                }
                catch (Exception)
                {
                    cachetime = 2;
                }

                if (cachetime < 1)
                {
                    cachetime = 1;
                }

                parames = new SqlParameter[]{
                    new SqlParameter("@code",_code)
                };

                if (_code == "dy-dzall")
                {
                    _code = "dy-dz,dy-dzm,dy-dzwz,dy-mz";
                }


                if (_code == "dyzb-dc")
                {
                    _code = "dyzb-dc,dyzb-bdc";
                }
                if ("dyzb-dm,dyzb-gpgm,dyzb-dmzdy,dyzb-fx".IndexOf(_code.Replace("gj", "")) != -1 && _code != "")
                {
                    dyzbdm = true;
                }

                if (_code != string.Empty)
                {
                    _code = _code.Replace("like", "lke");

                    g = _code.Split(',');
                    _code = string.Empty;
                    check_status = false;
                    for (int t = 0; t < g.Length; t++)
                    {
                        //for (int i = 0; i < business_list.Count; i++)
                        //{
                        //    if (business_list[i] == g[t])
                        //    {
                        //        _code += _code == "" ? "" : " or ";
                        //        _code += " code='" + g[t] + "' ";
                        //        break;
                        //    }
                        //}
                        _code += _code == "" ? "" : " or ";
                        //_code += " code='" + SafeSql(g[t]) + "' ";


                        //_code += " replace(replace(replace(replace(code,'3h',''),'1h',''),'bt',''),'bz','')='" + SafeSql(g[t]) + "' ";

                        _code += " code='" + SafeSql(g[t]) + "' ";
                    }

                    if (string.IsNullOrEmpty(_code))
                    {
                        return "{\"code\":10001,\"msg\":\"此业务暂时不支持操作\"}";
                    }
                    _code = "(" + _code + ")";


                    _code = _code.Replace("lke", "like");
                }
                //else
                //{
                //    for (int i = 0; i < business_list.Count; i++)
                //    {
                //        _code += _code == "" ? "" : " or ";
                //        _code += " code='" + business_list[i] + "' ";
                //    }
                //    _code = "(" + _code + ")";
                //}
                _code = (string.IsNullOrEmpty(_code) ? "" : " and ") + _code;

                status = status == "" ? "  and status<>'已完成' and status<>'订单异常' " + (all == "1" ? "" : " and status<>'退单中'  ") : " and status='" + status + "' ";

                if (dyzbdm)
                {
                    status = " and status='已完成' ";
                    _code += " and expireTime>GETDATE() and order_type=988 ";
                }
                else
                {
                    _code += " and order_type=0 ";
                }


                if (status_flag != "")
                {
                    status_flag = status_flag.Replace("未设置", "");
                    status_flag = status_flag.Replace("清空", "");
                    status += " and status_flag='" + status_flag + "' ";
                }


                if (state_text != "")
                {
                    status_flag = status_flag.Replace("未设置", "");
                    status_flag = status_flag.Replace("清空", "");
                    status += " and isnull(state_text,'')='" + state_text + "' ";
                }


                //2019.02.08 增加异常返回，刷已删除作品的链接
                //status = status == "" ? "  and status<>'已完成' and status<>'退单中' " : " and status='" + status + "' ";

                //sql = " select *,(case when code='dy-pldz' then data else '-1' end) as plid from orders where app_id=" + softid.ToString() + " and " + _code + " and status<>'已退单' " + status + " order by createTime ";


                if (string.IsNullOrEmpty(cachetimeStr))
                {
                    //普通的取单方式   
                    sql = " select " + max + " id,transactionID,code,url,source_url,num,startCount,nowCount,status,createTime,(case when code='dy-pldz' or code='dy-plhf' or code='dy-gz' or code='dy-gzm' or code like 'dyzb-%' or code like 'dy-jy%' then data else '-1' end) as plid,rqinfo,api_params,status_flag,uid,state_text from orders with(nolock) where app_id=" + softid.ToString() + " and (speed=1 or status='退单中' or expectNum>nowCount-startCount) " + _code + " and status<>'已退单' " + status + " order by " + (all == "1" ? " (case when status='退单中' then 0 else 1 end),  " : "") + " createTime " + (!string.IsNullOrEmpty(max) ? order : "");
                }
                else
                {
                    if (string.IsNullOrEmpty(max))
                    {
                        dic.Add("code", 10000);
                        dic.Add("msg", "请设置max参数");
                        return JsonMapper.ToJson(dic);
                    }
                    sql = " update orders set uptime='2019-01-01 00:00:00' output deleted.id,deleted.transactionID,deleted.code,deleted.url,deleted.source_url,deleted.num,deleted.startCount,deleted.nowCount,deleted.status,deleted.createTime,(case when deleted.code='dy-pldz' or deleted.code='dy-gz' or deleted.code='dy-gzm' or deleted.code like 'dyzb-%' or deleted.code like 'dy-jy%' then deleted.data else '-1' end) as plid,deleted.api_params,deleted.status_flag,deleted.uid,deleted.state_text  where id in (select " + max + " id from orders with(UPDLOCK) where app_id=" + softid.ToString() + " and (speed=1 or status='退单中' or expectNum>nowCount-startCount) " + _code + " and status<>'已退单' " + status + " order by isnull(uptime,GETDATE()) desc,id desc) ";
                }


                dt = db.getDataTable(sql, parames);
                count = dt.Rows.Count.ToString();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    url = dt.Rows[i]["url"] + "";
                    if (url == "")
                    {
                        url = dt.Rows[i]["source_url"] + "";
                    }
                    plid = dt.Rows[i]["plid"] + "";
                    temp = (dt.Rows[i]["code"] + "").Replace(" ", "");

                    plid = plid.Replace("【补单】", "");
                    plid = plid.Replace("【新订单重下】", "");

                    if (temp == "dy-gz" || temp == "dy-gzm")
                    {
                        url = url.Replace("http://www.iesdouyin.com/share/user/", "");
                        if (!string.IsNullOrEmpty(plid))
                        {
                            tempurl = url;
                            url = plid;
                            plid = tempurl;
                        }
                    }

                    if (temp.IndexOf("dy-jy") != -1)
                    {
                        plid = "jy:" + plid;
                    }
                    //if (temp == "dy-dzm")
                    //{
                    //    temp = "dy-mz";
                    //}
                    status = dt.Rows[i]["status"] + "";
                    if (dyzbdm)
                    {
                        status = "进行中";
                    }
                    dic = new Dictionary<string, object>();
                    dic.Add("orderid", dt.Rows[i]["id"] + "");
                    dic.Add("tid", dt.Rows[i]["transactionID"] + "");
                    dic.Add("code", temp);
                    dic.Add("url", url.Replace("\0", ""));
                    dic.Add("plid", plid.Replace("\0", ""));
                    dic.Add("need", dt.Rows[i]["num"] + "");
                    dic.Add("start", dt.Rows[i]["startCount"] + "");
                    dic.Add("now", dt.Rows[i]["nowCount"] + "");
                    dic.Add("status", status);
                    dic.Add("createAt", dt.Rows[i]["createTime"] + "");
                    dic.Add("rqinfo", dt.Rows[i]["rqinfo"] + "");
                    dic.Add("api_params", dt.Rows[i]["api_params"] + "");
                    dic.Add("status_flag", dt.Rows[i]["status_flag"] + "");
                    dic.Add("user_name", getUserName(dt.Rows[i]["uid"] + ""));
                    dic.Add("state_text", dt.Rows[i]["state_text"] + "");
                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();

                dic.Add("cache_time", DateTime.Now.ToString("yyyy-MM-dd hh:mm:ss"));
                dic.Add("cache_next_time", DateTime.Now.AddSeconds(cachetime).ToString("yyyy-MM-dd hh:mm:ss"));
                dic.Add("total", dt.Rows.Count);
                dic.Add("order", list);

                back = JsonMapper.ToJson(dic);
                //log.WriteLog("api-order", "query", back);
                //if (dt.Rows.Count > 100)
                //{
                cae.SetCache(cache_name, back, DateTime.Now.AddSeconds(cachetime));
                //}
                break;
            case "checkorder":
                cache_name = _code + status + request_type + cachename;
                cache_index = getCacheIndexStr(cache_name);
                if (cache_index == "-1")
                {
                    cache_index = "";
                }

                parames = new SqlParameter[]{
                    new SqlParameter("@code",_code),
                    new SqlParameter("@status",status)
                };

                if (_code != string.Empty)
                {
                    cond += " and code=@code ";
                }
                if (status != string.Empty)
                {
                    cond += " and status=@status ";
                }
                else
                {
                    cond += " and (status='未开始' or status='进行中') ";
                }

                switch (request_type)
                {
                    case "2"://需求数从低到高（优先未开始）
                        order_by = "(case status when '未开始' then 0 else 1 end),num";
                        break;
                    case "3"://需求数从高到低（优先未开始
                        order_by = "(case status when '未开始' then 0 else 1 end),num desc";
                        break;
                    case "5"://订单ID从高到低（不优先）
                        order_by = "createTime desc";
                        cond += cache_index;
                        break;
                    default:
                        order_by = "createTime";
                        request_type = "4";//默认值
                        cond += cache_index;
                        break;
                }

                sql += "select * from"
                    + "("
                    + " select *, ROW_NUMBER() over(partition by code order by " + order_by + " ) as rowNum "
                    + " from orders with(nolock) where app_id=" + softid.ToString() + cond + " "
                    + " ) ranked "
                    + " where ranked.rowNum <=" + MaxTopnum.ToString() + " ";
                //Response.Write(sql);
                //Response.End();
                dt = db.getDataTable(sql, parames);
                count = dt.Rows.Count.ToString();
                leng = dt.Rows.Count;
                for (int i = 0; i < leng; i++)
                {
                    temp3 = dt.Rows[i]["id"] + "";
                    temp = dt.Rows[i]["startCount"] + "";
                    temp = temp == "" ? "-1" : temp;
                    temp2 = (dt.Rows[i]["code"] + "").Replace(" ", "");
                    tempInt = Convert.ToInt64(temp3);

                    dic = new Dictionary<string, object>();
                    dic.Add("orderid", temp3);
                    dic.Add("code", temp2);
                    dic.Add("url", dt.Rows[i]["url"] + "");
                    //dic.Add("plid", dt.Rows[i]["plid"] + "");
                    dic.Add("need", dt.Rows[i]["num"] + "");
                    dic.Add("start", temp);
                    dic.Add("now", dt.Rows[i]["nowCount"] + "");
                    dic.Add("status", dt.Rows[i]["status"] + "");
                    //dic.Add("createAt", dt.Rows[i]["createTime"] + "");
                    list.Add(dic);

                    if (request_type == "4" || request_type == "5")
                    {
                        check_exists = false;
                        for (int x = 0; x < idxTs.Count; x++)
                        {
                            if (idxTs[x].name == temp2)
                            {
                                check_exists = true;
                                idxTs[x].rowsLen += 1;
                                if (request_type == "5")
                                {
                                    //取最小ID
                                    if (idxTs[x].index > tempInt)
                                    {
                                        idxTs[x].index = tempInt;
                                    }
                                }
                                else
                                {
                                    //取最大ID
                                    if (idxTs[x].index < tempInt)
                                    {
                                        idxTs[x].index = tempInt;
                                    }
                                }

                            }
                        }
                        if (!check_exists)
                        {
                            idxT = new indexT();
                            idxT.name = temp2;
                            idxT.index = tempInt;
                            idxT.rowsLen = 1;
                            idxTs.Add(idxT);
                        }
                    }
                }

                end_id = "";
                if (request_type == "4" || request_type == "5")
                {
                    for (int x = 0; x < idxTs.Count; x++)
                    {
                        if (idxTs[x].rowsLen >= MaxTopnum)
                        {
                            end_id += string.Format(" when '{0}' then {1} ", idxTs[x].name, idxTs[x].index);
                        }
                    }

                    if (!string.IsNullOrEmpty(end_id))
                    {
                        if (request_type == "5")
                        {
                            end_id = " and id<(case code " + end_id + " else 999999999 end) ";
                        }
                        else
                        {
                            end_id = " and id>(case code " + end_id + " else -1 end) ";
                        }
                    }
                    setCacheIndexStr(cache_name, end_id);
                }

                dic = new Dictionary<string, object>();
                dic.Add("total", leng);
                dic.Add("order", list);
                dic.Add("next_where", end_id);

                back = JsonMapper.ToJson(dic);


                break;
            case "orderByDate":
                if (!string.IsNullOrEmpty(_date))
                {
                    _code = "dy-dz";
                    parames = new SqlParameter[]{
                        new SqlParameter("@code",_code),
                        new SqlParameter("@date",_date)
                    };
                    status = status == "" ? "  and status<>'已完成' and status<>'订单异常' " : " and status='" + status + "' ";
                    sql = " select * from orders where app_id=" + softid.ToString() + " and code=@code and datediff(day,createTime,@date)=0 and status<>'已退单' " + status;
                    dt = db.getDataTable(sql, parames);
                    count = dt.Rows.Count.ToString();
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dic = new Dictionary<string, object>();
                        dic.Add("orderid", dt.Rows[i]["id"] + "");
                        dic.Add("code", dt.Rows[i]["code"] + "");
                        dic.Add("url", dt.Rows[i]["url"] + "");
                        dic.Add("need", dt.Rows[i]["num"] + "");
                        dic.Add("start", dt.Rows[i]["startCount"] + "");
                        dic.Add("now", dt.Rows[i]["nowCount"] + "");
                        dic.Add("status", dt.Rows[i]["status"] + "");
                        list.Add(dic);
                    }
                }
                dic = new Dictionary<string, object>();

                dic.Add("total", dt.Rows.Count);
                dic.Add("order", list);


                back = JsonMapper.ToJson(dic);
                break;
            case "queryId":

                parames = new SqlParameter[]{
                    new SqlParameter("@id",id)
                };

                sql = " select *,(case when code='dy-pldz' then data else '-1' end) as plid  from orders where app_id=" + softid.ToString() + " and id=@id ";
                dt = db.getDataTable(sql, parames);
                if (dt.Rows.Count > 0)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("code", 0);
                    dic.Add("message", "查询成功");
                    dic.Add("orderid", dt.Rows[0]["id"] + "");
                    dic.Add("appcode", dt.Rows[0]["code"] + "");
                    dic.Add("url", (dt.Rows[0]["url"] + "").Replace("\0", ""));
                    dic.Add("plid", (dt.Rows[0]["plid"] + "").Replace("\0", ""));
                    dic.Add("need", dt.Rows[0]["num"] + "");
                    dic.Add("start", dt.Rows[0]["startCount"] + "");
                    dic.Add("now", dt.Rows[0]["nowCount"] + "");
                    dic.Add("status", dt.Rows[0]["status"] + "");
                    dic.Add("api_params", dt.Rows[0]["api_params"] + "");
                    dic.Add("status_flag", dt.Rows[0]["status_flag"] + "");
                    dic.Add("user_name", getUserName(dt.Rows[0]["uid"] + ""));
                    dic.Add("state_text", dt.Rows[0]["state_text"] + "");

                    back = JsonMapper.ToJson(dic);

                }
                else
                {
                    dic.Add("code", 10966);
                    dic.Add("message", "订单ID不存在");
                    back = JsonMapper.ToJson(dic);
                }

                break;
            case "change":

                id = Request.QueryString["id"] + "";
                startCount = Request.QueryString["start"] + "";
                nowCount = Request.QueryString["now"] + "";


                //if (!globalClass.IsNumeric(startCount) || !globalClass.IsNumeric(nowCount))
                //{
                //    return "{\"code\":10009,\"msg\":\"start和now参数必须是整数类型\"}";
                //}

                if (!globalClass.IsNumeric(startCount))
                {
                    startCount = "";
                }
                if (!globalClass.IsNumeric(nowCount))
                {
                    nowCount = "";
                }


                log.debug("更新", "[" + Request.QueryString["mid"] + "] ID[" + id + "] 初始：" + startCount + ",当前：" + nowCount + ",状态：" + status);

                _code = string.Empty;
                for (int i = 0; i < business_list.Count; i++)
                {
                    _code += _code == "" ? "" : " or ";
                    _code += " code='" + business_list[i] + "' ";
                }
                _code = "(" + _code + ")";

                parames = new SqlParameter[]{
                    new SqlParameter("@id",id),
                    new SqlParameter("@startCount",startCount),
                    new SqlParameter("@nowCount",nowCount),
                    new SqlParameter("@status",status),
                    new SqlParameter("@extra",extra),
                    new SqlParameter("@errtotalnum",errtotalnum),
                    new SqlParameter("@api_params",api_params),
                    new SqlParameter("@state_text",state_text),
                    new SqlParameter("@remark1",remark1),
                    new SqlParameter("@note",note)
                };

                string startCountSql = "(case when @startCount='' or (@startCount>startCount and startCount<>0) then startCount else @startCount end)";
                string nowCountSql = "(case when @nowCount='' or @nowCount<nowCount then nowCount else @nowCount end)";
                //string startCountZbSql = "(case when @nowCount='' or @nowCount<startCount then @nowCount else (case when @startCount='' or (@startCount>startCount and startCount<>0) then startCount else @startCount end) end)";
                string startCountZbSql = "(case when @nowCount<>'' and @nowCount<startCount then @nowCount else (case when @startCount<>'' and startCount=0 then @startCount else startCount end) end)";
                string nowCountZbSql = "(case when @nowCount='' or status='已完成' then nowCount else @nowCount end)";
                string statusSql = "(case when @status='' or status='已完成' then status  when status='退单中' and @status<>'订单异常' then status else @status end)";


                ss["limit_status"] = "and status<>'已退单' and status<>'订单异常'";
                if (Request.QueryString["force"] + "" == "1")
                {
                    startCountSql = "(case when @startCount='' then startCount else @startCount end)";
                    nowCountSql = "(case when @nowCount='' then nowCount else @nowCount end)";
                    statusSql = "(case when @status='' then status  when status='退单中' and @status<>'订单异常' then status else @status end)";
                    ss["limit_status"] = "";
                }


                //拦截sql=========
                string interceptStartCount = "(case when startCount<>0 and @nowCount=1 then {icsql} else {commonsql} end)";
                interceptStartCount = interceptStartCount.Replace("{icsql}", startCountSql).Replace("{commonsql}", startCountZbSql);
                //=========拦截sql


                string common_sql = "(case when code='dyzb-dc' or code='dyzb-bdc' then {zbsql} else {ptsql} end) ";
                startCountSql = common_sql.Replace("{zbsql}", startCountZbSql).Replace("{ptsql}", startCountSql);
                //startCountSql = common_sql.Replace("{zbsql}", interceptStartCount).Replace("{ptsql}", startCountSql);//拦截语句



                string ytb_sql = "(case when CHARINDEX('ytb-',code)=1 then {ytbsql} else {ptsql} end) ";
                nowCountSql = ytb_sql.Replace("{ytbsql}", "@nowCount").Replace("{ptsql}", nowCountSql);


                if (!string.IsNullOrEmpty(nowCount))
                {
                    string nownumCacheId = "nownum_" + id;
                    string nownumCacheIdTime = nownumCacheId + "_time";
                    string nownumCache = (string)cae.GetCache(nownumCacheId);

                    //缓存最新的最高值，如果最高值与最低值上传时间相差1分钟并且低于n值则采用最低当前值的更新规制，同时抹除最高值这个缓存
                    if (string.IsNullOrEmpty(nownumCache) || Convert.ToInt64(nowCount) > Convert.ToInt64(nownumCache))
                    {
                        cae.SetCache(nownumCacheId, nowCount, DateTime.Now.AddMinutes(10));
                        cae.SetCache(nownumCacheIdTime, "1", DateTime.Now.AddSeconds(dyzbCheckNumSecond));
                    }
                    else
                    {
                        //当前值低于缓存值会通过这里
                    }

                    if (string.IsNullOrEmpty((string)cae.GetCache(nownumCacheIdTime)))//!!! 这里好像判断反了
                    {
                        //时间条件成立（当前值低于缓存值才会进入这里）
                        log.WriteLog("DyzbCheckNum", "", string.Format("id:{0},cache_now:{1},now:{2}", id, nownumCache, nowCount));
                        if (Convert.ToInt64(nownumCache) - Convert.ToInt64(nowCount) >= dyzbCheckNumDiffNum)
                        {
                            if (Convert.ToInt64(nownumCache) >= 100 && nowCount == "1")
                            {
                                log.WriteLog("DyzbDontNum", "", string.Format("id:{0},cache_now:{1},now:{2}", id, nownumCache, nowCount));
                            }
                            else
                            {
                                log.WriteLog("DyzbUpdateSuccess", "", string.Format("id:{0},cache_now:{1},now:{2}", id, nownumCache, nowCount));
                                //数量条件成立更换SQL（并且更新缓存）
                                nowCountSql = common_sql.Replace("{zbsql}", nowCountZbSql).Replace("{ptsql}", nowCountSql);
                                cae.SetCache(nownumCacheId, nowCount, DateTime.Now.AddMinutes(10));
                                cae.SetCache(nownumCacheIdTime, "1", DateTime.Now.AddSeconds(dyzbCheckNumSecond));
                            }
                        }
                        else
                        {
                            //条件不成立（最新值比当前值大）
                        }
                    }
                }

                sqltemp = ",";
                sqltemp += ("expectNum=(case when {finishNum}>expectNum then {finishNum} else expectNum end)").Replace("{finishNum}", nowCountSql + "-" + startCountSql);

                if (!string.IsNullOrEmpty(extra))
                {
                    //sqltemp
                    sqltemp += ",data=@extra";
                }

                //sql = " update orders set startCount=" + startCountSql + ",nowCount=" + nowCountSql + ",status=(case when @status='' or status='已完成' then status  when status='退单中' and @status<>'订单异常' then status else @status end)" + sqltemp + " output deleted.* where id=@id and status<>'已退单' and status<>'订单异常' and app_id=" + softid.ToString() + " ";

                sql = string.Empty;
                if (status == "订单异常")
                {
                    sql = " declare @errnum int declare @code varchar(20) ";
                    sql += " update orders set enum=enum+1,@errnum=enum,@code=code where id=@id and status<>'已退单' and status<>'订单异常' if(@errnum is null)begin return end ";

                    sql += " if(@code='hszb-dc')begin set @errtotalnum=3 end ";

                    sql += " if(@errnum+1<@errtotalnum)begin return end ";
                }

                if (status == "已完成")
                {
                    sqltemp += ",finishTime=GETDATE()";
                }
                if (status_flag != "")
                {
                    status_flag = status_flag.Replace("未设置", "");
                    status_flag = status_flag.Replace("清空", "");
                    sqltemp += ",status_flag='" + SafeSql(status_flag) + "'";
                }

                tdic["remark1"] = fz.req("remark1");
                if (tdic["remark1"] + "" != "")
                {
                    tdic["remark1"] = (tdic["remark1"] + "").Replace("未设置", "");
                    tdic["remark1"] = (tdic["remark1"] + "").Replace("清空", "");
                    sqltemp += ",remark1='" + SafeSql(tdic["remark1"] + "") + "'";
                }

                if (api_params != "")
                {
                    sqltemp += ",api_params=replace(replace(@api_params,'清空',''),'未设置','') ";
                }
                if (state_text != "")
                {
                    sqltemp += ",state_text=replace(replace(@state_text,'清空',''),'未设置','') ";
                }
                if (note != "")
                {
                    sqltemp += ",note=replace(replace(@note,'清空',''),'未设置','') ";
                }

                if (status == "其他异常")
                {
                    statusSql = "@status";
                }

                sql += " declare @appid varchar(50) declare @currentPeople int ";
                sql += " update orders set @appid=code,@currentPeople=" + nowCountSql + ",startCount=" + startCountSql + ",nowCount=" + nowCountSql + ",status=" + statusSql + sqltemp + " output deleted.* where id=@id " + ss["limit_status"] + " and app_id=" + softid.ToString() + " ";

                //直播订单更新currentPeople
                sql += " if(@appid='dyzb-dc' or @appid='dyzb-bdc')begin update liveOrders set currentPeople=@currentPeople where sub_orderid=@id end ";


                dt = db.getDataTable(sql, parames);

                // 订单更新 结束 -----------------------------------------------------------------------------


                if (dt.Rows.Count > 0)
                {
                    back = "{\"code\":0,\"msg\":\"订单更改成功\"}";
                    tempstr = dt.Rows[0]["status"] + "";
                    errnum = dt.Rows[0]["enum"] + "";
                    switch (dt.Rows[0]["code"] + "")
                    {
                        case "hszb-dc":
                            errtotalnum = 3;
                            break;
                        default:
                            break;
                    }

                    //记录startCount情况---------------
                    try
                    {
                        if (dt.Rows[0]["code"] + "" == "dy-bf" && startCount == "-1")
                        {
                            log.WriteLog("playOrders", "detail", string.Format("id:{0},start:{1},now:{2},dtStart:{3}", id, startCount, nowCount, dt.Rows[0]["startCount"] + ""));
                        }

                        //"(case when @nowCount='' or @nowCount<startCount then @nowCount else (case when @startCount='' or (@startCount>startCount and startCount<>0) then startCount else @startCount end) end)";
                        if (nowCount == "")
                        {
                            log.WriteLog("start_record", "now_empty", string.Format("id:{0},start:{1},now:{2},up_start:{3}", id, dt.Rows[0]["startCount"] + "", nowCount, startCount));
                        }
                        else
                        {
                            if (Convert.ToInt64(nowCount) < Convert.ToInt64(dt.Rows[0]["startCount"] + ""))
                            {
                                log.WriteLog("start_record", "change", string.Format("id:{0},start:{1},now:{2},up_start:{3}", id, dt.Rows[0]["startCount"] + "", nowCount, startCount));
                            }
                        }
                    }
                    catch (Exception)
                    {
                    }
                    //---------------记录startCount情况
                    if ((status == "订单异常" && Convert.ToInt16(errnum) + 1 >= errtotalnum) || status == "退单中" || tempstr == "退单中")
                    {
                        log.WriteLog("退单", "api", "id:" + id + " start:" + dt.Rows[0]["startCount"] + " now:" + dt.Rows[0]["nowCount"] + "|info:[oStatus:" + tempstr + ",upStatus:" + status + "]");
                        temp = "客户_申请";
                        if (status == "退单中" || status == "订单异常")//软件更新上来的状态
                        {
                            if (tempstr == "进行中" || tempstr == "未开始")//原有状态（是否正常运行中）
                            {
                                temp = "自动_后台";
                            }
                        }

                        //退单
                        parames = new SqlParameter[]{
                            new SqlParameter("@orderid",id),
                            new SqlParameter("@name",temp)
                        };
                        sql = " [tuidan] @orderid,@name ";

                        db.ExecuteNonQuery(sql, parames);

                    }

                }
                else
                {
                    back = "{\"code\":10003,\"msg\":\"此订单不存在或不允许修改\"}";
                }
                break;
            case "apidata":

                _code = string.Empty;
                for (int i = 0; i < business_list.Count; i++)
                {
                    _code += _code == "" ? "" : " or ";
                    _code += " code='" + business_list[i] + "' ";
                }
                _code = "(" + _code + ")";
                parames = new SqlParameter[]{
                    new SqlParameter("@id",id)
                };
                sql += " select * from [pldata] with(nolock) where orderid=@id ";
                sql += " if(@@ROWCOUNT=0)begin select code,data from [orders] with(nolock) where  id=@id and app_id=" + softid.ToString() + " end ";


                sql = " select code,data from [orders] with(nolock) where  id=@id and app_id=" + softid.ToString();
                sql += " select data from c_comment with(nolock) where name='通用2'  ";
                sql += " select data from c_comment with(nolock) where name='其他_通用回复'  ";

                ds = db.getDataSet(sql, parames);
                dt = ds.Tables[0];

                if (dt.Rows.Count > 0)
                {
                    temp = dt.Rows[0]["code"] + "";

                    if (temp.IndexOf("-plzdy") != -1 || temp.IndexOf("-dmzdy") != -1)
                    {
                        temp = dt.Rows[0]["data"] + "";
                        temp = temp == "" || temp == "[\"\"]" ? "[]" : temp;

                        if (temp == "[]")
                        {
                            dt = ds.Tables[1];
                            if (dt.Rows.Count > 0)
                            {
                                for (int i = 0; i < dt.Rows.Count; i++)
                                {
                                    ls.Add(dt.Rows[i][0] + "");
                                }
                                temp = JsonMapper.ToJson(ls);
                            }
                        }

                        back = "{\"code\":0,\"msg\":\"获取成功\",\"data\":" + temp + "}";
                    }
                    else if (temp.IndexOf("-flpl") != -1 || temp.IndexOf("-zf") != -1)
                    {
                        parames = new SqlParameter[]{
                            new SqlParameter("@id",id)
                        };
                        sql = " select data from c_comment with(nolock) where name=(select cast(data as varchar(50)) from orders with(nolock) where  id=@id and app_id=" + softid.ToString() + ")  ";
                        dt = db.getDataTable(sql, parames);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                ls.Add(dt.Rows[i][0] + "");
                            }
                            temp = JsonMapper.ToJson(ls);

                            if (temp == "[]" || temp == "[\"\"]")
                            {
                                ls = new List<string>();
                                dt = ds.Tables[1];
                                if (dt.Rows.Count > 0)
                                {
                                    for (int i = 0; i < dt.Rows.Count; i++)
                                    {
                                        ls.Add(dt.Rows[i][0] + "");
                                    }
                                    temp = JsonMapper.ToJson(ls);
                                }
                            }

                            back = "{\"code\":0,\"msg\":\"获取成功\",\"data\":" + temp + "}";
                        }
                        else
                        {
                            back = "{\"code\":10003,\"msg\":\"订单不存在\"}";
                        }
                    }
                    else if (temp.IndexOf("-pl") != -1)
                    {
                        if (temp == "dy-plhf")
                        {
                            dt = ds.Tables[2];
                        }
                        else
                        {
                            dt = ds.Tables[1];
                        }
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                ls.Add(dt.Rows[i][0] + "");
                            }
                            temp = JsonMapper.ToJson(ls);
                            back = "{\"code\":0,\"msg\":\"获取成功\",\"data\":" + temp + "}";
                        }
                        else
                        {
                            back = "{\"code\":10003,\"msg\":\"订单不存在\"}";
                        }
                    }
                    else
                    {
                        back = "{\"code\":10009,\"msg\":\"此订单没有评论信息\"}";
                    }
                }
                else
                {
                    back = "{\"code\":10003,\"msg\":\"订单不存在\"}";
                }



                //parames = new SqlParameter[]{
                //    new SqlParameter("@id",id)
                //};
                //sql = " select data from orders where id=@id and app_id=" + softid.ToString() + " and " + _code + "  ";
                //dt = db.getDataTable(sql, parames);
                //if (dt.Rows.Count > 0)
                //{
                //    temp = dt.Rows[0]["data"] + "";
                //    temp = temp == "" ? "[]" : temp;
                //    back = "{\"code\":0,\"msg\":\"获取成功\",\"data\":" + temp + "}";
                //}
                //else
                //{
                //    back = "{\"code\":10003,\"msg\":\"订单不存在\"}";
                //}
                break;
            case "apidata_fl":

                _code = string.Empty;
                for (int i = 0; i < business_list.Count; i++)
                {
                    _code += _code == "" ? "" : " or ";
                    _code += " code='" + business_list[i] + "' ";
                }
                _code = "(" + _code + ")";

                parames = new SqlParameter[]{
                    new SqlParameter("@id",id),
                    new SqlParameter("@startCount",startCount),
                    new SqlParameter("@nowCount",nowCount),
                    new SqlParameter("@status",status)
                };

                sql = " select data from c_comment where name=(select cast(data as varchar(50)) from orders where  id=@id and app_id=" + softid.ToString() + " and " + _code + ")  ";
                dt = db.getDataTable(sql, parames);
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        ls.Add(dt.Rows[i][0] + "");
                    }
                    temp = JsonMapper.ToJson(ls);
                    back = "{\"code\":0,\"msg\":\"获取成功\",\"data\":" + temp + "}";
                }
                else
                {
                    back = "{\"code\":10003,\"msg\":\"订单不存在\"}";
                }
                break;
            case "check_zhibo_task":
                back = (string)cae.GetCache(cache_name);
                if (!string.IsNullOrEmpty(back))
                {
                    return back;
                }
                _code = Request.QueryString["code"] + "";

                parames = new SqlParameter[]{
                    new SqlParameter("@code",_code)
                };
                if (!string.IsNullOrEmpty(_code))
                {
                    temp = " and replace(replace(code,'bt',''),'bz','')=@code ";
                }
                sql = " select id,code,url,num,data from orders with(nolock) where expireTime>GETDATE() and order_type=988 and status='已完成' " + temp;
                dt = db.getDataTable(sql, parames);
                for (int i = 0; i < dt.Rows.Count; i++)
                {

                    temp = dt.Rows[i]["code"] + "";
                    temp = temp.Replace("bt", "");
                    temp = temp.Replace("bz", "");

                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["id"] + "");
                    dic.Add("code", temp);
                    dic.Add("url", dt.Rows[i]["url"] + "");
                    dic.Add("num", dt.Rows[i]["num"] + "");
                    dic.Add("plid", dt.Rows[i]["data"] + "");
                    dic.Add("resource_code", dt.Rows[i]["code"] + "");
                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("code", 1);
                dic.Add("msg", "");
                dic.Add("total", dt.Rows.Count);
                dic.Add("order", list);
                back = JsonMapper.ToJson(dic);
                cae.SetCache(cache_name, back, DateTime.Now.AddSeconds(3));
                break;
            case "douyin_zhibo_check":
                back = (string)cae.GetCache(cache_name);
                if (!string.IsNullOrEmpty(back))
                {
                    return back;
                }

                //parames = new SqlParameter[]{
                //    new SqlParameter("@order_type",order_type)
                //};

                switch (order_type)
                {
                    case "huoshan":
                        order_type = "3";
                        break;
                    default:
                        order_type = "1,2,4,5";
                        break;
                }


                //sql = " select MIN(id) as orderid,MIN(online) as online,MIN(liveid) as liveid,url,COUNT(0) as url_num,SUM(renqi_num) as renqi_num from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10) group by url ";
                //sql = " select id as orderid,url,liveid,online from [liveOrders] with(nolock) where id in(select MIN(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type=@order_type and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10) group by url) ";

                //sql = " select id as orderid,order_type,url,liveid,online from [liveOrders] with(nolock) where id in(select MAX(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type in (" + order_type + ") and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10) group by url,order_type) ";

                string task_mode = Request.QueryString["mode"] + "";
                string task_intelval = uConfig.stcdata("check_diff_minute");
                string task_num = Request.QueryString["num"] + "";
                temp = ",o.status";
                temp2 = "";
                if (string.IsNullOrEmpty(task_num) || !IsNumeric(task_num))
                {
                    task_intelval = "0";
                    //temp = ",(case when ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10 then o.status else '等待中' end) as status";
                }
                else
                {
                    temp2 = " top " + task_num;
                }

                //sql = " select " + temp2 + " lo.id as orderid,lo.order_type,lo.url,lo.liveid,lo.online,o.num,o.startCount,(o.startCount+o.num) as finishNum" + temp + ",lo.budan_num from [liveOrders] lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id where lo.id in(select MAX(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type in (" + order_type + ") and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=" + task_intelval + ") group by url,(case when order_type=1 or order_type=2 or order_type=4 or order_type=5 then 1 else order_type end)) ";

                //num参数改为总rq
                sql = " select " + temp2 + " lo.id as orderid,lo.order_type,lo.url,lo.liveid,lo.online,rqo.total_renqi as num,o.startCount,(o.startCount+rqo.total_renqi) as finishNum,lo.currentPeople" + temp + ",lo.budan_num from [liveOrders] lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id left join (SELECT url,SUM(renqi_num) as total_renqi FROM [liveOrders] with(nolock) where DATEADD(hour,total_day,createTime)>GETDATE() and status=0 group by url)rqo on rqo.url=lo.url where lo.id in(select MAX(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type in (" + order_type + ") and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=" + task_intelval + ") group by url,(case when order_type=1 or order_type=2 or order_type=4 or order_type=5 then 1 else order_type end)) ";
                dt = db.getDataTable(sql);

                temp = Request.QueryString["second"] + "";
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    int second = dateDiff(DateTime.Now, idExpireTime(dt.Rows[i]["orderid"] + ""));
                    if (temp == "1" && second == 0)
                    {
                        continue;
                    }

                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["orderid"] + "");
                    dic.Add("url", dt.Rows[i]["url"] + "");
                    dic.Add("liveid", (dt.Rows[i]["liveid"] + "").Replace("【补单】", "").Replace("【新订单重下】", ""));
                    dic.Add("online", dt.Rows[i]["online"] + "");
                    dic.Add("order_type", dt.Rows[i]["order_type"] + "");
                    dic.Add("num", dt.Rows[i]["num"] + "");
                    dic.Add("startCount", dt.Rows[i]["startCount"] + "");
                    dic.Add("finishNum", dt.Rows[i]["finishNum"] + "");
                    dic.Add("currentPeople", dt.Rows[i]["currentPeople"] + "");
                    dic.Add("budan_num", dt.Rows[i]["budan_num"] + "");
                    dic.Add("status", dt.Rows[i]["status"] + "");
                    dic.Add("second", second);
                    //dic.Add("group_num", dt.Rows[i]["url_num"] + "");
                    list.Add(dic);
                }
                if (task_mode == "1")
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("total", dt.Rows.Count);
                    dic.Add("list", list);
                    back = JsonMapper.ToJson(dic);
                }
                else
                {
                    back = JsonMapper.ToJson(list);
                }

                //cae.SetCache(cache_name, back, DateTime.Now.AddSeconds(2));
                break;
            case "upload_online_status":
                #region upload_online_status
                string create_type = Request.QueryString["create_type"] + "";
                string sql_param = "";
                string update_sqlParam = "";

                orderid = Request.QueryString["id"] + "";
                parames = new SqlParameter[]{
                    new SqlParameter("@id",orderid),
                    new SqlParameter("@online",Request.QueryString["online"]+""),
                    new SqlParameter("@liveid",Request.QueryString["liveid"]+""),
                    new SqlParameter("@currentPeople",Request.QueryString["currentPeople"]+"")
                };
                //sql = " update liveOrders set task_time=GETDATE(),online=@online output deleted.* where orderid=@orderid and status=0 and DATEADD(DAY,total_day,createTime)>GETDATE() and ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10 and online=(case when @online=1 then 0 else 1 end) ";

                sql_param = " and ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=0 and online=(case when @online=1 then 0 else 1 end) ";
                if (create_type == "budan")
                {
                    sql_param = " and ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=" + dyzbBcMinute + " and budan_num<" + (dyzbBdNum + 1) + " ";//补单间隔（dyzbBcMinute分钟） 补单次数（dyzbBdNum次）
                    update_sqlParam = ",budan_num=budan_num+1";
                }

                if (Request.QueryString["online"] + "" == "0")
                {
                    update_sqlParam = ",budan_num=0";
                }

                if (Request.QueryString["currentPeople"] + "" != "")
                {
                    //更新当前值
                    update_sqlParam += ",currentPeople=@currentPeople";
                }

                if (Request.QueryString["currentPeople"] + "" == "0")
                {
                    log.WriteLog("直播_currentPeople为0", orderid, Request.QueryString.ToString());
                }

                temp = "task_time=GETDATE(),online=@online,liveid=@liveid";
                if (create_type == "update_current")
                {
                    temp = string.Empty;//仅更新数量不做其他操作
                    sql_param = string.Empty;
                    update_sqlParam = "currentPeople=@currentPeople";
                }
                else
                {
                    try
                    {
                        if (Request.QueryString["liveid"] + "" == "")
                        {
                            log.WriteLog("直播_LiveId为空", "状态_" + (Request.QueryString["online"] + "" == "1" ? "开播" : "下播") + "_" + orderid, Request.QueryString.ToString());
                        }
                    }
                    catch (Exception)
                    {

                    }
                }

                sql += " declare @order_type int ";
                sql += " declare @table table(id int,order_type int,code varchar(20),status varchar(20),liveid varchar(50),orderid int,url varchar(500),renqi_num int,online int,startCount int,num int) declare @url varchar(500) declare @orderid int ";
                sql += " select @url=url,@order_type=order_type,@orderid=orderid from liveOrders with(nolock) where id=@id and status=0 and DATEADD(hour,total_day,createTime)>GETDATE() " + sql_param;
                sql += " if(@url='')begin return end ";
                sql += " insert into @table select lo.id,lo.order_type,lo.appid,lo.status,lo.liveid,lo.orderid,lo.url,lo.renqi_num,lo.online,o.startCount,o.num from liveOrders lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id where DATEADD(hour,lo.total_day,lo.createTime)>GETDATE() and ISNULL(DATEDIFF(minute,lo.task_time,GETDATE()),999)>=0 and lo.status=0 and lo.url=@url and (case when @order_type=1 or @order_type=2 or @order_type=4 or @order_type=5 then @order_type else lo.order_type end)=@order_type  ";
                sql += " update liveOrders set " + temp + update_sqlParam + " where id in (select id from @table) ";
                sql += " select * from @table ";
                //return sql;

                if (create_type != "update_current")
                {
                    sql += " if(@@ROWCOUNT>0)begin  update orders set status='进行中',data=@liveid where id in (select orderid from @table) and (status='进行中' or status='未开始') end ";
                }

                temp = string.Empty;

                try
                {
                    dt = db.getDataTable(sql, parames);
                }
                catch (Exception ex)
                {
                    if (Request.QueryString["online"] + "" == "1")
                    {
                        for (int i = 0; i < 5; i++)
                        {
                            parames = new SqlParameter[]{
                                new SqlParameter("@id",orderid)
                            };
                            sql = " update liveOrders set online=0 where id=@id ";
                            try
                            {
                                db.ExecuteNonQuery(sql, parames);
                                temp = "success";
                                break;
                            }
                            catch (Exception)
                            {
                                temp = "error";
                            }
                        }
                    }

                    try
                    {
                        temp2 = dt.Rows[0]["url"] + "";
                    }
                    catch (Exception)
                    {

                    }
                    log.WriteLog("直播_开播异常", "SQL_ERROR", "url：" + temp2 + ",result：" + temp + ",error:" + ex.Message.ToString());
                    temp2 = string.Empty;
                    dt = new DataTable();

                }

                if (create_type == "update_current")
                {
                    return "{\"code\":0,\"msg\":\"success\",\"type\":\"update_num_" + dt.Rows.Count + "\"}";
                }

                if (dt.Rows.Count > 0)
                {

                    //先进行老订单退单
                    if (IsNumeric(orderid) && Convert.ToInt64(orderid) > 0)
                    {
                        //之前在刷的订单全退单

                        string liveids = string.Empty;

                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            temp += temp == "" ? "" : ",";
                            temp += dt.Rows[i]["orderid"] + "";


                            liveids += liveids == "" ? "" : ",";
                            liveids += dt.Rows[i]["orderid"] + "-" + dt.Rows[i]["liveid"] + "-" + dt.Rows[i]["code"];
                        }

                        log.WriteLog("直播状态_更新", orderid, "zt：" + (Request.QueryString["online"] + "" == "1" ? "开播" : "下播") + ",liveid:" + Request.QueryString["liveid"] + ",create_type:" + create_type + ",liveids:" + liveids);


                        //下播批量退单
                        log.WriteLog("直播批量退单", "状态_" + (Request.QueryString["online"] + "" == "1" ? "开播" : "下播"), temp);


                        sql = " update orders set status='退单中',refundTime=GETDATE() output deleted.* where father_id in (" + temp + ") and (status='未开始' or status='进行中' or status='退单中') ";
                        //sql = " select * from orders with(nolock) where father_id=@orderid and (status='未开始' or status='进行中') ";
                        try
                        {
                            dt2 = db.getDataTable(sql);
                            if (dt2.Rows.Count < 10)//防止搞错退平台订单
                            {
                                sql = string.Empty;
                                for (int i = 0; i < dt2.Rows.Count; i++)
                                {
                                    sql += " exec('[tuidan] " + dt2.Rows[i]["id"] + ",''直播_后台''') ";
                                }
                                if (!string.IsNullOrEmpty(sql))
                                {
                                    res = db.ExecuteNonQuery(sql);
                                }
                            }
                        }
                        catch (Exception)
                        {
                            log.WriteLog("直播批量退单_异常", "状态_" + (Request.QueryString["online"] + "" == "1" ? "开播" : "下播"), temp);
                        }
                    }


                    unified unif = new unified();
                    if (Request.QueryString["online"] + "" == "1")
                    {
                        orderid = dt.Rows[0]["orderid"] + "";
                        order_type = dt.Rows[0]["order_type"] + "";

                        res = 0;

                        bool lastIsOnlie = false;
                        string currentPeople = Request.QueryString["currentPeople"] + "";
                        string onlineStartPeople = "";
                        string startPeople = currentPeople;
                        string budan_stauts = "";
                        Int64 totalPeople = 0;
                        string appId = string.Empty;

                        //多订单情况下（包括上榜1、不上榜2,高级上榜4,特价5）优先级：高级上榜>上榜>特价>不上榜
                        string[] ranklist = { "2", "5", "1", "4" };
                        int rankindex = 0;

                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            appId = dt.Rows[i]["code"] + "";
                            yzid += dt.Rows[i]["id"] + ",";
                            totalPeople += Convert.ToInt64(dt.Rows[i]["renqi_num"] + "");

                            for (int r = 0; r < ranklist.Length; r++)
                            {
                                if (ranklist[r] == dt.Rows[i]["order_type"] + "")
                                {
                                    if (r > rankindex)
                                    {
                                        rankindex = r;
                                        order_type = dt.Rows[i]["order_type"] + "";
                                    }
                                    break;
                                }
                            }

                            //if (order_type != "4" && dt.Rows[i]["order_type"] + "" == "1")
                            //{
                            //    order_type = dt.Rows[i]["order_type"] + "";
                            //}

                            //if (dt.Rows[i]["order_type"] + "" == "4")
                            //{
                            //    order_type = dt.Rows[i]["order_type"] + "";
                            //}

                            if (dt.Rows[i]["online"] + "" == "1")
                            {
                                lastIsOnlie = true;
                                onlineStartPeople = dt.Rows[i]["startCount"] + "";
                            }
                        }
                        yzid = yzid.Trim(',');
                        temp2 = totalPeople.ToString();

                        if (create_type == "budan")
                        {
                            budan_stauts = "【补单】";
                            lastIsOnlie = true;
                        }
                        if (lastIsOnlie)
                        {
                            if (budan_stauts == "")
                            {
                                budan_stauts = "【新订单重下】";
                            }
                            //之前已开播过（初始值采用老初始值）
                            startPeople = onlineStartPeople;
                        }
                        //else
                        //{
                        //    //订单到期1小时内，也可以采用之前的初始值
                        //    try
                        //    {

                        //        parames = new SqlParameter[]{
                        //            new SqlParameter("@id",Request.QueryString["id"] + ""),
                        //            new SqlParameter("@liveid",Request.QueryString["liveid"]+"")
                        //        };

                        //        sql = string.Empty;
                        //        sql += " declare @order_type int  declare @url varchar(500) declare @orderid int ";
                        //        sql += " select @url=url,@order_type=order_type,@orderid=orderid from liveOrders with(nolock) where id=@id and status=0 and DATEADD(DAY,total_day,createTime)>GETDATE() ";
                        //        sql += " if(@url='')begin return end ";
                        //        sql += " select top 1 lo.id,lo.order_type,lo.appid,lo.status,lo.liveid,lo.orderid,lo.url,lo.renqi_num,lo.online,o.startCount,o.num from liveOrders lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id where DATEADD(hour,lo.total_day,lo.createTime)>DATEADD(minute,-30,GETDATE()) and lo.url=@url and lo.online=1 and lo.liveid=@liveid and (case when @order_type=1 or @order_type=2 or @order_type=4 or @order_type=5 then @order_type else lo.order_type end)=@order_type and liveid=@liveid order by DATEADD(hour,lo.total_day,lo.createTime) desc  ";
                        //        DataTable liveExpireOrders = db.getDataTable(sql, parames);
                        //        string tempPeople = string.Empty;
                        //        if (liveExpireOrders.Rows.Count > 0)
                        //        {
                        //            tempPeople = liveExpireOrders.Rows[0]["startCount"] + "";
                        //            log.WriteLog("直播_延用老订单初始值_检测", liveExpireOrders.Rows[0]["id"] + "", string.Format("单次订单ID：{0}  原初始值：{1} > 新初始值：{2}", liveExpireOrders.Rows[0]["orderid"] + "", startPeople, tempPeople));
                        //            if (Convert.ToInt64(startPeople) > Convert.ToInt64(tempPeople))
                        //            {
                        //                log.WriteLog("直播_延用老订单初始值", liveExpireOrders.Rows[0]["id"] + "", string.Format("单次订单ID：{0}  原初始值：{1} > 新初始值：{2}", liveExpireOrders.Rows[0]["sub_orderid"] + "", startPeople, tempPeople));
                        //                startPeople = tempPeople;
                        //            }
                        //        }
                        //        else
                        //        {
                        //            log.WriteLog("直播_无到期一小时订单", Request.QueryString["id"] + "", orderid + " > " + Request.QueryString["liveid"] + " > " + string.Format("初始值：{0}", startPeople));
                        //        }
                        //    }
                        //    catch (Exception ex)
                        //    {
                        //        log.WriteLog("直播_延用老订单初始值_出错", Request.QueryString["id"] + "", orderid + " > " + Request.QueryString["liveid"] + " > " + ex.Message.ToString());
                        //    }
                        //}

                        if (string.IsNullOrEmpty(startPeople))
                        {
                            log.WriteLog("直播_初始值错误", "data", "url：" + dt.Rows[0]["url"] + ",yzid：" + yzid + ",startPeople：" + startPeople + ",currentPeople：" + currentPeople + ",info：" + ToJson(dt));
                        }

                        _code = "";
                        switch (order_type)
                        {
                            case "1":
                                _code = "dyzb-dc";
                                break;
                            case "2":
                                _code = "dyzb-bdc";
                                break;
                            case "3":
                                _code = "hszb-dc";
                                break;
                            case "4":
                                _code = "dyzb-gjdc";
                                break;
                            case "5":
                                _code = "dyzb-bdc";
                                break;
                            default:
                                break;
                        }
                        string rqinfo = string.Empty;
                        if (!string.IsNullOrEmpty(_code))
                        {
                            if (appId.IndexOf("ms") != -1)
                            {
                                rqinfo = "ms";
                            }
                        }

                        string order_num = temp2;
                        string remark = string.Empty;
                        try
                        {
                            ////临时限制最高下单6w
                            //if (Convert.ToInt64(order_num) > 60000)
                            //{
                            //    order_num = "60000";
                            //}


                            //判断延续的初始值是否高于当前值，如果高于当前值那么初始值应该改为当前值
                            try
                            {
                                if (Convert.ToInt64(startPeople) > Convert.ToInt64(currentPeople))
                                {
                                    remark = "[初始值有变] 原初始值：" + startPeople;
                                    startPeople = currentPeople;
                                }
                            }
                            catch (Exception)
                            {
                            }


                            temp = unif.OrderCreate("1", "fromcode_" + _code, "", dt.Rows[0]["url"] + "", order_num, Request.QueryString["liveid"] + "" + budan_stauts, yzid, "startnum_" + startPeople, "", "ROBOT", orderid, currentPeople, "", rqinfo);
                        }
                        catch (Exception ex)
                        {
                            temp = "ERROR：" + ex.Message.ToString();
                        }

                        //开播
                        log.WriteLog("直播_下单结果", "result", "url：" + dt.Rows[0]["url"] + ",code：" + _code + ",num：" + order_num + ",realnum：" + temp2 + ",yzid：" + yzid + ",startPeople：" + startPeople + ",currentPeople：" + currentPeople + ",liveid：" + Request.QueryString["liveid"] + ",create_type：" + create_type + ",budan_stauts：" + budan_stauts + "" + ",result：" + temp + " " + remark);
                    }
                    else
                    {
                        //下播（前面已退单无需再处理）
                    }
                    back = "{\"code\":0,\"msg\":\"success\"}";
                }
                else
                {
                    log.WriteLog("直播_状态更改失败", "result", "create_type：" + create_type + ",id：" + orderid + ",online：" + Request.QueryString["online"] + ",liveid：" + Request.QueryString["liveid"]);

                    back = "{\"code\":10099,\"msg\":\"订单状态更新失败\"}";
                }
                #endregion
                break;
            case "douyin_zhibo_check_time":
                cache_name += "_" + getUserIP(HttpContext.Current).Replace(".", "_");
                back = (string)cae.GetCache(cache_name);
                //if (!string.IsNullOrEmpty(back))
                //{
                //    return back;
                //}
                switch (order_type)
                {
                    case "huoshan":
                        order_type = "3";
                        break;
                    default:
                        order_type = "1,2,4,5";
                        break;
                }

                string topnum = Request.QueryString["num"] + "";
                int checkWithTime = 50;
                DataTable ldt = new DataTable();
                DataTable cdt = new DataTable();
                if (!IsNumeric(topnum))
                {
                    topnum = "1";
                }


                //sql = " declare @tb table(id int) ";
                //sql += " insert into @tb select top " + topnum + " MAX(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type in (" + order_type + ") and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10) and isnull(checkTime,GETDATE())<=GETDATE() group by url,(case when order_type=1 or order_type=2 or order_type=4 or order_type=5 then 1 else order_type end) ";
                //sql += string.Format(" update liveOrders set checkTime=(case when isnull(checkTime,GETDATE())<=GETDATE() then dateadd(second,{0},GETDATE()) else checkTime end) output deleted.id,(case when isnull(deleted.checkTime,GETDATE())<=GETDATE() then 1 else 0 end) need_check,isnull(deleted.checkTime,GETDATE()) as checkTime where id in(select id from @tb) ", checkWithTime.ToString());
                //sql += " select lo.id as orderid,lo.order_type,lo.url,lo.liveid,lo.online,o.num,o.startCount,(o.startCount+o.num) as finishNum,o.status,lo.budan_num from [liveOrders] lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id where lo.id in(select id from @tb) order by checkTime ";
                //ds = db.getDataSet(sql);

                //ldt = ds.Tables[0];
                //ldt = selectDateTable(ldt, "need_check=1");

                //dt = ds.Tables[1];

                try
                {
                    temp = (string)cae.GetCache("order_cache_second");
                }
                catch (Exception)
                {
                }
                if (string.IsNullOrEmpty(temp))
                {
                    cae.RemoteCache("douyin_zhibo_task");
                }
                try
                {
                    dt = (DataTable)cae.GetCache("douyin_zhibo_task");
                }
                catch (Exception)
                {
                    dt = new DataTable();
                }

                string check_diff_minute = uConfig.stcdata("check_diff_minute");

                if (dt == null || dt.Rows.Count == 0)
                {
                    //sql = " select lo.id as orderid,lo.order_type,lo.url,lo.liveid,lo.online,o.num,o.startCount,(o.startCount+o.num) as finishNum,o.status,lo.budan_num,GETDATE() as checkTime from [liveOrders] lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id where lo.id in(select MAX(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type in (" + order_type + ") and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=10) group by url,(case when order_type=1 or order_type=2 or order_type=4 or order_type=5 then 1 else order_type end)) ";

                    //1020将num参数改成实际rq数量
                    sql = " select lo.id as orderid,lo.order_type,lo.url,lo.liveid,lo.online,rqo.total_renqi as num,o.startCount,(o.startCount+rqo.total_renqi) as finishNum,lo.currentPeople,o.status,lo.budan_num,GETDATE() as checkTime from [liveOrders] lo with(nolock) left join orders o with(nolock) on lo.sub_orderid=o.id left join (SELECT url,SUM(renqi_num) as total_renqi FROM [liveOrders] with(nolock) where DATEADD(hour,total_day,createTime)>GETDATE()  and status=0 group by url)rqo on rqo.url=lo.url where lo.id in(select MAX(id) from [liveOrders] with(nolock) where status=0 and DATEADD(hour,total_day,createTime)>GETDATE() and order_type in (" + order_type + ") and (online=1 or ISNULL(DATEDIFF(minute,task_time,GETDATE()),999)>=" + check_diff_minute + ") group by url,(case when order_type=1 or order_type=2 or order_type=4 or order_type=5 then 1 else order_type end)) ";
                    dt = db.getDataTable(sql);

                    //Response.Write("aaanum----" + dt.Rows.Count);


                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i]["checkTime"] = idExpireTime(dt.Rows[i]["orderid"] + "", 1);
                    }
                    dt.DefaultView.Sort = " checkTime asc ";
                    dt = dt.DefaultView.ToTable();


                    putLog("[重载缓存表]-------------------" + dt.Rows.Count);

                    cae.SetCache("order_cache_second", "1", DateTime.Now.AddSeconds(10));//10秒后到期
                }

                List<int> RemoveAtList = new List<int>();
                int index = 0;
                string tqids = "";
                string rmids = "";
                try
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        DataRow _order = dt.Rows[i];

                        string order_id = _order["orderid"] + "";
                        string order_url = _order["url"] + "";
                        string order_liveid = (_order["liveid"] + "").Replace("【补单】", "").Replace("【新订单重下】", "");

                        temp = "cache_liveid_" + order_id;
                        if (DateTime.Now < idExpireTime(order_id))
                        {
                            if (cae.GetCache(temp + "_api") != null)
                            {
                                //这是是软件API提交上来的时间（要删除掉防止卡单）
                                RemoveAtList.Add(i);
                                rmids += order_id + ",";
                            }
                            continue;
                        }

                        index++;
                        if (index > Convert.ToInt16(topnum))
                        {
                            break;
                        }
                        cae.SetCache(temp, DateTime.Now.AddSeconds(checkWithTime));
                        RemoveAtList.Add(i);


                        tqids += order_id + ",";


                        dic = new Dictionary<string, object>();
                        dic.Add("id", order_id);
                        dic.Add("url", order_url);
                        dic.Add("liveid", order_liveid);
                        dic.Add("online", _order["online"] + "");
                        //dic.Add("order_type", _order["order_type"] + "");
                        dic.Add("order_type", 2);
                        dic.Add("num", _order["num"] + "");
                        dic.Add("startCount", _order["startCount"] + "");
                        dic.Add("finishNum", _order["finishNum"] + "");
                        dic.Add("currentPeople", _order["currentPeople"] + "");
                        dic.Add("budan_num", _order["budan_num"] + "");
                        dic.Add("status", _order["status"] + "");
                        //dic.Add("group_num", dt.Rows[i]["url_num"] + "");
                        list.Add(dic);


                        //log.WriteLog("检测订单_输出", order_id, "url:" + order_url + ",liveid:" + order_liveid);

                    }
                }
                catch (Exception ex)
                {
                    log.WriteLog("操作报错", "读取dt", "num：" + dt.Rows.Count + ",error：" + ex.Message.ToString());
                }
                for (int i = RemoveAtList.Count - 1; i >= 0; i--)
                {
                    dt.Rows.RemoveAt(RemoveAtList[i]);
                }
                cae.SetCache("douyin_zhibo_task", dt);//10秒后到期

                back = JsonMapper.ToJson(list);

                //back = dt.Rows.Count + "|" + back;

                //cae.SetCache(cache_name, back, DateTime.Now.AddSeconds(0.1));

                //putLog((string.IsNullOrEmpty(tqids) ? "" : "[提取]" + tqids) + "[表剩余]" + dt.Rows.Count + "[ip]" + getUserIP(HttpContext.Current));
                break;
            case "update_check_time":
                temp = "cache_liveid_" + Request.QueryString["id"];
                string time = Request.QueryString["time"] + "";
                if (!IsNumeric(time))
                {
                    time = "500";
                }

                //先获API叠加时间的，再获取系统默认叠加的（例如50S）
                DateTime expireTime = idExpireTime(Request.QueryString["id"] + "");

                expireTime = expireTime.AddSeconds(Convert.ToInt16(time));
                cae.SetCache(temp, expireTime, expireTime);
                back = "{\"code\":0,\"msg\":\"success\",\"expireTime\":\"" + expireTime.ToString("yyyy-MM-dd hh:mm:ss") + "\"}";
                break;
            case "douyin_id_expireTime":
                temp = "cache_liveid_" + Request.QueryString["id"];
                DateTime tempExpire = idExpireTime(Request.QueryString["id"] + "");

                dic = new Dictionary<string, object>();
                dic.Add("code", 0);
                dic.Add("msg", "success");
                dic.Add("expireTime", tempExpire.ToString("yyyy-MM-dd HH:mm:ss"));
                dic.Add("id", Request.QueryString["id"] + "");
                dic.Add("second", dateDiff(DateTime.Now, tempExpire));
                back = JsonMapper.ToJson(dic);
                break;
            case "cache_douyin_dt":
                try
                {
                    dt = (DataTable)cae.GetCache("douyin_zhibo_task");
                }
                catch (Exception)
                {
                    dt = new DataTable();
                }
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["orderid"] + "");
                    dic.Add("url", dt.Rows[i]["url"] + "");
                    dic.Add("liveid", (dt.Rows[i]["liveid"] + "").Replace("【补单】", "").Replace("【新订单重下】", ""));
                    dic.Add("online", dt.Rows[i]["online"] + "");
                    dic.Add("order_type", dt.Rows[i]["order_type"] + "");
                    dic.Add("num", dt.Rows[i]["num"] + "");
                    dic.Add("startCount", dt.Rows[i]["startCount"] + "");
                    dic.Add("finishNum", dt.Rows[i]["finishNum"] + "");
                    dic.Add("budan_num", dt.Rows[i]["budan_num"] + "");
                    dic.Add("status", dt.Rows[i]["status"] + "");
                    dic.Add("checkTime", dt.Rows[i]["checkTime"] + "");
                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("code", "0");
                dic.Add("msg", "success");
                dic.Add("total", dt.Rows.Count);
                dic.Add("list", list);
                back = JsonMapper.ToJson(dic);
                break;
            case "apilog":
                back = getLog();
                break;
            case "apilog_none":
                apiLog_none();
                dic.Add("code", "0");
                dic.Add("msg", "success");
                back = JsonMapper.ToJson(dic);
                break;
            case "c":
                back = (string)cae.GetCache(Request.QueryString["name"] + "");
                break;
            case "test":
                dt = (DataTable)cae.GetCache("douyin_zhibo_task");
                back = ToJson(dt);
                break;
            case "queryids":
                temp = Request.QueryString["type"] + "";
                if (string.IsNullOrEmpty(temp))
                {
                    return "{\"code\":10004,\"msg\":\"error param\"}";
                }
                if (!string.IsNullOrEmpty(Request.QueryString["start"] + ""))
                {
                    temp2 += " and createTime>=@start ";
                }
                if (!string.IsNullOrEmpty(Request.QueryString["end"] + ""))
                {
                    temp2 += " and createTime<=@end ";
                }

                parames = new SqlParameter[]{
                    new SqlParameter("@type",temp),
                    new SqlParameter("@start",Request.QueryString["start"]+""),
                    new SqlParameter("@end",Request.QueryString["end"]+"")
                };
                sql = " SELECT orderid as id FROM liveOrders with(nolock) where DATEADD(hour,total_day,createTime)>GETDATE() and status=0 and total_day-budan_count=@type " + temp2;
                dt = db.getDataTable(sql, parames);
                back = "已查到总共 " + dt.Rows.Count + " 条数据。\r\n\r\n";
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    back += dt.Rows[i]["id"] + "\r\n";
                }
                Response.ContentType = "text/plain";
                break;
            case "oped":
                string server_userId = "1";
                sql = " select isnull(SUM((case when uid<>" + server_userId + " then fee else 0 end)),0) as rxf,sum((case when uid<>" + server_userId + " then num else 0 end)) as oCount,isnull(SUM((case when uid=" + server_userId + " then fee else 0 end)),0) as bd_rxf,sum((case when uid=" + server_userId + " then num else 0 end)) as bd_oCount from feeTotal with(nolock) where DATEDIFF(DAY,indate,GETDATE())=0  ";
                dt = db.getDataTable(sql);
                if (dt.Rows.Count > 0)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("order_num", dt.Rows[0]["oCount"] + "");
                    dic.Add("total_fee", dt.Rows[0]["rxf"] + "");
                    dic.Add("budan_num", dt.Rows[0]["bd_oCount"] + "");
                    back = getResBody("success", 1, dic);
                }
                else
                {
                    back = getResBody("数据获取失败");
                }
                break;
            case "get_userMoney":
                sql = " select * from users with(nolock) ";
                dt = db.getDataTable(sql);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["id"] + "");
                    dic.Add("nick", dt.Rows[i]["nick"] + "");
                    dic.Add("amount", dt.Rows[i]["score"] + "");

                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("list", list);
                fz.sendResponse("success", 1, dic);
                break;
            case "user_recharge":
                if (!IsNumeric(fz.req("amount")))
                {
                    fz.sendResponse("请输入正确的加款金额");
                }
                if (Convert.ToDouble(fz.req("amount")) >= 0)
                {
                    temp = "充值";
                }
                else
                {
                    temp = "客服售后处理";
                }
                pams.Add(new SqlParameter("@title", temp));

                sql = @" 
declare @score decimal(18,2) 
declare @nick varchar(20)
update users set @nick=nick,@score=score,score=score+@amount where id=@id 
if(@score is not null)
begin 
    insert into records values(@id,@title,@amount,@score+@amount,'','',GETDATE())  
    select @score+@amount as new_amount,@nick as user_nick
end";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("user_nick", dt.Rows[0]["user_nick"] + "");
                    dic.Add("current_amount", dt.Rows[0]["new_amount"] + "");
                    fz.sendResponse("充值成功", 1, dic);
                }
                else
                {
                    fz.sendResponse("充值失败");
                }
                break;
            case "switchToRecOrder":

                temp = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd") + " 00:00:00";

                pams.Add(new SqlParameter("@record_time", temp));
                sql = @"

BEGIN TRY

  DECLARE @tab1 TABLE (id INT);

  insert into record_orders OUTPUT inserted.id INTO @tab1 select top 50000 * from orders with(nolock) where createTime<@record_time and (status='已完成' or status='已退单')  

  delete orders where createTime<@record_time and id in (select id from @tab1)

END TRY
BEGIN CATCH
END CATCH;

";

                res = db.ExecuteNonQuery(sql, pams.ToArray());

                if (res > 0)
                {
                    fz.sendResponse("已转入" + (res / 2) + "单", 1);
                }
                else
                {
                    fz.sendResponse("暂无需处理订单", 1);
                }

                break;
            default:
                back = "{\"code\":10004,\"msg\":\"您的请求方式有误\"}";
                break;
        }
        return back;
    }


    public string getUserName(string uid)
    {
        DataTable dt = new DataTable();
        string usernick = string.Empty;
        try
        {
            dt = selectDateTable(uConfig.users, " id='" + uid + "' ", 1);
            if (dt.Rows.Count > 0)
            {
                usernick = dt.Rows[0]["nick"] + "";
            }
        }
        catch (Exception)
        {
        }
        return usernick;
    }

    private int dateDiff(DateTime dtStart, DateTime dtEnd)
    {
        TimeSpan tsStart = new TimeSpan(dtStart.Ticks);
        TimeSpan tsEnd = new TimeSpan(dtEnd.Ticks);
        TimeSpan ts = tsEnd.Subtract(tsStart).Duration();
        int dateDiffSecond = ts.Days * 24 * 60 * 60 + ts.Hours * 60 * 60 + ts.Minutes * 60 + ts.Seconds;

        //两个时间的秒差
        return dateDiffSecond;
    }

    private DateTime idExpireTime(string id, int beforeTime = 0)
    {
        string temp = "cache_liveid_" + id;
        DateTime tempExpire = DateTime.Now;
        if (beforeTime == 1)
        {
            tempExpire = Convert.ToDateTime("2020-01-01 00:00:00");
        }
        try
        {
            tempExpire = (DateTime)cae.GetCache(temp + "_api");
        }
        catch (Exception)
        {
            try
            {
                tempExpire = (DateTime)cae.GetCache(temp);
            }
            catch (Exception)
            {
            }
        }
        return tempExpire;
    }

    private void putLog(string logstr, bool wTime = true)
    {
        string log = string.Empty;
        try
        {
            log = (string)cae.GetCache("apilog"); ;
        }
        catch (Exception)
        {
        }
        //cae.SetCache("apilog", log + "<br>" + (wTime ? "[" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "] " : "") + logstr);
    }

    private string getLog()
    {
        string log = string.Empty;
        try
        {
            log = (string)cae.GetCache("apilog"); ;
        }
        catch (Exception)
        {
        }
        return log;
    }

    private void apiLog_none()
    {
        cae.SetCache("apilog", "");
    }

    public string gs(string status)
    {
        string sta = string.Empty;
        switch (status)
        {
            case "0":
                sta = "未开始";
                break;
            case "1":
                sta = "进行中";
                break;
            case "2":
                sta = "已完成";
                break;
            case "3":
                sta = "订单异常";
                break;
            case "4":
                sta = "退单中";
                break;
            case "5":
                sta = "待补单";
                break;
            case "6":
                sta = "补单中";
                break;
            case "9":
                sta = "其他异常";
                break;
            default:
                sta = "";
                break;
        }
        return sta;
    }


    public class indexT
    {
        public string name
        {
            get;
            set;
        }
        public Int64 index
        {
            get;
            set;
        }
        public int rowsLen
        {
            get;
            set;
        }
    }

    public string getCacheIndex(string name)
    {
        string cacheIndex = (string)cae.GetCache("cacheApiIndex_" + name);
        cacheIndex = string.IsNullOrEmpty(cacheIndex) || !IsNumeric(cacheIndex) ? "-1" : cacheIndex;
        return cacheIndex;
    }
    public void setCacheIndex(string name, string index)
    {
        cae.SetCache("cacheApiIndex_" + name, index);
    }

    public string getCacheIndexStr(string name)
    {
        string cacheIndex = (string)cae.GetCache("cacheApiIndexStr_" + name);
        cacheIndex = string.IsNullOrEmpty(cacheIndex) ? "" : cacheIndex;
        return cacheIndex;
    }
    public void setCacheIndexStr(string name, string index)
    {
        cae.SetCache("cacheApiIndexStr_" + name, index);
    }
}