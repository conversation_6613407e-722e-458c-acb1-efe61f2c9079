      html, body, p {
    padding: 0px;
    margin: 0px;
}

.nav-wrap {
    width: 100%;
    height: 60px;
    box-shadow: 0 5px 10px #f0f0f0;
    margin-bottom: 30px;
}

    .nav-wrap .nav {
        width: 1200px;
        margin: 0 auto;
    }

        .nav-wrap .nav .dqd-logo {
            /*width: 160px;*/
            float: left;
            margin-top: 10px;
            margin-right: 10px;
        }

        .nav-wrap .nav .nuxt-link-active {
            background: #16b13a;
            color: #fff!important;
        }

        .nav-wrap .nav .nav-tab {
            width: 100px;
            height: 60px;
            line-height: 60px;
            text-align: center;
            display: inline-block;
            font-size: 18px;
            text-decoration: none;
            color: #000;
            cursor: pointer;
        }

        .nav-wrap .nav .nav-selected {
            background: #16b13a;
            color: #fff!important;
        }

        .nav-wrap .nav .login {
            float: right;
            line-height: 60px;
            color: #333;
            font-size: 16px;
            text-decoration: none;
        }

.home-wrap {
    width: 1200px;
    margin: 0 auto 60px;
    overflow: hidden;
}


   /*
 * Examples
 *
 * Isolated sections of example content for each component or feature. Usually
 * followed by a code snippet.
 */

        .bs-example {
            position: relative;
            padding: 45px 15px 15px;
            margin: 0 -15px 15px;
            border-color: #e5e5e5 #eee #eee;
            border-style: solid;
            border-width: 1px 0;
            -webkit-box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
            box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
        }
            /* Echo out a label for the example */
            .bs-example:after {
                position: absolute;
                top: 15px;
                left: 15px;
                font-size: 12px;
                font-weight: bold;
                color: #959595;
                text-transform: uppercase;
                letter-spacing: 1px;
                content: "账户信息";
            }

        .bs-example-padded-bottom {
            padding-bottom: 24px;
        }

        /* Tweak display of the code snippets when following an example */
        .bs-example + .highlight,
        .bs-example + .zero-clipboard + .highlight {
            margin: -15px -15px 15px;
            border-width: 0 0 1px;
            border-radius: 0;
        }

        /* Make the examples and snippets not full-width */
        @media (min-width: 768px) {
            .bs-example {
                margin-right: 0;
                margin-left: 0;
                background-color: #fff;
                border-color: #ddd;
                border-width: 1px;
                border-radius: 4px 4px 0 0;
                -webkit-box-shadow: none;
                box-shadow: none;
            }

                .bs-example + .highlight,
                .bs-example + .zero-clipboard + .highlight {
                    margin-top: -16px;
                    margin-right: 0;
                    margin-left: 0;
                    border-width: 1px;
                    border-bottom-right-radius: 4px;
                    border-bottom-left-radius: 4px;
                }

            .bs-example-standalone {
                border-radius: 4px;
            }
        }

        /* Undo width of container */
        .bs-example .container {
            width: auto;
        }

        /* Tweak content of examples for optimum awesome */
        .bs-example > p:last-child,
        .bs-example > ul:last-child,
        .bs-example > ol:last-child,
        .bs-example > blockquote:last-child,
        .bs-example > .form-control:last-child,
        .bs-example > .table:last-child,
        .bs-example > .navbar:last-child,
        .bs-example > .jumbotron:last-child,
        .bs-example > .alert:last-child,
        .bs-example > .panel:last-child,
        .bs-example > .list-group:last-child,
        .bs-example > .well:last-child,
        .bs-example > .progress:last-child,
        .bs-example > .table-responsive:last-child > .table {
            margin-bottom: 0;
        }

        .bs-example > p > .close {
            float: none;
        }

        /* Typography */
        .bs-example-type .table .type-info {
            color: #767676;
            vertical-align: middle;
        }

        .bs-example-type .table td {
            padding: 15px 0;
            border-color: #eee;
        }

        .bs-example-type .table tr:first-child td {
            border-top: 0;
        }

        .bs-example-type h1,
        .bs-example-type h2,
        .bs-example-type h3,
        .bs-example-type h4,
        .bs-example-type h5,
        .bs-example-type h6 {
            margin: 0;
        }

        /* Contextual background colors */
        .bs-example-bg-classes p {
            padding: 15px;
        }

        /* Images */
        .bs-example > .img-circle,
        .bs-example > .img-rounded,
        .bs-example > .img-thumbnail {
            margin: 5px;
        }

        /* Tables */
        .bs-example > .table-responsive > .table {
            background-color: #fff;
        }

        /* Buttons */
        .bs-example > .btn,
        .bs-example > .btn-group {
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .bs-example > .btn-toolbar + .btn-toolbar {
            margin-top: 10px;
        }

        /* Forms */
        .bs-example-control-sizing select,
        .bs-example-control-sizing input[type="text"] + input[type="text"] {
            margin-top: 10px;
        }

        .bs-example-form .input-group {
            margin-bottom: 10px;
        }

        .bs-example > textarea.form-control {
            resize: vertical;
        }

        /* List groups */
        .bs-example > .list-group {
            max-width: 400px;
        }

        /* Navbars */
        .bs-example .navbar:last-child {
            margin-bottom: 0;
        }

        .bs-navbar-top-example,
        .bs-navbar-bottom-example {
            z-index: 1;
            padding: 0;
            overflow: hidden; /* cut the drop shadows off */
        }

            .bs-navbar-top-example .navbar-header,
            .bs-navbar-bottom-example .navbar-header {
                margin-left: 0;
            }

            .bs-navbar-top-example .navbar-fixed-top,
            .bs-navbar-bottom-example .navbar-fixed-bottom {
                position: relative;
                margin-right: 0;
                margin-left: 0;
            }

        .bs-navbar-top-example {
            padding-bottom: 45px;
        }

            .bs-navbar-top-example:after {
                top: auto;
                bottom: 15px;
            }

            .bs-navbar-top-example .navbar-fixed-top {
                top: -1px;
            }

        .bs-navbar-bottom-example {
            padding-top: 45px;
        }

            .bs-navbar-bottom-example .navbar-fixed-bottom {
                bottom: -1px;
            }

            .bs-navbar-bottom-example .navbar {
                margin-bottom: 0;
            }

        @media (min-width: 768px) {
            .bs-navbar-top-example .navbar-fixed-top,
            .bs-navbar-bottom-example .navbar-fixed-bottom {
                position: absolute;
            }
        }

        /* Pagination */
        .bs-example .pagination {
            margin-top: 10px;
            margin-bottom: 10px;
        }

        /* Pager */
        .bs-example > .pager {
            margin-top: 0;
        }

        /* Example modals */
        .bs-example-modal {
            background-color: #f5f5f5;
        }

            .bs-example-modal .modal {
                position: relative;
                top: auto;
                right: auto;
                bottom: auto;
                left: auto;
                z-index: 1;
                display: block;
            }

            .bs-example-modal .modal-dialog {
                left: auto;
                margin-right: auto;
                margin-left: auto;
            }

        /* Example dropdowns */
        .bs-example > .dropdown > .dropdown-toggle {
            float: left;
        }

        .bs-example > .dropdown > .dropdown-menu {
            position: static;
            display: block;
            margin-bottom: 5px;
            clear: left;
        }

        /* Example tabbable tabs */
        .bs-example-tabs .nav-tabs {
            margin-bottom: 15px;
        }

        /* Tooltips */
        .bs-example-tooltips {
            text-align: center;
        }

            .bs-example-tooltips > .btn {
                margin-top: 5px;
                margin-bottom: 5px;
            }

        .bs-example-tooltip .tooltip {
            position: relative;
            display: inline-block;
            margin: 10px 20px;
            opacity: 1;
        }

        /* Popovers */
        .bs-example-popover {
            padding-bottom: 24px;
            background-color: #f9f9f9;
        }

            .bs-example-popover .popover {
                position: relative;
                display: block;
                float: left;
                width: 260px;
                margin: 20px;
            }

        /* Scrollspy demo on fixed height div */
        .scrollspy-example {
            position: relative;
            height: 200px;
            margin-top: 10px;
            overflow: auto;
        }

        .bs-example > .nav-pills-stacked-example {
            max-width: 300px;
        }

        /* Simple collapse example */
        #collapseExample .well {
            margin-bottom: 0;
        }

        /* Don't wrap event names in Events tables in JS plugin docs */
        .bs-events-table > thead > tr > th:first-child,
        .bs-events-table > tbody > tr > td:first-child {
            white-space: nowrap;
        }

        .bs-events-table > thead > tr > th:first-child {
            width: 150px;
        }

        .js-options-table > thead > tr > th:nth-child(1),
        .js-options-table > thead > tr > th:nth-child(2) {
            width: 100px;
        }

        .js-options-table > thead > tr > th:nth-child(3) {
            width: 50px;
        }

        /*
 * Code snippets
 *
 * Generated via Pygments and Jekyll, these are snippets of HTML, CSS, and JS.
 */

        .highlight {
            padding: 9px 14px;
            margin-bottom: 14px;
            background-color: #f7f7f9;
            border: 1px solid #e1e1e8;
            border-radius: 4px;
        }

            .highlight pre {
                padding: 0;
                margin-top: 0;
                margin-bottom: 0;
                word-break: normal;
                white-space: nowrap;
                background-color: transparent;
                border: 0;
            }

                .highlight pre code {
                    font-size: inherit;
                    color: #333; /* Effectively the base text color */
                }

                    .highlight pre code:first-child {
                        display: inline-block;
                        padding-right: 45px;
                    }

                      .btn-white {
            background-color: #fff;
            border-color: #e7eaf3!important;
            color: #677788;
        }

            .btn-white.active, .btn-white.focus, .btn-white:active, .btn-white:focus, .btn-white:hover {
                color: #0052ea;
                box-shadow: 0 3px 6px -2px rgba(140,152,164,.25);
            }

        td {
            font-size: 12px;
        }

            td .btn {
                padding: 0 10px;
                margin: 0 2px;
            }

        .link_td {
            color: #1e2022;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        kbd {
            margin: 0 1px;
        }



        
/*Modal 模態框*/

        .momo-modal-mask,.momo-modal-dialog  {
            position:fixed;
            height:100%;
            width:100%;
            left:0;
            top:0;
        }

        .momo-modal-mask {
            background:rgba(29, 33, 41, 0.6);
            z-index:1001;
        }

        .momo-modal-dialog {
            z-index:1002;
            display:flex;
            justify-content: center;/*水平主轴居中*/
            align-items: center;/*垂直交叉轴居中*/            
        }

        .momo-modal-simple {
            background:#fff;
            border-radius:5px;
            padding:22px 30px;
            max-width:700px;
            margin:10px;
            min-width: 200px;
            position:relative;
        }
        

        .momo-modal-title {
            text-align:left;
            margin-bottom: 24px;
        }

        
        .momo-modal-title svg {
            color:rgb(22, 93, 255);
            position:relative;
            top:5px;
            width:1.5em;
            height:1.5em;
            margin-right: 8px;
        }

        .momo-modal-error svg {
            color:rgb(213, 73, 65);
        }
        .momo-modal-warning svg {
            color:rgb(227, 115, 24);
        }
        .momo-modal-success svg {
            color:rgb(43, 164, 113);
        }

        .momo-modal-content {
            margin:10px 0;
            font-size:14px;
            line-height:20px;
            text-align:center;
        }
        .momo-modal-buttom {
            text-align:center;
                margin-top: 32px;
        }
        .momo-modal-buttom .moButton {
            margin-bottom:0px;margin:0 5px;
        }