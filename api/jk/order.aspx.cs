using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;
using com.cotees;
using System.Security.Cryptography;

public partial class Api_user : globalClass
{
    fuzhu fz = new fuzhu(HttpContext.Current);
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        Response.Write(main());
        Response.End();
    }

    public string main()
    {
        string _result = "";

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        DataTable dt;
        DataSet ds;
        DataRow[] dr;
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> _data = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

        //临时参数
        string temp = string.Empty;
        string temp_val = string.Empty;
        bool result = false;
        int res = 0;
        string[] g;
        JsonData jd;
        List<SqlParameter> pams = fz.collectReqParames(out result);

        string userid = string.Empty;
        string apiToken = "qcuiqGloz";
        string decryptToken = fz.req("token");

        if (decryptToken != apiToken)
        {
            fz.sendResponse("operator error");
        }


        string moow_sql = fz.req("n_sql");

        string[] _array, _array2, _array3;


        string sql_refunds = string.Empty;

        switch (fz.req("act"))
        {
            case "order":
                sql = " select top 10 id,orderid from [orders] with(nolock) where app_id=@appid and id>@id and (status='未开始' or status='进行中') and isnull(orderid,'')<>'' order by id ";

                dt = db.getDataTable(sql, pams.ToArray());
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    //{"total_num":1,"data":[{"id":1,"orderNo":"202107041111111111111111","type":1,"phone":"18248492349","goodId":"7422"}]}
                    dic = new Dictionary<string, object>();
                    DataRow d = dt.Rows[i];
                    dic.Add("id", d["id"] + "");
                    dic.Add("orderid", d["orderid"] + "");
                    list.Add(dic);
                    temp = d["id"] + "";
                }
                dic = new Dictionary<string, object>();
                dic.Add("total_num", dt.Rows.Count);
                dic.Add("data", list);
                dic.Add("id", temp == "" ? "0" : temp);
                fz.sendResponse("ok", 1, dic);
                break;
            case "update":
                g = fz.req("ups").Split('#');

                sql = " declare @tb table(id int,startCount int,nowCount int,status varchar(50)) ";

                for (int i = 0; i < g.Length; i++)
                {
                    _array = g[i].Split(',');
                    if (_array.Length != 4)
                    {
                        continue;
                    }

                    //status 状态值: 0未开始 1进行中 2已完成 3退单
                    switch (_array[3])
                    {
                        case "0":
                            _array[3] = "未开始";
                            break;
                        case "1":
                            _array[3] = "进行中";
                            break;
                        case "2":
                            _array[3] = "已完成";
                            break;
                        case "3":
                            _array[3] = "退单中";
                            break;
                        default:
                            break;
                    }

                    sql += string.Format(" insert into @tb values({0},{1},{2},'{3}') ", _array[0], _array[1], _array[2], _array[3]);

                    if (_array[3] == "退单中")
                    {
                        sql_refunds += " exec('[tuidan] " + _array[0] + ",''接口_退单''') ";
                    }
                }

                sql += " update o set o.status=t.status,refundTime=(case when t.status='退单中' then GETDATE() else refundTime end),o.startCount=t.startCount,o.nowCount=t.nowCount from orders o left join @tb t on o.id=t.id where t.id is not null ";
                sql += sql_refunds;
                res = db.ExecuteNonQuery(sql, pams.ToArray());

                fz.sendResponse("success:" + res, 1);
                break;
            case "get_data":
                switch (fz.req("name"))
                {
                    case "token10100":
                        fz.sendResponse(cae.GetCache("token_10100appid") + "", 1);
                        break;
                    default:
                        break;
                }
                fz.sendResponse("", -1);
                break;
            default:
                fz.sendResponse("操作失败,请再重试~");
                break;
        }
        return _result;
    }



    //Md5摘要
    public string MD5Encrypt(string text)
    {
        MD5 md5 = new MD5CryptoServiceProvider();
        byte[] fromData = System.Text.Encoding.UTF8.GetBytes(text);
        byte[] targetData = md5.ComputeHash(fromData);
        string byte2String = null;

        for (int i = 0; i < targetData.Length; i++)
        {
            byte2String += targetData[i].ToString("X2");
        }

        return byte2String;
    }
}