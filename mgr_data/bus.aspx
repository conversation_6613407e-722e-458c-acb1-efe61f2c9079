<%@ Page Title="业务管理" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="bus.aspx.cs" Inherits="admin_businessManager" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .son_ td {
            background: #f6f6f6;
            color: gray;
            color: #333;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="page-header clearfix">
        <h1 class="pull-left">业务管理<%=Request.QueryString["active_id"] + "" == "" ? "" : "<span style='color:red;'>（活动：" + Request.QueryString["active_name"] + "）</span>" %></h1>
    </div>
    <div id="temp" class="none" style="background: #fff; margin: 10px 0; padding: 10px;">
        <div>
            <div class="input-group input-group-sm" style="margin-bottom: 12px;">
                <span class="input-group-addon">业务提示</span>
                <input id="Text1" name="Username" type="text" class="form-control tips" placeholder="请输入业务提示信息" />
            </div>
            <div class="input-group input-group-sm" style="margin-bottom: 12px;">
                <span class="input-group-addon">当前价格</span>
                <input id="nick" name="Username" type="text" class="form-control price" placeholder="请输入业务价格（更改后所有密价失效）" />
            </div>
            <div class="input-group input-group-sm" style="margin-bottom: 12px;">
                <span class="input-group-addon">是否开放</span>
                <select class="form-control status">
                    <option value="1">开放</option>
                    <option value="2">禁用</option>
                </select>
            </div>
            <div class="input-group input-group-sm" style="margin-bottom: 12px;">
                <span class="input-group-addon">业务排序</span>
                <input id="sort" type="text" class="form-control sort" placeholder="值越小越靠前" />
            </div>
        </div>
    </div>



    <div id="rank_panel" class="none">
        <h4 style="font-family: FangSong;">业务价格 <span style="color: #bbb;" id="rank_name">> 大商户</span></h4>
        <hr />
    </div>

    <%if (uConfig.p_userNickAD == "gm-facai")
      {
    %>
    <a class="btn btn-primary" onclick="busSave()">保存本次调整</a>

    <%
      } %>

    <div>
        <div class="input-group" style="padding: 6px;">


            <div class="input-group" style="padding: 6px;">



                <input id="search-keyword" type="text" placeholder="请输入关键词" class="input-sm form-control" onkeyup="searchList()" />
                <span class="input-group-btn">
                    <a class="btn btn-sm btn-primary" onclick="searchList()">搜索</a>

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                    <a class="btn btn-sm btn-success" onclick="new_item()">添加业务</a>
                    <%
                      } %>
                    <a class="btn btn-sm btn-danger" onclick="javascript:location.href=location.href;">刷新</a>
                </span>
            </div>
        </div>
    </div>


    <table class="ctTable discolor" style="width: 100%;">
        <tr>
            <th></th>
            <th>ID</th>
            <th>代码</th>
            <th>任务名称</th>
            <%if (Request.QueryString["userid"] + "" == "")
              {
            %>

            <th>点数</th>
            <th>状态</th>
            <th>退单</th>
            <th>关闭不退</th>
            <th></th>

            <%
              }
              else
              {
            %>

            <th>点数</th>
            <th>最低数量</th>
            <th>最高数量</th>
            <th>状态</th>
            <th>退单</th>
            <th>关闭不退</th>

            <%} %>
        </tr>
        <tbody id="list-display">
        </tbody>
    </table>
    <a class="btn btn-primary" style="margin-top: 10px;" onclick="busSave()">保存本次调整</a>


    <script id="user_number_limit" type="text/javascript">
        <%=ToJson(dt) %>
    </script>


    <script id="app_group" type="text/javascript">
        <%=ToJson(chelper.gdt("app_group")) %>
    </script>


    <script>

        var _page = "apps";
        var typeList = [];
        typeList.push(['fans', '粉丝']);
        //typeList.push(['like', '点赞']);
        typeList.push(['view', '播放']);
        //typeList.push(['comment', '评论']);
        typeList.push(['online', '人气']);



        var mediaList = [];
        mediaList.push(['tk', 'Tiktok']);
        mediaList.push(['ins', 'Instagram']);
        mediaList.push(['fb', 'Facebook']);
        mediaList.push(['tw', 'Twitter']);
        //mediaList.push(['tg', 'Telegram']);
        mediaList.push(['ytb', 'Youtube']);
        mediaList.push(['twitch', 'Twitch']);
        mediaList.push(['shopee', 'Shopee']);
        mediaList.push(['bigo', 'BIGO']);
        mediaList.push(['steam', 'Steam']);
        mediaList.push(['lazada', 'Lazada']);



        var app_group_json = JSON.parse($('#app_group').html());

        var app_group = [];
        app_group.push(['-1', '无']);
        for (var i = 0; i < app_group_json.length; i++) {
            var e = app_group_json[i];
            app_group.push([e.id, e.name]);
        }

        function getAppGroupData(groupid) {
            var res = '';
            for (var i = 0; i < app_group_json.length; i++) {
                var e = app_group_json[i];
                if (e.id == groupid) {
                    res = '<div style="color: ' + (e.state == 1 ? 'green' : '#bbb') + ';margin-top: 3px;">[' + e.name + ']</div>';
                    break;
                }
            }
            return res;
        }


        var close_not_refund = [];
        close_not_refund.push(['0', '禁用']);
        close_not_refund.push(['1', '启用']);

        function new_item() {
            var cg = [];

            cg.push({ name: '社交媒体', id: 'media_type', value: 'dy', type: 'select', data: mediaList });
            cg.push({ name: '业务分类', id: 'type', value: -1, type: 'select', data: typeList });
            cg.push({ name: '业务代码', id: 'code', value: '' });
            cg.push({ name: '业务名称', id: 'name', value: '' });
            cg.push({ name: '业务组', id: 'groupid', value: '-1', type: 'select', data: app_group });
            cg.push({ name: '时长(分)', id: 'times', value: '' });
            cg.push({ name: '关闭不退', id: 'close_not_refund', value: '0', type: 'select', data: close_not_refund });
            cg.push({ name: '关闭限时', id: 'close_limit_times', value: '' });

            edit({ aname: 'mgr_data/apidata', title: '添加业务', action: _page, id: '', data: cg });
        }


        var rankid = request("rankid", location.href);
        var jsondata;

        $(function () {
            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=bus",
                data: { rankid: rankid, active_id: request("active_id", location.href) },
                datatype: "json",
                success: function (data) {
                    jsondata = data;
                    searchbus("", "");
                },
                error: function () {
                    serr();
                }
            });
        })

        var pager_key = "";
        var opt = "";

        function setPageSize(obj) {
            opt = $(obj).val();
            searchList();
        }

        function searchList() {
            pager_key = $("#search-keyword").val();
            searchbus(pager_key, opt);
        }

        function searchbus(key, key2) {
            key = key.toUpperCase();
            key2 = key2.toUpperCase();
            var json = jsondata;
            var rank_info = json.rank_info;
            var rank_res = rank_info.res;
            if (rank_res != "") {
                if (rank_res != "查询成功") {
                    layer.msg(rank_res, { icon: 2 });
                    return;
                }

                $("#rank_name").html("> " + rank_info.name);
                $("#rank_panel").show();
            }
            json = json.app_info;
            console.log('json', json);
            $("#list-display").html("");


            var limit_list = JSON.parse($('#user_number_limit').html());


            for (var i = 0; i < json.length; i++) {
                var options = json[i].options;









                if ((json[i].app_name.toUpperCase().indexOf(key) != -1 || json[i].app_code.toUpperCase().indexOf(key) != -1) && json[i].app_name.toUpperCase().indexOf(key2) != -1) {
                    var new_html = "";

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                    new_html = "<td><span style='font-size:12px;cursor:pointer' onclick=\"appEdit('" + json[i].app_code + "')\" class=\"fa-pencil fa\">&nbsp;编辑</span>&nbsp;&nbsp;<span style='font-size:12px;cursor:pointer;color:chocolate;' onclick=\"event_create({'aname':'admin','name':'" + _page + "','event':'delete','id':" + json[i].app_id + "})\" class=\"fa-windows fa\">&nbsp;删除</span></td>";
                    <%
                      } %>

                    //console.log('e', typeof (json[i].times) != 'undefined');
                    var __html = "<tr onclick='javascript:$(\".app_" + json[i].app_code + "\").toggleClass(\"none\")'><td>" + "</td><td>" + json[i].app_id + "</td><td>" + json[i].app_code
                        + (json[i].times != '' && json[i].times != "0" ? '<span style="background: #939393;color:#eee;padding: 2px 5px;border-radius: 3px;margin-left: 5px;">' + json[i].times + '分钟</span>' : '')
                         + getAppGroupData(json[i].groupid)
                        + "</td><td  style='max-width:78px;'>" + json[i].app_name + "</td><td><input class='app_input' style='max-width:78px;' data-default='" + json[i].app_price + "' data-id='" + json[i].app_id + "' data-code='" + json[i].app_code + "' value='" + json[i].app_price + "'></td><td>" + (json[i].app_status == 1 ? "<span style='color:#3199e8'>开放</span>" : "<span style='color:#dd3b3a'>关闭</span>") + "</td><td>" + (json[i].refund_order == 1 ? "<span style='color:green'>支持</span>" : json[i].refund_order == 0 ? "<span style='color:red'>禁止</span>" : "<span style='color:#000'>未知</span>") + "</td><td>" + (json[i].close_not_refund == 1 ? "<span style='color:green'>支持(" + json[i].close_limit_times +")</span>" : "<span style='color:red'>禁止</span>") + "</td>" + new_html + "</tr>";

                <%if (Request.QueryString["userid"] + "" != "")
                  {
                %>
                    var amount = "";
                    var minNumber = "";
                    var maxNumber = "";
                    for (var t = 0; t < limit_list.length; t++) {
                        if (limit_list[t].appid == json[i].app_id) {
                            amount = limit_list[t].amount
                            minNumber = limit_list[t].mincount
                            maxNumber = limit_list[t].maxcount
                        }
                    }
                    __html = "<tr onclick='javascript:$(\".app_" + json[i].app_code + "\").toggleClass(\"none\")'><td>" + "</td><td>" + json[i].app_id + "</td><td>" + json[i].app_code + "</td><td  style='max-width:78px;'>" + json[i].app_name + "</td><td><input class='app_input amount' style='max-width:78px;' data-default='" + amount + "' data-id='" + json[i].app_id + "' data-code='" + json[i].app_code + "' value='" + amount + "'></td><td><input class='app_input minNumber' style='max-width:78px;' data-default='" + minNumber + "' data-id='" + json[i].app_id + "' data-code='" + json[i].app_code + "' value='" + minNumber + "'></td><td><input class='app_input maxNumber' style='max-width:78px;' data-default='" + maxNumber + "' data-id='" + json[i].app_id + "' data-code='" + json[i].app_code + "' value='" + maxNumber + "'></td><td>" + (json[i].app_status == 1 ? "<span style='color:#3199e8'>开放</span>" : "<span style='color:#dd3b3a'>关闭</span>") + "</td>" + "<td>" + (json[i].refund_order == 1 ? "<span style='color:green'>支持</span>" : json[i].refund_order == 0 ? "<span style='color:red'>禁止</span>" : "<span style='color:#000'>未知</span>") + "</td>" +
                        ("<td>" + (json[i].colose_not_refund == 1 ? "<span style='color:green'>启用</span>" :  "<span style='color:red'>禁用</span>") + "</td>")
                        + "</tr>";

                <%
                  }
                %>

                    $("#list-display").append(__html);
                }
            }
        }



        function _bindEvent(__temp) {
            $('[edit-' + __temp.id + ']').on("click", function () {
                edit({ aname: 'mgr_data/apidata', title: '修改业务', action: _page, id: __temp.id, data: __temp.modify_model });
            })
        }

        var add_pack_list = function (obj) {
            var k = $(obj);
            push_pack_data(k, "", "", "")
        }

        var push_pack_data = function (k, num, total_fee, packid) {
            k.closest("tr").before("<tr class='s11' pack_source='" + num + "-" + total_fee + "' packid='" + packid + "' appid='" + k.closest("tr").attr("appid") + "'><td></td><td>|</td><td><input value='" + num + "' style='max-width:78px;'></td><td><input value='" + total_fee + "' style='max-width:78px;'></td><td>-</td><td>-</td><td>-</td><td>-</td></tr>");
        }
    </script>
    <style>
        .s11 td {
            background: #f3f7f4!important;
        }
    </style>
    <script>

        function busSave() {
            var lists = new Array();
            $(".app_input").each(function () {
                var id = $(this).data("id");
                var code = $(this).data("code");
                var valD = $(this).data("default");
                var val = $(this).val();
                if (val != "" && !isNaN(val) && valD != val) {
                    lists.push({
                        type: '',
                        id: id,
                        code: code,
                        price: val
                    });
                }
            })

            <%if (Request.QueryString["userid"] + "" != "")
              {
                %>

            var lists = new Array();

            $(".minNumber").each(function () {
                var id = $(this).data("id");
                var code = $(this).data("code");
                var valD = $(this).data("default");
                var val = $(this).val();
                var updateAmount = "";
                var updateMin = "";
                var updateMax = "";
                if (valD != val) {
                    if (val == "") {
                        val = "null"
                    }
                    updateMin = val;
                }


                valD = $(this).closest("tr").find(".maxNumber").data("default");
                val = $(this).closest("tr").find(".maxNumber").val();
                if (valD != val) {
                    if (val == "") {
                        val = "null"
                    }
                    updateMax = val;
                }



                valD = $(this).closest("tr").find(".amount").data("default");
                val = $(this).closest("tr").find(".amount").val();
                if (valD != val) {
                    if (val == "") {
                        val = "null"
                    }
                    updateAmount = val;
                }

                if (updateMin != "" || updateMax != "" || updateAmount != "") {
                    lists.push({
                        type: 'update_number',
                        userid: '<%=Request.QueryString["userid"] + "" %>',
                        id: id,
                        code: code,
                        price: 99999,
                        amount: updateAmount,
                        mincount: updateMin,
                        maxcount: updateMax
                    });
                }
            })


                <%
              }
                %>
            //$(".app_cost").each(function () {
            //    var id = $(this).data("id");
            //    var code = $(this).data("code");
            //    var valD = $(this).data("default");
            //    var val = $(this).val();
            //    if (val != "" && !isNaN(val) && valD != val) {
            //        lists.push({
            //            type: 'cost',
            //            id: id,
            //            code: code,
            //            price: val
            //        });
            //    }
            //})


            var packs = new Array();
            $(".s11").each(function () {
                var packid = $(this).attr("packid");
                var appid = $(this).attr("appid");
                var pack_source = $(this).attr("pack_source");
                var num = $(this).find("input").eq(0).val();
                var total_fee = $(this).find("input").eq(1).val();
                if (pack_source && pack_source != num + "-" + total_fee) {
                    packs.push({
                        id: packid,
                        appid: appid,
                        num: num,
                        total_fee: total_fee
                    });
                }
            })

            var data = {
                app: lists,
                packs: packs,
                option: []
            };

            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=busSave",
                data: { change: JSON.stringify(data), rankid: rankid, active_id: request("active_id", location.href) },
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {
                        layer.msg(json.msg, { icon: 1 }, rfPage);
                    } else {
                        layer.msg(json.msg, { icon: 2 });
                    }
                },
                error: function () {
                    serr();
                }
            });
        }


        function busSave2() {
            var app = "";
            var option = "";
            var res = "";
            $(".app_input").each(function () {
                var id = $(this).data("id");
                var code = $(this).data("code");
                var valD = $(this).data("default");
                var val = $(this).val();
                if (val != "" && !isNaN(val) && valD != val) {
                    app += app == "" ? "" : ",";
                    app += "{\"id\":" + id + ",\"code\":\"" + code + "\",\"price\":\"" + val + "\"}";
                }
            })


            $(".option_input").each(function () {
                var id = $(this).data("id");
                var code = $(this).data("code");
                var valD = $(this).data("default");
                var val = $(this).val();
                if (val != "" && !isNaN(val) && valD != val) {
                    option += option == "" ? "" : ",";
                    option += "{\"id\":" + id + ",\"code\":\"" + code + "\",\"price\":\"" + val + "\"}";
                }
            })

            res = "{\"app\":[" + app + "],\"option\":[" + option + "]}";


            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=busSave",
                data: { change: res, rankid: rankid, active_id: request("active_id", location.href) },
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {
                        layer.msg(json.msg, { icon: 1 }, rfPage);
                    } else {
                        layer.msg(json.msg, { icon: 2 });
                    }
                },
                error: function () {
                    serr();
                }
            });
        }

        function appEdit(appcode) {
            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=appEditQuery",
                data: { appcode: appcode },
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {

                        var modify_model = [];
                        modify_model.push({ name: '社交媒体', id: 'media_type', value: json.media_type, type: 'select', data: mediaList });
                        modify_model.push({ name: '业务分类', id: 'type2', value: json.type2, type: 'select', data: typeList });
                        modify_model.push({ name: '业务代码', id: 'code', value: json.appcode });
                        modify_model.push({ name: '业务名称', id: 'name', value: json.name });
                        modify_model.push({ name: '业务提示', id: 'notice', type: 'textarea', value: json.notice });
                        modify_model.push({ name: '最低数量', id: 'mincount', value: json.mincount });
                        modify_model.push({ name: '最高数量', id: 'maxcount', value: json.maxcount });

                        modify_model.push({ name: '业务排序', id: 'sort', value: json.sort });
                        modify_model.push({ name: '取消功能', id: 'refund_order', value: json.refund_order, type: 'option', data: [['1', '支持'], ['0', '不支持']] });

                        modify_model.push({ name: '关闭不退', id: 'close_not_refund', value: json.close_not_refund, type: 'select', data: close_not_refund });
                        modify_model.push({ name: '关闭限时', id: 'close_limit_times', value: json.close_limit_times });

                        var plans = [];
                        for (var i = 0; i < json.apps.length; i++) {
                            //$("#edbox .apps").append("<input type=\"radio\" name=\"interface\" " + ((json.status == 1 && json.apps[i].appid == json.appid) ? "checked=\"checked\"" : "") + " value=\"" + json.apps[i].appid + "\" />" + json.apps[i].name + "&nbsp;&nbsp;&nbsp;");
                            plans.push([json.apps[i].appid, json.apps[i].name]);
                        }
                        plans.push([-1, '关闭']);

                        modify_model.push({ name: '对接平台', id: 'appid', type: 'option', value: json.appid, data: plans });
                        modify_model.push({ name: '业务组', id: 'groupid', value: json.groupid, type: 'select', data: app_group });
                        modify_model.push({ name: '时长(分)', id: 'times', value: json.times });


                        edit({ aname: 'mgr_data/apidata', title: '修改业务', action: _page, id: json.id, data: modify_model });


                    } else {
                        layer.msg(json.msg, { icon: 2 });
                    }
                },
                error: function () {
                    serr();
                }
            });
        }

        function changeEdit() {
            var appcode = $("#edbox .appcode").val();
            var origin_appcode = $("#edbox .origin_appcode").val();
            var appname = $("#edbox .appname").val();
            var appnotice = $("#edbox .appnotice").val();
            var appmincount = $("#edbox .appmincount").val();
            var appmaxcount = $("#edbox .appmaxcount").val();
            var appmincost = $("#edbox .appmincost").val();
            var appsort = $("#edbox .appsort").val();
            var limTotalNum = $("#edbox .limTotalNum").val();
            //var maxPushMoney = $("#edbox .maxPushMoney").val();

            var appid = $("#edbox .apps").find('input[name="interface"]:checked').val();
            var istest = $("#edbox input[name='istest']:checked").val();
            var modeClassId = $("#edbox input[name='modeClassId']:checked").val();
            var refund_order = $("#edbox input[name='refund_order']:checked").val();
            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=changeEdit",
                data: { origin_appcode: origin_appcode, appcode: appcode, appname: appname, appnotice: appnotice, appmincount: appmincount, appmaxcount: appmaxcount, appmincost: appmincost, appsort: appsort, appid: appid, istest: istest, modeClassId: modeClassId, limTotalNum: limTotalNum, refund_order: refund_order },
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {
                        layer.msg(json.msg, { icon: 1, time: 500 }, rfPage);
                    } else {
                        layer.msg(json.msg, { icon: 2 });
                    }
                },
                error: function () {
                    serr();
                }
            });
        }
    </script>
</asp:Content>
