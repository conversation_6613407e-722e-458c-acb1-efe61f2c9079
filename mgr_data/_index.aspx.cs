using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;

public partial class serv_index : baseClass
{
    public DataTable dt = new DataTable();
    public DataTable temp_dt = new DataTable();
    public string googleCodeQrImg = "";
    public string googleCodeSecret = "";


    public string todayAmount = "0";
    public string yesterdayAmount = "0";
    public string monthAmount = "0";
    public string lastMonthAmount = "0";

    public double payment_rate = 1;

    public Dictionary<string, object> _dic = new Dictionary<string, object>();

    protected void Page_Load(object sender, EventArgs e)
    {
        _dic.Add("todayRefund", 0);
        _dic.Add("yesterdayRefund", 0);
        _dic.Add("monthRefund", 0);
        _dic.Add("lastMonthRefund", 0);


        _dic.Add("weekSerMoney", 0);
        _dic.Add("weekLastSerMoney", 0);

        if (!IsPostBack)
        {
           
        }
    }

    public int get_week()
    {
        int response_day = Convert.ToInt16(DateTime.Now.DayOfWeek);
        response_day = response_day == 0 ? 7 : response_day;
        return response_day;
    }
}