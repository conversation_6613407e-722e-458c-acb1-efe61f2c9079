<%@ Page Title="用户管理" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="new_users.aspx.cs" Inherits="admin_proxyUser" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .imBox {
            margin-top: 0.2em;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="page-header clearfix">
        <h1 class="pull-left">用户管理</h1>
    </div>

    <div class="form-inline" role="form">
        <div class="search-wrapper">
            <div class="input-group">
                <input id="search-keyword" type="text" placeholder="请输入代理名称" class="form-control search-input" style="width: 200px;" />
                <span class="input-group-btn">
                    <a class="btn btn-default" onclick="searchList()">搜索</a>
                    <a class="btn btn-primary" onclick="new_item()">开通账户</a>
                    <a class="btn btn-default" onclick="javascript:location.href=location.href">清空</a>
                </span>
            </div>
        </div>
        <span id="add-alert" class=""></span>
    </div>




    <table class="ctTable">
        <thead>
            <tr>
                <th>账号</th>
                <th>点数</th>
                <th>历史点数</th>
                <th>账户状态</th>
                <th>点数/任务量</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="list-display">
        </tbody>
    </table>


    <div style="text-align: center;">
        <div style="margin-top: 10px;">
            <div class="dataTables_info" id="editable_info" role="status" aria-live="polite">当前显示 1 到 <span class="page_size">1</span> 条  共 <span class="page_count">0</span> 条记录</div>
        </div>
        <div>
            <div class="dataTables_paginate paging_simple_numbers" id="editable_paginate">
                <ul class="pagination">
                </ul>
            </div>
        </div>
    </div>

    <script id="rank_list" type="text/ecmascript">
        <%=ToJson(rank_list)%>
    </script>

    <script>
        var rankList = [];
        var array = JSON.parse($("#rank_list").html());
        rankList.push([-1, "无等级"]);
        for (var i = 0; i < array.length; i++) {
            rankList.push([array[i]["id"], array[i]["name"]]);
        }
    </script>


    <script>
        var _page = "users";
        var pager_size = 50;
        var pager_index = 0;
        var pager_key = "";

        var opt = "";
        var opt2 = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            opt2 = request("param", location.href);
            getPager();
        }

        function searchList() {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt2 = request("param", location.href);
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=page",
                data: { s: pager_size, p: pager_index, page: _page, keyword: pager_key, opt: opt, opt2: opt2 },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var trStyle = "";
                            //trStyle = "background-color:ghostwhite;";
                            var tr = "";
                            tr += "<tr style='" + trStyle + "'>";
                            tr += ("<td>" + data[i].nick + "</td>");
                            tr += ("<td>" + data[i].score.toFixed(2) + "</td>");
                            tr += ("<td>" + (data[i].cost / 10).toFixed(2) + "</td>");
                            tr += ("<td>" + (data[i].status == "1" ? "正常" : "<span style='color:red;'>冻结</span>") + "</td>");
                            tr += ("<td>" + data[i].fee + "/" + data[i].num + "</td>");


                            var select_data = [];
                            select_data.push(["未开始", "未开始"]);
                            select_data.push(["进行中", "进行中"]);
                            select_data.push(["已完成", "已完成"]);
                            select_data.push(["订单异常", "订单异常"]);
                            select_data.push(["退单中", "退单中"]);
                            select_data.push(["已退单", "已退单"]);

                            var user_discount = [];
                            user_discount.push(["2", "独立折扣"]);
                            user_discount.push(["1", "开通"]);
                            user_discount.push(["0", "关闭"]);

                            var user_state = [];
                            user_state.push(["1", "激活"]);
                            user_state.push(["0", "冻结"]);


                            var cg = [];
                            cg.push({ name: '用户账号', id: 'nick', value: data[i].nick });
                            cg.push({ name: '用户密码', id: 'pwd', value: data[i].pwd });
                            cg.push({ name: '账户状态', id: 'status', value: data[i].status, type: 'select', data: user_state });


                            var user_money = [];
                            user_money.push({ name: '增加点数', id: 'money', value: '' });


                            tr += "<td style='width:199px;'>"

    <%if (uConfig.p_userNickAD == "gm-facai")
      {
    %>
                                + "<button style='display: inline-block;width: 80px;' type='button' class='el-button  el-button--primary el-button--mini' onclick='location.href=\"bus.aspx?userid=" + data[i].id + "\"'><span>上下限调整</span></button>&nbsp;"

    <%
      } %>


                                + "<button style='display: inline-block;width: 35px;' type='button' class='el-button  el-button--primary el-button--mini' onclick='edit(" + edtext({ aname: 'mgr_data/apidata', title: "用户加点", action: _page + "_money", id: data[i].id, data: user_money }) + ")'><span>加点</span></button>&nbsp;"
                                + "<button style='display: inline-block;width: 35px;' type='button' class='el-button  el-button--primary el-button--mini' onclick='edit(" + edtext({ aname: 'mgr_data/apidata', title: "编辑用户", action: _page, id: data[i].id, data: cg }) + ")'><span>编辑</span></button>&nbsp;"
                                //+ "<button type='button' class='el-button  el-button--danger el-button--mini' onclick=\"event_create({'aname':'mgr_data/apidata','name':'" + _page + "','event':'delete','id':" + data[i].id + "})\"><span>删除</span></button>"
                                + "</td>"


                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".pagination").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                    serr();
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>



    <script>

        function new_item() {
            var cg = [];

            cg.push({ name: '用户账号', id: 'nick', value: '' });
            cg.push({ name: '用户密码', id: 'pwd', value: '' });
            //cg.push({ name: '用户QQ', id: 'qq', value: '' });
            //cg.push({ name: '用户等级', id: 'rank', value: -1, type: 'select', data: rankList });

            edit({ aname: 'mgr_data/apidata', title: '新增用户', action: _page, id: '', data: cg });
        }
    </script>


    <script>

        function shouquan(uid) {
            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=opens_q",
                data: { uid: uid },
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {
                        var apps = [];
                        for (var i = 0; i < json.apps.length; i++) {
                            apps.push([json.apps[i].id, json.apps[i].name, { c: (json.apps[i].isopen == 1 ? true : false) }]);
                        }
                        console.log(apps);
                        var cg = [];
                        cg.push({ name: '授权业务', id: 'sq', value: '', type: 'check', data: apps });
                        edit({ aname: 'mgr_data/apidata', title: '授权业务', action: 'opens', id: uid, data: cg });
                    } else {
                        _tip(data.msg, 1500);
                    }
                },
                error: function () {
                    _tip(_expText, 1500);
                }
            });
        }
    </script>
</asp:Content>
