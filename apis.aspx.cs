using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;

public partial class wiky_index : baseClass
{

    public DataTable udt;
    public Dictionary<string, object> apidic = new Dictionary<string, object>();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            string sql = string.Empty;
            DataTable dt = new DataTable();
            DataSet ds = new DataSet();
            dbClass db = new dbClass();
            List<SqlParameter> pams = new List<SqlParameter>();

            pams.Add(new SqlParameter("@userid", uConfig.p_uid));

            sql = " select * from users with(nolock) where id=@userid ";
            ds = db.getDataSet(sql, pams.ToArray());
            udt = ds.Tables[0];

            pids.DataSource = uConfig.appList;
            pids.DataBind();


            string temp = string.Empty;

            temp = "createApi";
            pushDicText(temp, "接口类型", "service", "接口类型：order_create");
            pushDicText(temp, "产品id", "pid", "通过获取底部的PID表获得");
            pushDicText(temp, "任务地址", "url", "任务链接需填写正确");
            pushDicText(temp, "订单数量", "count", "下单所需数量", "是", "Int");
            pushDicText(temp, "订单备注", "note", "RQ任务[小时] 其他可不填", "否");
            pushDicText(temp, "商户订单号", "oid", "商户系统内部的订单号&nbsp;,32个字符内、&nbsp;可包含字母,<span style='color: rgb(255, 0, 0);'>每次下单请求确保在商户系统唯一</span>", "否");

            temp += "Result";
            pushDicText(temp, "状态码", "status", "状态码为“1”代表操作成功，状态值为其他代表失败", "是", "Int");
            pushDicText(temp, "返回信息", "message", "当前状态对应的返回信息");
            pushDicText(temp, "订单ID", "OrderId", "当下单成功或订单已存在时返回此值", "否", "Int");
            


            temp = "queryApi";
            pushDicText(temp, "接口类型", "service", "接口类型：order_query");
            pushDicText(temp, "订单ID", "orderid", "订单ID可传多个，每个ID之间用“,”隔开，建议不要超过10个");

            temp += "Result";
            pushDicText(temp, "状态码", "status", "状态码为“1”代表操作成功，状态值为其他代表失败", "是", "Int");
            pushDicText(temp, "返回信息", "message", "当前状态对应的返回信息");
            pushDicText(temp, "订单ID", "id", "", "是", "Int");
            pushDicText(temp, "产品ID", "pid", "", "是", "Int");
            pushDicText(temp, "订单链接", "url", "");
            pushDicText(temp, "订单数量", "num", "", "是", "Int");
            pushDicText(temp, "订单备注", "note", "");
            pushDicText(temp, "商户订单号", "orderid", "");
            pushDicText(temp, "订单开始值", "start", "", "是", "Int");
            pushDicText(temp, "订单最新值", "now", "", "是", "Int");
            pushDicText(temp, "订单状态", "status", "当前状态有：未开始 进行中 已完成 退单中 已退单 停播 开播");
            pushDicText(temp, "下单时间", "order_time", "");



            temp = "cancelApi";
            pushDicText(temp, "接口类型", "service", "接口类型：order_qx");
            pushDicText(temp, "订单ID", "orderid");

            temp += "Result";
            pushDicText(temp, "状态码", "status", "状态码为“1”代表操作成功，状态值为其他代表失败", "是", "Int");
            pushDicText(temp, "返回信息", "message", "当前状态对应的返回信息");



            temp = "refreshApi";
            pushDicText(temp, "接口类型", "service", "接口类型：order_sx");
            pushDicText(temp, "订单ID", "orderid");

            temp += "Result";
            pushDicText(temp, "状态码", "status", "状态码为“1”代表操作成功，状态值为其他代表失败", "是", "Int");
            pushDicText(temp, "返回信息", "message", "当前状态对应的返回信息");

        }
    }

    public void pushDicText(string name, string zdname, string val, string explain = "", string bt = "是", string type = "String")
    {
        string result = "";

        result += "<tr>";
        result += " <td style=\"color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);\" width=\"146\">" + zdname + "</td>";
        result += " <td style=\"color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);\" width=\"133\">" + val + "</td> ";
        result += " <td style=\"color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; vertical-align: middle; white-space: normal; border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);\" width=\"69\">" + bt + "</td> ";
        result += " <td style=\"color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; vertical-align: middle; white-space: normal; border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);\" width=\"139\">" + type + "</td> ";
        result += " <td style=\"color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);\" width=\"324\">" + explain + "</td> ";
        result += "</tr>";

        try
        {
            if (apidic[name] == null) { }
        }
        catch (Exception)
        {
            apidic.Add(name, "");
        }

        apidic[name] += result;
    }
}