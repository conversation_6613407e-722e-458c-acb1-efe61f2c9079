using LitJson;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using x.Encry;
using System.Data;
using System.Collections;
using com.cotees;
using System.Text.RegularExpressions;

/// <summary>
/// fuzhu 的摘要说明
/// </summary>
public class fuzhu : globalClass
{
    private HttpContext context;
    public fuzhu(HttpContext _context = null)
    {
        if (_context == null)
        {
            _context = HttpContext.Current;
        }
        context = _context;
    }

    public string uploadFile(string pathname = "file_upload", string limitFileTypes = ".PNG,.JPG,.GIF,.JPEG,.DOCX,.DOC,.PPTX,.XLS,.XLSX,.ZIP,.DOC,.DOCX,.MP4,.PDF,.RTF,.PPT,.TXT", bool onlyOne = false)
    {
        string res = string.Empty;

        //文件参数
        string ext = string.Empty; // 拓展名
        string md5 = string.Empty; //文件MD5
        string path = string.Empty; // 文件存放路径


        HttpFileCollection fsc = context.Request.Files;
        HttpPostedFile file = null;
        for (int i = 0; i < fsc.Count; i++)
        {
            string filename = string.Empty;
            file = fsc[i];
            res += res == "" ? "" : ",";
            filename = file.FileName;
            if (filename == "blob")
            {
                filename = "test.png";
            }
            ext = Path.GetExtension(filename);
            md5 = GetMD5HashFromStream(file.InputStream);
            path = "~/images/upload/";
            checkPath(path);

            path += pathname + "/";
            checkPath(path);

            if (("," + limitFileTypes + ",").IndexOf("," + ext.ToUpper() + ",") == -1)
            {
                if (!string.IsNullOrEmpty(ext))
                {
                    res += ext + "|文件类型不允许上传";
                }
                continue;
            }

            path = path + md5 + ext;
            file.SaveAs(System.Web.HttpContext.Current.Server.MapPath(path));

            path = path.Replace("~/", "../");
            res += path;

            if (onlyOne)
            {
                break;
            }
        }
        return res;
    }

    public void checkCode()
    {
        string sessionCode = string.Empty;
        string code = req("code");
        if (string.IsNullOrEmpty(code)) sendResponse("请输入验证码");
        try
        {
            sessionCode = Session["CheckCode"].ToString();
        }
        catch (Exception)
        {
            sendResponse("请重新输入验证码");
        }
        if (code.ToUpper() != sessionCode.ToUpper()) sendResponse("您输入的验证码错误");
        Session["CheckCode"] = null;
    }

    public List<SqlParameter> collectReqParames()
    {
        bool checkResult = false;
        return collectReqParames(out checkResult);
    }

    public List<SqlParameter> collectReqParames(out bool checkResult)
    {
        checkResult = true;
        string repeatText = ",";
        string text = string.Empty;
        string doname = context.Request.QueryString["do"] + "";
        SqlParameter[] p = new SqlParameter[] { };
        List<SqlParameter> list = new List<SqlParameter>();

        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            text = context.Request.Form.Keys[i];
            if (text == null)
            {
                continue;
            }
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                if (text.Length < 128)
                {
                    list.Add(
                        new SqlParameter("@" + text, context.Request.Form[i])
                    );
                    if (context.Request.Form[i] == string.Empty)
                    {
                        checkResult = false;
                    }
                }

            }
        }
        for (int i = 0; i < context.Request.QueryString.Count; i++)
        {
            text = context.Request.QueryString.Keys[i];
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                list.Add(
                    new SqlParameter("@" + text, context.Request.QueryString[i])
                );
                if (context.Request.QueryString[i] == string.Empty)
                {
                    checkResult = false;
                }
            }
        }
        list.Add(new SqlParameter("@userid", uConfig.p_uid));
        list.Add(new SqlParameter("@user_ip", getUserIP(HttpContext.Current)));
        return list;
    }

    public void check_sign(string secret_key = "")
    {
        if (sign(secret_key) != req("sign"))
        {
            sendResponse("sign校验有误");
        }
    }

    public void checkTimesTampSign(string secret_key = "")
    {
        string timestamp = req("timestamp");
        string signstr = secret_key + timestamp;

        //sendResponse(signstr + "----" + md5(signstr).ToLower() + "----" + req("sign"));
        if (md5(signstr).ToLower() != req("sign"))
        {
            sendResponse("sign校验有误");
        }
    }

    public string sign(string secret_key)
    {
        string repeatText = ",";
        string text = string.Empty;
        SortedDictionary<string, object> dic = new SortedDictionary<string, object>();

        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            text = context.Request.Form.Keys[i];
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                dic.Add(text, context.Request.Form[i] + "");
            }
        }
        for (int i = 0; i < context.Request.QueryString.Count; i++)
        {
            text = context.Request.QueryString.Keys[i];
            if (text.IndexOf("];") == -1 && repeatText.IndexOf("," + text + ",") == -1)
            {
                repeatText += text + ",";
                dic.Add(text, context.Request.QueryString[i] + "");
            }
        }

        string str = string.Empty;
        foreach (var item in dic)
        {
            if (!string.IsNullOrEmpty(item.Value + ""))
            {
                if (item.Key == "sign")
                {
                    continue;
                }
                str += item.Key + '=' + item.Value + '&';
            }
        }
        str = str.Trim('&') + "&" + secret_key;

        int showTest = 1;
        if (showTest == 1)
        {
            Dictionary<string, object> tempd = new Dictionary<string, object>();
            tempd.Add("signstr", str);
            sendResponse(md5(str).ToLower(), 0, tempd);
        }

        str = md5(str).ToLower();
        return str;
    }


    public string getCond()
    {
        pager_cond = pager_cond.Replace("@{ip}", getUserIP());
        List<string> arr = getContainerMul(pager_cond, "@{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = sreq(value);
            pager_cond = pager_cond.Replace("@{" + arr[i] + "}", value);
        }

        arr = getContainerMul(pager_cond, "#{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = req(value);
            pager_cond = pager_cond.Replace("#{" + arr[i] + "}", value);
        }
        return pager_cond;
    }


    public List<string> getCheckValues(string name)
    {
        string temp = string.Empty;
        return getCheckValues(name, out temp);
    }

    public List<string> getCheckValues(string name, out string outValue, string fgf = ",")
    {
        string key = string.Empty;
        string text = string.Empty;
        outValue = string.Empty;
        List<string> ls = new List<string>();
        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            key = context.Request.Form.Keys[i];
            text = getContainer(key, name + ":[", "];");
            if (!string.IsNullOrEmpty(text))
            {
                ls.Add(text);
                outValue += string.IsNullOrEmpty(outValue) ? "" : fgf;
                outValue += text;
            }
        }
        return ls;
    }

    public static string response_msg_lable = "info";

    public void setResponseLable(string name)
    {
        response_msg_lable = name;
    }

    public void sendResponse(string message, int code = -1, Dictionary<string, object> dic = null)
    {
        if (dic == null)
        {
            dic = new Dictionary<string, object>();
        }
        Dictionary<string, object> _json = new Dictionary<string, object>();
        _json.Add("code", code);
        _json.Add(response_msg_lable, message);
        foreach (var k in dic)
        {
            _json.Add(k.Key, k.Value);
        }

        string result = JsonMapper.ToJson(_json);

        if (response_aes_encry)
        {
            result = setAesResponse(result);
        }

        context.Response.ContentType = "application/json";
        context.Response.Write(result);
        context.Response.End();
    }


    public string langTrans(string message)
    {
        //string default_lang = "cn";
        //try
        //{
        //    default_lang = uConfig.p_lang != "" ? uConfig.p_lang : default_lang;
        //    byte[] buffer = File.ReadAllBytes(Server.MapPath("~/langs/" + default_lang + ".json"));
        //    JsonData jd = JsonMapper.ToObject(Encoding.UTF8.GetString(buffer));
        //    message = jd[message] + "";
        //}
        //catch (Exception)
        //{

        //}
        return message;
    }

    public void sendResponseTrans(string message, int code = -1, Dictionary<string, object> dic = null)
    {
        message = langTrans(message);
        sendResponse(message, code, dic);
    }

    public void sendRsp(int res, string success_result, string error_result)
    {
        string[] g;
        if (res > 0)
        {
            sendResponse(success_result, 1);
        }
        else
        {
            int errcode = -1;
            if (error_result.IndexOf('|') != -1)
            {
                g = error_result.Split('|');
                errcode = Convert.ToInt16(g[1]);
                error_result = g[0];
            }
            sendResponse(error_result, errcode);
        }
    }

    public void check_exist(string[] g, bool jsResponse = false)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        foreach (string item in g)
        {
            var g2 = item.Split(',');
            var tiptext = "请填写完整的信息";
            if (g2.Length == 2)
            {
                tiptext = g2[1] + "不能为空";
            }
            else
            {
                tiptext = "必须填写参数" + item;
            }

            bool checkint = false;
            bool checkdate = false;
            bool checkOptionint = false;
            bool checkPlusint = false;

            bool checkres = true;
            if (g2[0].IndexOf("-int") != -1) { g2[0] = g2[0].Replace("-int", ""); checkint = true; }
            if (g2[0].IndexOf("-date") != -1) { g2[0] = g2[0].Replace("-date", ""); checkdate = true; }
            if (g2[0].IndexOf("-oint") != -1) { g2[0] = g2[0].Replace("-oint", ""); checkOptionint = true; }
            if (g2[0].IndexOf("-pint") != -1) { g2[0] = g2[0].Replace("-pint", ""); checkPlusint = true; }
            string data = req(g2[0]);

            checkres = !checkres || (checkint && !IsNumeric(data)) ? false : checkres;
            checkres = !checkres || (checkdate && !IsDate(data)) ? false : checkres;
            checkres = !checkres || (checkOptionint && data != "0" && data != "1") ? false : checkres;
            checkres = !checkres || (checkPlusint && !IsNumeric(data) && Convert.ToDouble(data) <= 0) ? false : checkres;
            checkres = !checkres || string.IsNullOrEmpty(data) ? false : checkres;



            if (false == checkres)
            {
                if (jsResponse)
                {
                    sendJs(tiptext);
                    return;
                }
                dic.Add("code", -1);
                dic.Add(response_msg_lable, tiptext);
                context.Response.ContentType = "application/json";
                context.Response.Write(JsonMapper.ToJson(dic));
                context.Response.End();
            }
        }
    }

    public bool exists(string name, bool checkAll = true)
    {
        string res;
        if (json_data != null)
        {
            try
            {
                res = json_data[name] + "";
            }
            catch (Exception)
            {
                res = null;
            }
        }
        else
        {
            res = context.Request.Form[name];
            if (checkAll && string.IsNullOrEmpty(res))
            {
                res = context.Request.QueryString[name];
            }
        }
        return res != null;
    }


    public string param(string name, string paramName = "select")
    {
        string key = string.Empty;
        var numIndex = string.Empty;
        string text = string.Empty;
        List<string> ls = new List<string>();
        for (int i = 0; i < context.Request.Form.Count; i++)
        {
            key = context.Request.Form.Keys[i];
            numIndex = getContainer(key, paramName + "[", "]");
            if (numIndex != "")
            {
                text = getContainer(key, paramName + "[" + numIndex + "][", "]");
                if (text == name)
                {
                    return SafeSql(req(key));
                }
            }
        }
        return string.Empty;
    }

    public string req(string name, bool checkAll = true)
    {
        string res = string.Empty;
        if (json_data != null)
        {
            try
            {
                res = json_data[name] + "";
            }
            catch (Exception)
            {
            }
        }
        else
        {
            res = context.Request.Form[name] + "";
            if (checkAll && string.IsNullOrEmpty(res))
            {
                res = context.Request.QueryString[name] + "";
            }
        }
        return res;
    }
    public string sreq(string name, bool checkAll = true)
    {
        return SafeSql(req(name, checkAll));
    }

    public bool empty(string name, bool checkAll = true)
    {
        return string.IsNullOrEmpty(req(name, checkAll));
    }
    public string select(bool check, string v1 = "", string v2 = "")
    {
        if (check)
        {
            return v1;
        }
        return v2;
    }


    public string sqlreq(string name, bool checkAll = true)
    {
        string res = string.Empty;
        res = req(name, checkAll);
        res = SafeSql(res);
        return res;
    }

    JsonData json_data;
    bool response_aes_encry = false;
    string aes_key = string.Empty;
    public string setAesResponse(string text)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        dic.Add("code", 1);
        dic.Add(response_msg_lable, "success");
        dic.Add("data", aes.AESEncrypt(text, aes_key, "115a8d5de3e196d1"));
        return JsonMapper.ToJson(dic);
    }
    public string getResult(string message, int code = -1, Dictionary<string, object> dic = null)
    {
        string result = getResBody(message, code, dic);
        if (response_aes_encry)
        {
            result = setAesResponse(result);
        }
        return result;
    }
    public void check_data(bool _resultIsAes = false, bool _requestIsAes = true)
    {
        response_aes_encry = _resultIsAes;
        string result = check_data_call(_requestIsAes);
        if (!string.IsNullOrEmpty(result))
        {
            context.Response.ContentType = "application/json";
            context.Response.Write(result);
            context.Response.End();
        }
    }
    List<string> _datePams = new List<string>();
    public void pamsdate(List<string> _date)
    {
        _datePams = _date;
    }
    public void pamsadd(string name, string value)
    {
        json_data[name] = value;
    }
    public SqlParameter[] pams
    {
        get
        {
            List<SqlParameter> pam = new List<SqlParameter>();
            try
            {
                IDictionary dic = json_data;
                foreach (string item in dic.Keys)
                {
                    string _name = item + "";
                    object _value;
                    if (json_data[item] == null)
                    {
                        _value = DBNull.Value;
                    }
                    else
                    {
                        _value = json_data[item] + "";
                    }
                    for (int i = 0; i < _datePams.Count; i++)
                    {
                        if (_name == _datePams[i])
                        {
                            _value = new DateTime(1970, 1, 1, 8, 0, 0, 0).AddSeconds(Convert.ToDouble(_value)).ToString();
                        }
                    }
                    pam.Add(new SqlParameter("@" + _name, _value));
                }
                pam.Add(new SqlParameter("@ip", getUserIP()));
            }
            catch (Exception s)
            {
                string temp = s.Message;
            }
            return sqlParamesCheck(needparams, pam.ToArray());
        }
    }
    public string check_data_call(bool _requestIsAes = true)
    {
        //1.客户端随机生成AES秘钥
        //2.通过AES秘钥加密当前时间和业务数据
        //3.通过RSA公钥加密AES秘钥
        //4.发送加密后的当前时间和加密后的业务数据和加密后的AES秘钥
        //5.服务器收到数据拿RSA私钥解密加密后的AES秘钥,得到AES秘钥
        //6.服务器拿AES秘钥即可解密客户端发来的相关数据
        //7.服务器拿AES秘钥加密返回结果后再返回给客户端
        //8.客户端收到密文数据用之前生成的AES秘钥解密

        string time = req("time");
        string data = req("data");
        string auth = req("auth");
        try
        {
            if (_requestIsAes)
            {
                try
                {
                    auth = rsa.RsaDecrypt(auth);
                    aes_key = auth;
                }
                catch (Exception ex)
                {
                    response_aes_encry = false;
                    return getResult("操作失败,请再重试~");
                    return getResult("AUTH:" + ex.Message);
                }
                try
                {
                    data = aes.AESDecrypt(data, auth, "115a8d5de3e196d1");
                }
                catch (Exception ex)
                {
                    response_aes_encry = false;
                    return getResult("操作失败,请再重试~");
                    return getResult("DATA:" + ex.Message);
                }

                try
                {
                    time = aes.AESDecrypt(time, auth, "115a8d5de3e196d1");
                }
                catch (Exception ex)
                {
                    response_aes_encry = false;
                    return getResult("操作失败,请再重试~");
                    return getResult("TIME:" + ex.Message + ":" + data + ":" + auth + ":" + time);
                }
                DateTime dtime;
                if (time.Length == 10)
                {
                    DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
                    long lTime = long.Parse(time + "0000000");
                    TimeSpan toNow = new TimeSpan(lTime);
                    dtime = dtStart.Add(toNow);
                }
                else
                {
                    DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
                    long lTime = long.Parse(time + "0000");
                    TimeSpan toNow = new TimeSpan(lTime);
                    dtime = dtStart.Add(toNow);
                }


                if (GetNowTimeSpanSec(dtime) > 600)//10分钟
                {
                    return getResult("接口已到期");
                }
            }
            else
            {
                using (var reader = new System.IO.StreamReader(context.Request.InputStream))
                {
                    data = reader.ReadToEnd();
                }
            }


            try
            {
                json_data = JsonMapper.ToObject(data);
            }
            catch (Exception ex)
            {
                return getResult("请求数据解析失败");
            }
        }
        catch (Exception)
        {

        }
        return string.Empty;
    }


    public static int GetNowTimeSpanSec(DateTime _time)
    {
        DateTime now = DateTime.Now;

        TimeSpan ts = now.Subtract(_time);

        int sec = (int)ts.TotalSeconds;

        return sec;
    }

    #region 短信模块

    //发送短信
    public string send_sms()
    {
        string phone = req("phone");
        string scene = req("scene");
        string sms_text = string.Empty;

        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        string sql = string.Empty;
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@scene", scene));
        sql = " [send_sms] @phone,@scene ";
        dt = db.getDataTable(sql, pams.ToArray());
        if (dt.Rows.Count > 0)
        {
            if (dt.Rows[0]["code"] + "" != "1")
            {
                sendResponse(dt.Rows[0]["msg"] + "", (int)dt.Rows[0]["code"]);
            }
            sms_text = dt.Rows[0]["sms_text"] + "";
        }

        return sms_text;
    }

    //短信发送失败记录
    public int sms_error()
    {
        string phone = req("phone");
        string scene = req("scene");

        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        string sql = string.Empty;
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@scene", scene));
        sql = " update sms_message_list set send_times=send_times-1 where phone=@phone and scene=@scene ";
        return db.ExecuteNonQuery(sql, pams.ToArray());
    }

    //短信验证
    public void sms_verify()
    {
        string phone = req("phone");
        string scene = req("scene");
        string code = req("code");
        if (string.IsNullOrEmpty(code))
        {
            sendResponse("短信码不正确", -2);
        }

        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();
        string sql = string.Empty;
        DataTable dt = new DataTable();

        pams.Add(new SqlParameter("@phone", phone));
        pams.Add(new SqlParameter("@scene", scene));
        pams.Add(new SqlParameter("@code", code));
        sql = " update sms_message_list set sms_text=null where phone=@phone and scene=@scene and sms_text=@code and DATEDIFF(SECOND,send_date,GETDATE())<600 ";
        int res = db.ExecuteNonQuery(sql, pams.ToArray());
        if (res == 0)
        {
            sendResponse("短信码不正确", -2);
        }
    }

    #endregion


    List<string> pager_cols = new List<string>();
    string pager_cond = string.Empty;
    bool pager_fastResponse = false;
    int response_type = 0;
    bool response_html = false;
    bool _showNull = true;

    public void setResponseData(bool _html, bool is_show_null = true)
    {
        response_html = _html;
        _showNull = is_show_null;
    }

    public void pager_list(List<string> lists, bool fast_response = false, int rt = 0)
    {
        pager_cols = lists;
        pager_fastResponse = fast_response;
        response_type = rt;
    }
    public void pager_where(bool when_this_success, string success_cond, string fail_cond = "")
    {
        if (when_this_success)
        {
            pager_cond += pager_cond == string.Empty ? string.Empty : " and ";
            pager_cond += " " + success_cond + " ";
        }
        else
        {
            if (!string.IsNullOrEmpty(fail_cond))
            {
                pager_cond += pager_cond == string.Empty ? string.Empty : " and ";
                pager_cond += " " + fail_cond + " ";
            }
        }
    }


    public Dictionary<string, object> pager_data(string db, string select_items, string orderby, string page, string size)
    {
        pager_cond = pager_cond.Replace("@{ip}", getUserIP());
        List<string> arr = getContainerMul(pager_cond, "@{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = sreq(value);
            pager_cond = pager_cond.Replace("@{" + arr[i] + "}", value);
        }

        arr = getContainerMul(pager_cond, "#{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            value = req(value);
            pager_cond = pager_cond.Replace("#{" + arr[i] + "}", value);
        }



        page = string.IsNullOrEmpty(page) ? "1" : page;
        page = (Convert.ToInt16(page) - 1).ToString();
        Dictionary<string, object> dic = jsonClass.queryPagerD(db, pager_cols, pager_cond, select_items, orderby, page, size, "{}", "","", _showNull);
        dic["page"] = Convert.ToInt16(dic["page"]) + 1;
        if (pager_fastResponse)
        {
            pager_common_response(dic);
        }
        return dic;
    }

    public Dictionary<string, object> pager_data(string db, string select_items, string orderby = "")
    {
        return pager_data(db, select_items, orderby, req("page"), req("limit"));
    }

    public Dictionary<string, object> pager_common_response(Dictionary<string, object> dic, bool is_response = true)
    {
        Dictionary<string, object> _data = new Dictionary<string, object>();
        if (response_html)
        {
            _data.Add("html", dic["pageHtml"]);
        }
        switch (response_type)
        {
            case 1:
                _data.Add("data", dic["data"]);
                dic = _data;
                break;
            default:
                _data.Add("pager", dic["pager"]);
                _data.Add("page", dic["page"]);
                _data.Add("limit", dic["limit"]);
                _data.Add("count", dic["total"]);
                _data.Add("list", dic["data"]);
                dic = new Dictionary<string, object>();
                dic.Add("data", _data);
                break;
        }

        if (is_response)
        {
            sendResponse("成功", 1, dic);
        }
        return dic;
    }


    public Dictionary<string, object> eachSql(string sql, string cols, bool is_response = true)
    {
        List<Dictionary<string, object>> _list = new List<Dictionary<string, object>>();
        Dictionary<string, object> _dic = new Dictionary<string, object>();
        List<object> _result = new List<object>();
        string[] n_arr;
        string n1 = string.Empty;
        string n2 = string.Empty;
        dbClass db = new dbClass();
        DataTable dt = db.getDataTable(sql, pams);
        string[] g = cols.Split(',');

        globalClass gc = new globalClass();
        string temp = string.Empty;
        string tempval = string.Empty;
        object value = string.Empty;

        if (g.Length == 1)
        {
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                n_arr = cols.Split(new string[] { " as " }, StringSplitOptions.None);

                temp = gc.getContainer(cols, "@", "(");
                tempval = gc.getContainer(cols, "@" + temp + "(", ")");
                cols = cols.Replace("@" + temp + "(" + tempval + ")", "");


                n1 = n_arr[0];
                n2 = n_arr.Length > 1 ? n_arr[1] : n_arr[0];
                value = dt.Rows[i][n1];
                value = funCheck(temp, value, tempval);
                _result.Add(value);

                _dic = new Dictionary<string, object>();
                _dic.Add(n2, value);
                _list.Add(_dic);
            }
            _dic = new Dictionary<string, object>();
            if (cols.IndexOf("as") != -1)
            {
                _dic.Add("data", _list);
            }
            else
            {
                _dic.Add("data", _result);
            }
        }
        else
        {
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                _dic = new Dictionary<string, object>();
                for (int t = 0; t < g.Length; t++)
                {
                    temp = gc.getContainer(g[t], "@", "(");
                    tempval = gc.getContainer(g[t], "@" + temp + "(", ")");
                    g[t] = g[t].Replace("@" + temp + "(" + tempval + ")", "");

                    n_arr = g[t].Split(new string[] { " as " }, StringSplitOptions.None);
                    n1 = n_arr[0];
                    n2 = n_arr.Length > 1 ? n_arr[1] : n_arr[0];

                    _dic.Add(n2, funCheck(temp, dt.Rows[i][n1], tempval));
                }
                _list.Add(_dic);
            }
            _dic = new Dictionary<string, object>();
            _dic.Add("data", _list);
        }

        if (is_response)
        {
            sendResponse("成功", 1, _dic);
        }

        return _dic;
    }

    public object funCheck(string fun_name, object key_value, string fun_val)
    {
        object _result = string.Empty;
        string[] pam = fun_val.Split(',');
        if (key_value == DBNull.Value || key_value == null)
        {
            return null;
        }
        switch (fun_name)
        {
            case "time":
                _result = Convert.ToInt64(ConvertDateToTimeSpan(key_value.ToString()));
                break;
            default:
                _result = key_value;
                break;
        }
        return _result;
    }

    public string at(string name, string default_text = "null")
    {
        string[] arr = name.Split(new string[] { " as " }, StringSplitOptions.None);
        string atNmae = string.Empty;
        name = arr[0];
        atNmae = "@" + name;
        if (arr.Length > 1)
        {
            atNmae = arr[1];
        }
        if (exists(name))
        {
            return atNmae;
        }
        return default_text;
    }

    public string formatOrderids(string orderids, int maxnum = 0)
    {
        string[] s = orderids.Split(',');
        int check_count = 0;

        orderids = "";
        for (int i = 0; i < s.Length; i++)
        {
            if (IsNumeric(s[i]) && s[i].Length < 10)
            {
                if (orderids != "")
                {
                    orderids += ",";
                }
                orderids += s[i];
                check_count += 1;
            }
            if (maxnum > 0)
            {
                if (check_count >= maxnum)
                {
                    break;
                }
            }
        }
        return orderids;
    }

    List<string> needparams = new List<string>();
    public void existsSqlParam(List<string> p)
    {
        needparams = p;
    }

    //有缺点，自己定义的参数也会被带进去
    public static SqlParameter[] sqlParamesCheck(List<string> _needparams, SqlParameter[] parames)
    {
        List<string> _existsKeys = new List<string>();
        List<SqlParameter> p = new List<SqlParameter>();
        foreach (SqlParameter _p in parames)
        {
            _existsKeys.Add(_p.ParameterName);
            p.Add(new SqlParameter(_p.ParameterName, _p.Value));
        }
        for (int i = 0; i < _needparams.Count; i++)
        {
            if (!_existsKeys.Exists(q => q == _needparams[i]))
            {
                _existsKeys.Add(_needparams[i]);
                p.Add(new SqlParameter(_needparams[i], DBNull.Value));
            }
        }
        parames = p.ToArray();
        return parames;
    }


    DataTable where_dt = new DataTable();
    public void set_log_where_dt(DataTable dt)
    {
        where_dt = dt;
    }
    public void log(string log_text, string where_status, bool noEmpty = false)
    {
        if (where_dt.Rows.Count > 0)
        {
            string[] arr = where_status.Split('|');
            string dt_name = where_status;
            string fz_name = where_status;
            if (arr.Length > 1)
            {
                dt_name = arr[0];
                fz_name = arr[1];
            }

            object obj_value = where_dt.Rows[0][dt_name];
            if (obj_value != DBNull.Value && obj_value.GetType() == (new DateTime()).GetType())
            {
                obj_value = Convert.ToDateTime(obj_value).ToString("yyyy-MM-dd HH:mm:ss");
            }

            if (obj_value + "" != req(fz_name))
            {
                if (!noEmpty || req(fz_name) != "")
                {
                    log(log_text);
                }
            }
        }
    }
    public void log(string log_text)
    {
        DataTable dt = new DataTable();
        List<SqlParameter> pams = new List<SqlParameter>();
        dbClass db = new dbClass();
        string sql = string.Empty;
        int res = 0;
        log_text = log_text.Replace("@{ip}", getUserIP());
        List<string> arr = getContainerMul(log_text, "@{", "}", true);
        for (int i = 0; i < arr.Count; i++)
        {
            string value = arr[i];
            if (!exists(arr[i]))
            {
                try
                {
                    object obj_value = where_dt.Rows[0][arr[i]];
                    if (obj_value != DBNull.Value && obj_value.GetType() == (new DateTime()).GetType())
                    {
                        obj_value = Convert.ToDateTime(obj_value).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    value = obj_value + "";

                }
                catch (Exception)
                {

                }
            }
            else
            {
                value = req(value);
            }
            log_text = log_text.Replace("@{" + arr[i] + "}", value);
        }

        string title = log_text.Split(' ')[0];
        if (log_text.Split(' ').Length > 1)
        {
            log_text = log_text.Replace(title + " ", "");
        }
        else
        {
            title = string.Empty;
        }

        pams.Add(new SqlParameter("@title", title));
        pams.Add(new SqlParameter("@log_text", log_text));
        sql = " insert into serv_admin_logs values(@servid,@title,@log_text,1,getdate()) ";
        res = db.ExecuteNonQuery(sql, pams.ToArray());
    }

    public bool IsPositiveNumber(string str)
    {
        str = req(str);
        return IsNumeric(str) && Convert.ToDouble(str) > 0;
    }



    public void sendJs(string message, string jump = "history.go(-1);")
    {
        string js = string.Empty;
        if (message != null)
        {
            js = "alert('" + message + "');";
        }
        context.Response.Write("<html><script>" + js + jump + "</script></html>");
        context.Response.End();
    }



    public bool limit_string(string key = "", string limit_list = "", string errmsg = "")
    {
        string[] g = limit_list.Split('|');
        bool is_exists = false;
        string key_value = req(key);
        for (int i = 0; i < g.Length; i++)
        {
            if (g[i] == key_value)
            {
                is_exists = true;
                break;
            }
        }

        if (!is_exists)
        {
            if (!string.IsNullOrEmpty(errmsg))
            {
                //{key} is error
                sendResponse(errmsg.Replace("{key}", key + " " + key_value + " "));
            }
        }

        return is_exists;
    }
    public bool is_Number(string key = "", string errmsg = "")
    {
        var key_value = req(key);
        if (!IsNumeric(req(key)))
        {
            if (!string.IsNullOrEmpty(errmsg))
            {
                //{key} is error
                sendResponse(errmsg.Replace("{key}", key + " " + key_value + " "));
            }
            return false;
        }

        return true;
    }
}