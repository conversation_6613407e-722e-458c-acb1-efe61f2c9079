<%@ Master Language="C#" AutoEventWireup="true" CodeFile="m.master.cs" Inherits="global_m" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=no" />

    <link href="../static/mgr/css/font-awesome.min.css" rel="stylesheet" />

    <link href="../static/mgr/layui/css/layui.css" rel="stylesheet" />

    <link href="../static/mgr/css/global.css" rel="stylesheet" />

    <link href="../static/mgr/css/css.css" rel="stylesheet" />

    <%--<script src="//cdn.bootcss.com/jquery/1.9.1/jquery.min.js"></script>--%>
    <script src="../static/mgr/js/jquery_1.9.1_jquery.min.js"></script>

    <script src="../static/mgr/js/modernizr.js"></script>

    <script src="../static/mgr/layer/layer.js"></script>

    <script src="../static/mgr/layui/layui.js"></script>

    <%--<script src="../static/mgr/js/layui-mz-min.js"></script>--%>

    <script src="../static/mgr/js/global.js"></script>

    <script src="../static/mgr/js/fuzhu.js"></script>

    <style>
        a {
            outline: 0!important;
            outline: none!important;
        }

        .manage-body {
            margin-left: 111px;
        }

        .sidebar {
            width: 111px;
        }

        @media screen and (max-width: 700px) {
            .sidebar {
                left: -190px;
            }

            .manage-body {
                margin-left: 0px;
            }
        }

        @media screen and (max-width: 765px) {
            #login-dropdown-form {
                position: relative;
            }
        }


        .nav-sidebar > li ul.sub-menu2 {
            background-color: #fff;
            border-top: 1px solid #e5e5e5;
            display: none;
            list-style: outside none none;
            margin: 0;
            padding: 0;
            position: relative;
        }



        .nav-sidebar ul.sub-menu2 ::before {
            -moz-border-bottom-colors: none;
            -moz-border-left-colors: none;
            -moz-border-right-colors: none;
            -moz-border-top-colors: none;
            border-color: #333;
            border-image: none;
            border-style: dotted;
            border-width: 0 0 0 1px;
            bottom: 0;
            content: "";
            display: block;
            left: 28px;
            position: absolute;
            top: 0;
            z-index: 1;
        }

        .nav-sidebar ul.sub-menu2 > li {
            margin-left: 0;
            position: relative;
        }

            .nav-sidebar ul.sub-menu2 > li::before {
                border-top: 1px dotted #333;
                content: "";
                display: inline-block;
                left: 28px;
                position: absolute;
                top: 17px;
                width: 7px;
            }


        .nav-sidebar .sub-menu > li.open2 ul.sub-menu2 {
            display: block;
        }


        .nav-sidebar ul.sub-menu2 > li > a {
            border-top: 1px dotted #e4e4e4;
            color: #616161;
            display: block;
            margin: 0;
            padding: 7px 0 9px 47px;
            position: relative;
        }

        .layui-form-label {
            width: 86px;
        }

        .layui-form-item {
            margin-bottom: 0px;
        }
    </style>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">
        <div class="navbar navbar-inverse navbar-fixed-top">
            <div class="container">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>

                    <a class="navbar-brand" style="cursor: pointer;" onclick="logoClick()">
                        <img src="../static/images/logo.png" style="position: relative; top: -5px; width: 48px;" />
                    </a>
                </div>
                <div class="navbar-collapse collapse">

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                    <ul class="nav navbar-nav">
                        <li><a href="../index.aspx" target="_blank">主页</a></li>
                    </ul>
                    <%
              } %>
                    <ul class="nav navbar-nav navbar-right">
                        <li id="user-account">
                            <a data-toggle="dropdown" href="javascript:;" class="dropdown-toggle">
                                <span class="show_nick"></span><i class="caret"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <li>
                                    <a href="../mgr_data/out.aspx">
                                        <span class="glyphicon glyphicon-off"></span>&ensp;退出
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li id="login-dropdown">
                            <a href="../mgr_data/out.aspx" class="navbar-link login-dropdown-toggle">退出
                            </a>
                            <div class="clearfix"></div>
                            <div id="login-dropdown-form">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon"><span class="glyphicon glyphicon-user"></span></span>
                                    <input id="nick" name="Username" type="text" class="form-control" placeholder="用户名/邮箱" />
                                </div>
                                <br />
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon"><span class="glyphicon glyphicon-lock"></span></span>
                                    <input id="pass" name="Password" type="password" class="form-control" placeholder="密码" />
                                </div>
                                <div id="login-error" class="pull-left"></div>
                                <div class="pull-right" style="margin-top: 6px;">
                                    <a class="btn btn-primary" href="../mgr_data/out.aspx">退出</a>
                                </div>
                                <div style="clear: both;"></div>
                            </div>
                        </li>
                    </ul>

                </div>

            </div>
        </div>
        <div>
            <div id="page-loading-bar">
                <dl>
                    <dd></dd>
                </dl>
            </div>

            <div class="sidebar">
                <ul class="nav nav-sidebar">
                    <li>
                        <a href="../mgr_data/_index.aspx">运营信息</a>
                    </li>
                    <li>
                        <a href="../mgr_data/app_group.aspx">业务组</a>
                    </li>
                    <li>
                        <a href="../mgr_data/bus.aspx">业务管理</a>
                    </li>
                    <li>
                        <a href="../mgr_data/orders.aspx">任务列表</a>
                    </li>
                    <li>
                        <a href="../mgr_data/recordslist.aspx">消耗记录</a>
                    </li>

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                    <li>
                        <a href="../mgr_data/new_users.aspx">用户管理</a>
                    </li>

                    <li>
                        <a href="../mgr_data/system.aspx">修改密码</a>
                    </li>
                    <%
              } %>

                </ul>
            </div>

            <div class="manage-body">
                <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server"></asp:ContentPlaceHolder>
            </div>

            <div class="clearfix"></div>


        </div>

        <script src="../static/mgr/js/bootstrap.min.js"></script>

        <script src="../static/mgr/js/platform.js"></script>


        <script>var userNick2 = getCookie("nick_admin");; if (userNick2) { $(".show_nick").html(userNick2) };</script>

        <script>

            function login() {
                var nick = $("#nick").val();
                var pass = $("#pass").val();

                $.ajax({
                    type: "POST",
                    url: "../Api/user.aspx?action=login",
                    data: { nick: nick, pass: pass },
                    datatype: "html",
                    success: function (data) {
                        if (data.indexOf(".aspx") != -1) {
                            location.href = data;
                        } else {
                            alert(data);
                        }
                    },
                    error: function () {
                        alert(_expText);
                    }
                });
            }
        </script>


        <script>
            $(".sub-menu>li>a").click(function () {
                $(this).closest("li").toggleClass("open2");
            })
        </script>




        <div class="fade modal m2" id="tipModal2" tabindex="998" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"></span><span class="sr-only">Close</span></button><h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <div class="txtarea-wrap tipData" style="color: #333;">
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="show-dialog" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">系统提示</h4>
                    </div>
                    <div class="modal-body tipData">
                    </div>
                </div>
            </div>
        </div>


        <script>
            var tips2 = function (text, t) {
                t = typeof (t) == undefined ? "系统提示" : t;
                //$("#tipModal2 .modal-title").html(t);
                //$("#tipModal2 .tipData").html(text);
                //$('#tipModal2').modal();

                $("#show-dialog .modal-title").html(t);
                $("#show-dialog .tipData").html(text);
                $('#show-dialog').modal();
            }

            // 复制函数
            function copyText() {
                var Url2 = document.getElementById("cardData");
                Url2.select(); // 选择对象
                document.execCommand("Copy"); // 执行浏览器复制命令
            }

            function close_modal() {
                $('#show-dialog').modal('hide');
            }
        </script>


        <script>
            function toggleMenu() {
                var disp = $(".sidebar").css("display");
                if (disp == "none") {
                    location.href = "../index.aspx";
                    return;
                }
                var left = $(".sidebar").css("left");
                if (left == "0px") {
                    $(".sidebar").animate({ "left": "-190px" });
                    $(".manage-body").animate({ "margin-left": "0px" });
                } else {
                    $(".sidebar").animate({ "left": "0px" });
                    $(".manage-body").animate({ "margin-left": "190px" });
                }
            }

            function logoClick() {
                toggleMenu();
            }

            function event_create(arr) {
                var tiptext = "是否确认此次操作？";
                if (arr['event'] == "delete") {
                    tiptext = "是否确认删除此条数据？";
                }

                layer.confirm(tiptext, {
                    btn: ['确认', '取消']
                }, function () {
                    var mark = mask = layer.load(2, { shade: [0.8, '#393D49'] });;
                    $.ajax({
                        type: "POST",
                        url: "../mgr_data/apidata.aspx?do=event_create",
                        data: arr,
                        dataType: "json",
                        success: function (data) {
                            layer.close(mask);
                            if (data.code == 1) {
                                if (typeof (getPager) == "function") {
                                    getPager();
                                } else {
                                    location.href = location.href;
                                }
                                layer.msg(data.msg, { icon: 1, time: 1200 });
                            } else {
                                layer.msg(data.msg, { icon: 2 });
                            }
                        },
                        error: function (data) {
                            layer.close(mask);
                            console.log("出现错误：", data);
                        }
                    });
                }, function () {
                });
            }

            var open_new_page = function (u, t) {
                location.href = u;

            }
        </script>
    </form>
</body>
</html>
