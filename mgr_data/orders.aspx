<%@ Page Title="订单列表" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="orders.aspx.cs" Inherits="u_records_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        var order_base_data = {}
    </script>
    
    <link href="../css/select2.min.css" rel="stylesheet" />
    <script src="../js/select2.min.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div>

        <div>
            <div class="input-group" style="padding: 6px;">


                <div class="input-group" style="padding: 6px;">

                    <span class="input-group-btn" style="font-size:12px;">
                        <select class=" appNames js-example-basic-single" id="app_code" style="border: 1px solid #ddd; font-size:12px;" onchange="getPager()">
                            <option value="">全部业务-all</option>
                            <asp:Repeater ID="status" runat="server">
                                <ItemTemplate>
                                    <option value="<%#Eval("appcode") %>"><%#Eval("typename") %><%#Eval("appname") %></option>
                                </ItemTemplate>
                            </asp:Repeater>
                        </select>
                    </span>
                    <style>
                        .select2-results__option--selectable{
                            font-size:12px;
                        }
                        .select2-container--default .select2-results>.select2-results__options{
                            max-height:600px;
                        }
                    </style>
                    <script>
                        $(document).ready(function () {
                            $('.appNames').select2();
                        });
                    </script>

                    <span class="input-group-btn">
                        <select class="btn btn-sm appStatus" id="order_state" style="border: 1px solid #ddd;" onchange="getPager()">
                            <option value="">全部状态</option>
                            <option value="未开始">未开始</option>
                            <option value="进行中">进行中</option>
                            <option value="退单中">退单中</option>
                            <option value="已退单">已退单</option>
                            <option value="已完成">已完成</option>
                            <option value="订单异常">订单异常</option>
                            <option value="其他异常">其他异常</option>
                        </select>
                    </span>


                    <input id="search-id" type="text" placeholder="ID" class="input-sm form-control" onchange="searchList()" style="width: 80px;" />
                    <input id="search-bid" type="text" placeholder="业务ID" class="input-sm form-control" onchange="searchList()" style="width: 80px;" />
                    <input id="search-url" type="text" placeholder="链接" class="input-sm form-control" onchange="searchList()" style="width: 120px;" />



                    <span class="input-group-btn">
                        <a class="btn btn-sm btn-primary" onclick="searchList()">搜索</a>
                        <a class="btn btn-sm btn-danger" onclick="javascript:location.href=location.href;"><span class="fa-refresh fa"></span></a>
                    </span>
                </div>
                <div style="padding: 5px;">
                    <%--<input type="checkbox" class="shangjia_refund_btn" onclick="searchList()" />&nbsp;商家API取消人气任务--%>
                    
                    &nbsp;&nbsp;<input type="checkbox" class="record_orders" />&nbsp;查看7天前的任务
                </div>
            </div>
        </div>


        <script>
            layui.use(['form', 'layedit', 'laydate'], function () {
            });
        </script>



        <table class="ctTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>任务ID</th>
                    <th>用户</th>
                    <th>类型</th>
                    <th>链接</th>
                    <th>初始/需求/当前</th>
                    <th>点数</th>
                    <th>任务状态</th>
                    <th>关闭不退单</th>
                    <th></th>
                    <th>创建时间</th>
                    <th>完成时间</th>
                </tr>
            </thead>
            <tbody id="list-display">
            </tbody>
        </table>


        <style>
            /*table*/
            .moTable {
                border-spacing: 0;
                width: 100%;
                background: #fff;
                border: 1px solid rgba(28,31,35,.08);
                border-radius: 3px;
            }

                .moTable thead th {
                    font-weight: 600;
                    color: #000;
                    font-size: 12px;
                    text-align: left;
                    padding: 8px;
                    border-left: 0;
                    position: relative;
                }

                .moTable td {
                    border-top: 1px solid rgba(28,31,35,.08);
                    font-size: 12px;
                    padding: 8px;
                    color: rgba(23,26,28,.65);
                }

                .moTable tr:hover td {
                    background: rgba(46,50,56,.05);
                }

                .moTable th, .moTable td {
                    word-break: keep-all;
                    /*white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;*/
                }
        </style>




        <div style="text-align: center;">
            <div style="margin-top: 10px;">
                <div class="dataTables_info" id="editable_info" role="status" aria-live="polite">当前显示 1 到 <span class="page_size">1</span> 条  共 <span class="page_count">0</span> 条记录</div>
            </div>
            <div>
                <div class="dataTables_paginate paging_simple_numbers" id="editable_paginate">
                    <ul class="pagination">
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <script>
        var main = '<%=uConfig.superAdminPrefix %>';

        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }
    </script>
    <script>
        var pager_size = 30;
        var pager_index = 0;


        function searchList() {
            pager_index = 0;
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=page",
                data: { s: pager_size, p: pager_index, page: 'orders', s_id: $("#search-id").val(), s_bid: $("#search-bid").val(), s_url: $("#search-url").val(), opt: $('#app_code').val(), opt2: request("param", location.href), shangjia_refund: checkIsSelect($(".shangjia_refund_btn")), record_orders: checkIsSelect($(".record_orders")), status: $('#order_state').val(), status2: $('#work_state').val() },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        order_base_data = {};
                        for (var i = 0; i < data.length; i++) {

                            order_base_data[data[i].id] = {
                                note: data[i].note
                            };

                            var btn = "";
                            var base_data = {
                                start: data[i].start,
                                order_num: data[i].order_num,
                                now: data[i].now,
                                order_status: data[i].order_status
                            };
                            var names = "";
                            var order_renqi_num = "";
                            var trStyle = "";
                            var renqi_type = "";
                            var refundId = data[i].id;

                            if (data[i].renqi_num != "") {
                                refundId = data[i].id + "_" + data[i].order_num + "_" + data[i].start + "_" + data[i].now + "_" + (data[i].othernum == "" ? "n" : data[i].othernum) + "_renqi";

                                renqi_type = "renqi";
                                trStyle = "background-color:ghostwhite;";

                                names += "<br><p style='color:#666;font-weight: 100;'>（" + data[i].renqi_num + "人气）</p>";

                                base_data.start = data[i].rqStart;
                                base_data.order_num = data[i].rqNum;
                                base_data.now = data[i].rqNow;
                                if (data[i].online == "0") {
                                    base_data.start = "-1";
                                    base_data.now = "-1";
                                    base_data.order_num = data[i].order_num;

                                    if (data[i].order_status == "进行中") {
                                        base_data.order_status = "<span style='color:#ccc;'>未开播</span>";
                                    }
                                } else {
                                    if (data[i].order_status == "进行中") {
                                        base_data.order_status = "<span style='color:green;'>开播中</span>";
                                    }
                                }

                                if (data[i].plid != "") {
                                    btn += "<p><a onclick=\"leaveroom('" + data[i].plid + "')\" style='color:darkslategray;'>离开</a></p>";
                                }

                                if (data[i].currentPeople != "") {
                                    order_renqi_num = "<p style='font-size:12px;color:slategray;'>当前人气：<span style='color:slategray'>" + data[i].currentPeople + "</span></p>";
                                }
                            }


                            if (data[i].isExpire == "0") {
                                btn += "<p><a onclick=\"o_status(" + data[i].id + ",'restatus')\" style='color:blue;'>" + (data[i].order_status == "已完成" ? "暂停" : "恢复") + "</a></p>";
                            }

                            if (data[i].shangjia_refund == "1") {
                                btn += "<p><span style='color:navy;'>【API取消】</span></p>";
                            }

                            var tr = "";
                            tr += "<tr style='" + trStyle + "'>";
                            tr += ("<td>" + data[i].id + "</td>");
                            tr += ("<td>" + "<p style='font-size: 12px;color: darkgrey;text-decoration: underline;display:inline-block;'>" + data[i].transactionID + "</p>" + "</td>");
                            tr += ("<td>" + data[i].nick + "</td>");
                            tr += ("<td style='min-width:100px;'><span style='font-weight:bold;color:#6f6f6f;;'>" + data[i].type_name + " - " + data[i].app_name + (data[i].otherinfo != "" ? '<span style="color:black;">（' + data[i].othernum + '小时）</span>' : "") + names + "</span></td>");
                            tr += ("<td  style='max-width:400px;word-wrap : break-word ;word-break:break-all;min-width:250px;'><p style='margin-bottom: 0;color: #ddd;font-size: 12px;'>" + (data[i].order_url != data[i].sUrl ? ((data[i].sUrl + "").indexOf("http") != -1 ? "<a target='_blank' href='" + data[i].sUrl + "'>" + data[i].sUrl + "</a>" : data[i].sUrl) : "") + "</p>" + ((data[i].order_url + "").indexOf("http") != -1 ? "<a target='_blank' href='" + data[i].order_url + "' style='color:#44a539;'>" + data[i].order_url + "</a>" : "<span style='color:#44a539;'>" + data[i].order_url) + order_renqi_num + "</span>" + (data[i].remark1 != "" ? "<p style='margin-top: 12px;'><img src='../static/images/warm99.png' style='width: 12px;' /><span style='font-size: 12px;color: #e2ac45;    cursor: pointer;'> " + data[i].remark1 + "</span></p>" : "") + (data[i].note == "" ? "" : "<p style='margin-botton:0px;'><span onclick='edit_note(this)' data-id='" + data[i].id + "' style='font-size: 12px;color: slategrey;'>note=" + data[i].note + "</span></p>") + "</td>");
                            tr += ("<td>" + base_data.start + " / " + base_data.order_num + " / " + base_data.now + "</td>");
                            tr += ("<td style='min-width:80px;'><span>" + data[i].cost + (data[i].market_money != "" ? "<span style='color:blue'> - " + data[i].market_money + "</span>" : "") + "</span>" + (data[i].ftip == "" ? "" : " / <span style='color:red'>" + data[i].ftip + "</span>") + "</td>");
                            tr += ("<td>" + statusToCode(base_data.order_status, refundId, 'ad') + (base_data.order_status == "退单中" ? "<a onclick='order_refund_fast(" + data[i].id + ",\"" + renqi_type + "\")' style='color:red;display: inline-block;width: 50px;'>快速取消</a>" : "") + btn + ("<a style='display: block;background: #ecf7e6;text-decoration: none;color: #44a539;'>" + data[i].status_flag + "</a>") + "</td>");
                            tr += ("<td>" + data[i].state_text + "</td>");
                            //tr += "<td><a onclick=\"morder(" + data[i].id + ",'" + data[i].order_url + "','" + data[i].order_num + "','" + data[i].start + "','" + data[i].now + "','" + data[i].order_status + "'," + data[i].othernum + ",'" + data[i].otherinfo + "')\">编辑</a></td>";


                            var type = "";


                            var select_data = [];
                            select_data.push(["未开始", "未开始"]);
                            select_data.push(["进行中", "进行中"]);
                            select_data.push(["退单中", "退单中"]);
                            select_data.push(["已退单", "已退单"]);
                            select_data.push(["已完成", "已完成"]);
                            select_data.push(["订单异常", "订单异常"]);
                            select_data.push(["其他异常", "其他异常"]);

                            var state_text = [];
                            state_text.push(["", "空"]);
                            state_text.push(["请求中", "请求中"]);
                            state_text.push(["请求完成", "请求完成"]);


                            var select_server = [];

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                            select_server.push(["10002", "转单系统"]);

                    <%
                      } %>
                            var cg = [];

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                            cg.push({ name: 'type', id: 'type', value: type, type: 'hidden' });
                            cg.push({ name: '任务状态', id: 'status', value: data[i].order_status, type: 'select', data: select_data });
                            cg.push({ name: '服务器', id: 'app_id', value: data[i].app_id, type: 'option', data: select_server });
                            cg.push({ name: '关闭不退', id: 'state_text', value: data[i].state_text, type: 'select', data: state_text });
                            cg.push({ name: '任务链接', id: 'url', value: data[i].order_url });
                            cg.push({ name: '需求数量', id: 'num', value: data[i].order_num });
                            cg.push({ name: '初始数量', id: 'start', value: data[i].start });
                            cg.push({ name: '当前数量', id: 'now', value: data[i].now });
                            cg.push({ name: 'ApiParam', id: 'api_params', value: data[i].api_params, type: 'textarea' });
                            cg.push({ name: '状态备注', id: 'remark1', value: data[i].remark1 });
                            <%
                      } %>

                            tr += "<td>"

                    <%if (uConfig.p_userNickAD == "gm-facai")
                      {
                    %>
                                + "<button style='display: inline-block;width: 35px;' type='button' class='el-button  el-button--primary el-button--mini' onclick='edit(" + edtext({ aname: 'mgr_data/apidata', title: "编辑任务", action: 'order_edit', id: data[i].id, data: cg }) + ")'><span>编辑</span></button>"
                    <%
                      } %>
                                + "</td>"




                            tr += ("<td>" + data[i].order_time + (data[i].refund_time ? "<p style='color:green;font-size:12px;'>申请:" + data[i].refund_time : "") + "</td>");
                            tr += ("<td>" + data[i].order_finish + "</td>");


                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".pagination").html(json.data.html);
                    } else {

                    }
                },
                error: function (ex) {
                    console.log('error', ex);
                    serr();
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    <script>

        function order_refund(id) {
            if (id.indexOf("renqi") != -1) {
                var arr = id.split('_');
                id = arr[0];
                var cg = [];
                cg.push({ name: '需求数量', id: 'neednum', value: arr[1], type: 'disabled' });
                cg.push({ name: '初始数量', id: 'startnum', value: arr[2], type: 'disabled' });
                cg.push({ name: '当前数量', id: 'nownum', value: arr[3], tip: '' });

                edit({ aname: 'mgr_data/apidata', title: '人气取消', action: 'renqi_refund', id: id, data: cg });
            } else {
                layer.confirm('您确认要申请取消吗，申请后将无法撤销！', {
                    btn: ['确认', '取消'] //按钮
                }, function () {
                    $.ajax({
                        type: "POST",
                        url: "../mgr_data/apidata.aspx?do=refund_order",
                        data: { id: id },
                        datatype: "json",
                        success: function (json) {
                            var callback;
                            if (json.code == 1) {
                                callback = rfPage;
                            }
                            _tip(json.msg, 1500, callback)
                        },
                        error: function () {
                            serr();
                        }
                    });
                }, function () {
                });
            }
        }

        function order_refund_fast(id) {
            layer.confirm('是否确认立即取消，请先确认任务已长时间未取消！', {
                btn: ['确认', '取消'] //按钮
            }, function () {
                $.ajax({
                    type: "POST",
                    url: "../mgr_data/apidata.aspx?do=refund_fast",
                    data: { id: id },
                    datatype: "json",
                    success: function (json) {
                        var callback;
                        if (json.code == 1) {
                            callback = rfPage;
                        }
                        _tip(json.msg, 1500, callback)
                    },
                    error: function () {
                        serr();
                    }
                });
            }, function () {
            });
        }

        function o_status(id, type) {
            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=o_status",
                data: { id: id, type: type },
                datatype: "json",
                success: function (json) {
                    var callback;
                    if (json.code == 1) {
                        layer.closeAll();
                        callback = getPager;
                    }
                    _tip(json.msg, 1500, callback)
                },
                error: function () {
                    serr();
                }
            });
        }


        function edit_note(e) {
            var id = $(e).data("id");
            var data = order_base_data[id].note;
            console.log('id', id, data);
            $(e).closest('p').html('<input value="' + data + '" data-id="' + id + '" onblur="blur_note(this)" style="padding:8px;" >');
            $('input[data-id="' + id + ']').focus();
        }

        function blur_note(e) {
            var id = $(e).data("id");
            var v = $(e).val();
            console.log('失去焦点', id, v);

            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=order_update",
                data: { id: id, note: v },
                datatype: "json",
                success: function (json) {
                    if (json.code == 1) {
                        order_base_data[id].note = v;
                        $(e).closest('p').html("<span onclick='edit_note(this)' data-id='" + id + "' style='font-size: 12px;color: slategrey;'>note=" + v + "</span>");
                    }
                },
                error: function () {
                    serr();
                }
            });

        }
    </script>
</asp:Content>

