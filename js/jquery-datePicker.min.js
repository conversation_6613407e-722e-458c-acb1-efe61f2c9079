$(function(d,e){var c={dContent:"datePicker-content",dBottom:"datePicker-bottom",dBBtn:"datePicker-bottom-btn",dBBConfirm:"datePicker-bottom-btn-confirm",dBBCancel:"datePicker-bottom-btn-cancel",dCFast:"datePicker-content-fast",dCFUl:"datePicker-content-fast-ul",dCFULi:"datePicker-content-fast-ul-li",dCCalender:"datePicker-content-calendar",dCCHours:"datePicker-content-calendar-hours",dCCHLeft:"datePicker-content-calendar-hours-left",dCCHContent:"datePicker-content-calendar-hours-content",dCCHRight:"datePicker-content-calendar-hours-right",dCCBody:"datePicker-content-calendar-body",dCCBLeft:"datePicker-content-calendar-body-left",dCCBRight:"datePicker-content-calendar-body-right"};var b={cBHeader:"calendarBody-header",cBHLeft:"calendarBody-header-left",cBHCenter:"calendarBody-header-content",cBHCText:"calendarBody-header-content-text",cBHRNextMonth:"calendarBody-header-right-nextMonth",cBHLPrevMonth:"calendarBody-header-left-prevMonth",cBHRNextYear:"calendarBody-header-right-nextYear",cBHLPrevYear:"calendarBody-header-left-prevYear",cBHRight:"calendarBody-header-right",cBContent:"calendarBody-content",cBCTitle:"calendarBody-content-title",cBCTText:"calendarBody-content-title-text",cBCBody:"calendarBody-content-body",cBCBRow:"calendarBody-content-body-row",cBCBRDate:"calendarBody-content-body-row-date",cHLeft:"calendarHour-day",cHRight:"calendarHour-hour",cHLInput:"calendarHour-day-input",cHRInput:"calendarHour-hour-input",cHRPull:"calendarHour-hour-pull",cHRPContent:"calendarHour-hour-pull-content",cHRPFooter:"calendarHour-hour-pull-footer",cHRPFCancel:"calendarHour-hour-pull-footer-cancel",cHRPFConfirm:"calendarHour-hour-pull-footer-confirm",cHRPCHours:"calendarHour-hour-pull-content-hour",cHRPHUl:"calendarHour-hour-pull-hour-ul",cHRPCMinute:"calendarHour-hour-pull-content-minute",cHRPMUl:"calendarHour-hour-pull-minute-ul",cHRPCSecond:"calendarHour-hour-pull-content-second",cHRPSUl:"calendarHour-hour-pull-second-ul",};function a(f){this.options={container:"#datePicker",width:646,reportTimeType:4,isDouble:true,isFast:false,startDom:f.container,endDom:f.container,disabledDate:false,format:"YYYY-MM-DD HH:mm:ss",yes:function(h,g){},fastTime:{"最近7天":{startTime:moment().subtract(7,"day").format("YYYY-MM-DD HH:mm:ss"),endTime:moment().format("YYYY-MM-DD HH:mm:ss")},"最近一个月":{startTime:moment().subtract(1,"month").format("YYYY-MM-DD HH:mm:ss"),endTime:moment().format("YYYY-MM-DD HH:mm:ss")},"最近三个月":{startTime:moment().subtract(3,"month").format("YYYY-MM-DD HH:mm:ss"),endTime:moment().format("YYYY-MM-DD HH:mm:ss")},}};this.options=e.extend({},this.options,f);this.init();return this}a.prototype.init=function(){this.initOptions();this.resetTime();this.createDom();this.createFastDom();this.createFooterDom();this.cutState();this.bindEvent()};a.prototype.initOptions=function(){this.container=this.options.container;e(this.container).empty();this.id=this.options.id;this.reportTimeType=this.options.reportTimeType;this.options.startTime=this.options.isDouble?moment().subtract(1,"month").valueOf():moment().valueOf();this.options.endTime=moment().subtract(1,"month").valueOf();this.reportStartYear=-3;this.reportEndYear=12;if(this.reportTimeType<7){this.options.endTime=moment(this.options.endTime).add(1,"month").valueOf()}else{if(this.reportTimeType<=8){this.options.startTime=moment().subtract(1,"year").valueOf();this.options.endTime=moment(this.options.startTime).add(1,"year").valueOf()}else{if(this.reportTimeType===9){this.options.startTime=this.options.isDouble?moment().subtract(10,"year").add(this.reportStartYear,"year").valueOf():moment().subtract(1,"year").add(this.reportStartYear,"year").valueOf();this.options.endTime=moment(this.options.startTime).add((this.reportEndYear+this.reportStartYear),"year").valueOf()}}}this.selectTime=[];this.selectDom=[];this.weekText=["日","一","二","三","四","五","六"];this.type={4:"hour",5:"day",6:"week",7:"month",8:"quarter",9:"year"};if(this.options.isDouble){this.date=[this.options.startTime,this.options.endTime]}else{this.date=[this.options.startTime]}if(this.options.isDouble){e(this.container).addClass("is-double")}if(this.options.isFast){e(this.container).addClass("is-fast")}e(this.container).attr("reportTimeType",this.reportTimeType)};a.prototype.resetTime=function(){var g=this.options.startTime;var f=this.options.endTime;this.options.startYear=moment(g).year();this.options.startMonth=moment(g).month();this.options.startDay=moment(g).date();this.options.startHour=moment(g).hour();this.options.startMinute=moment(g).minute();this.options.startSecond=moment(g).second();this.options.endYear=moment(f).year();this.options.endMonth=moment(f).month();this.options.endDay=moment(f).date();this.options.endHour=moment(f).hour();this.options.endMinute=moment(f).minute();this.options.endSecond=moment(f).second();this.options.startTime=moment({y:this.options.startYear,M:this.options.startMonth,d:this.options.startDay,h:this.options.startHour,m:this.options.startMinute,s:this.options.startSecond}).valueOf();
this.options.endTime=moment({y:this.options.endYear,M:this.options.endMonth,d:this.options.endDay,h:this.options.endHour,m:this.options.endMinute,s:this.options.endSecond}).valueOf()};a.prototype.setTime=function(f){return moment(f).valueOf()};a.prototype.addTime=function(h,g,f){f=this.options.reportTimeType===9&&!f?9:f;f=f||1;return moment(h).add(f,g).valueOf()};a.prototype.subtractTime=function(h,g,f){f=this.options.reportTimeType===9&&!f?9:f;f=f||1;return moment(h).subtract(f,g).valueOf()};a.prototype.createDom=function(){var g=this.options.reportTimeType!==4?"hide":"";this.container.append('<div class="'+c.dContent+'"><div class="'+c.dCFast+'"></div><div class="'+c.dCCalender+'"><div class="'+c.dCCHours+" "+g+'"></div><div class="'+c.dCCBody+'"></div></div></div><div class="'+c.dBottom+'"></div>');if(this.options.isDouble){this.container.find("."+c.dCCBody).append('<div class="'+c.dCCBLeft+'"></div><div class="'+c.dCCBRight+'"></div>');this.container.find("."+c.dCCHours).append('<div class="'+c.dCCHLeft+'" name-text="开始日期,开始时间"></div><div class="ali-iconfont ali-icon076arrowXY02 '+c.dCCHContent+'"></div><div class="'+c.dCCHRight+'" name-text="结束日期,结束时间"></div>');this.calendarDoms=[this.container.find("."+c.dCCBLeft),this.container.find("."+c.dCCBRight)];this.hourDoms=[this.container.find("."+c.dCCHLeft),this.container.find("."+c.dCCHRight)]}else{this.calendarDoms=[this.container.find("."+c.dCCBody)];this.container.find("."+c.dCCHours).attr("name-text","开始日期,开始时间");this.hourDoms=[this.container.find("."+c.dCCHours)]}for(var h=0;h<this.date.length;h++){var j=this.calendarDoms[h];var f=this.hourDoms[h];var k=this.date[h];this.createHeaderDom(j,k);this.createBodyDom(j,k);this.createDomBodyHours(f,k)}};a.prototype.createHeaderDom=function(h,g){var f=b.cBHeader;var i=h.find("."+f);if(!i.length){i=e('<div class="'+f+'"></div>');h.append(i)}switch(this.reportTimeType){case 4:this.createHeaderDomDay(i,g);break;case 5:this.createHeaderDomDay(i,g);break;case 6:this.createHeaderDomDay(i,g);break;case 7:this.createHeaderDomDay(i,g);break;case 8:this.createHeaderDomDay(i,g);break;case 9:this.createHeaderDomDay(i,g);break}};a.prototype.createHeaderDomDay=function(l,k){var i=this.options.isDouble?"is-double":"";var f=moment(k).format("YYYY 年 M 月 ");if(this.options.reportTimeType===7||this.options.reportTimeType===8){f=moment(k).format("YYYY 年")}else{if(this.options.reportTimeType===9){f=moment(k).format("YYYY 年")+" - "+moment(k).add(8,"year").format("YYYY 年")}}var j="<div class='"+b.cBHLeft+"'><i class='ali-iconfont ali-icon081arrowXZ05 "+b.cBHLPrevYear+"'></i><i class='ali-iconfont ali-icon075arrowXZ02 "+b.cBHLPrevMonth+"'></i></div>";var g="<div class='"+b.cBHCenter+"'><span class='"+b.cBHCText+" "+i+"'>"+f+"</span></div>";var h="<div class='"+b.cBHRight+"'><i class='ali-iconfont ali-icon076arrowXY02 "+b.cBHRNextMonth+"'></i><i class='ali-iconfont ali-icon082arrowXY05 "+b.cBHRNextYear+"'></i></div>";l.append(j).append(g).append(h)};a.prototype.setHeaderText=function(h,f){var g=moment(f).format("YYYY 年 M 月 ");if(this.options.reportTimeType===7||this.options.reportTimeType===8){g=moment(f).format("YYYY 年")}else{if(this.options.reportTimeType===9){g=moment(f).format("YYYY 年")+" - "+moment(f).add(8,"year").format("YYYY 年")}}h.find("."+b.cBHCText).text(g)};a.prototype.createBodyDom=function(i,h){var g=b.cBContent;var f=i.find("."+g).empty();if(!f.length){f=e('<div class="'+g+'"></div>');i.append(f)}switch(this.reportTimeType){case 4:this.createDomBodyDay(f,h);break;case 5:this.createDomBodyDay(f,h);break;case 6:this.createDomBodyDay(f,h);break;case 7:this.createDomBodyMonth(f,h);break;case 8:this.createDomBodyQuarter(f,h);break;case 9:this.createDomBodyYear(f,h);break}};a.prototype.createDomBodyDay=function(v,r){r=moment(r).startOf("day").valueOf();var l=e("<div class='"+b.cBCTitle+"'></div>");var B="";for(var G=0;G<this.weekText.length;G++){B+='<span class="'+b.cBCTText+'">'+this.weekText[G]+"</span>"}l.append(B);var A=e("<div class='"+b.cBCBody+"'></div>");var q=0;for(var G=0;G<6;G++){var w=moment(r).startOf("month").weekday();var u=moment(r).startOf("month").subtract(w,"day").valueOf();var D=this.options.reportTimeType===6?"title='"+moment(u).add(G,"week").week()+"周'":"";var p=e("<div class='"+b.cBCBRow+"' "+D+"></div>");var g=moment(r).month();var m="";var t=this.options.isDouble||this.reportTimeType===6?" start-date active":" current";t=this.selectTime[0]?t:"";var s=this.options.isDouble||this.reportTimeType===6?" end-date active":"";s=this.selectTime[1]?s:"";var n=this.options.isDouble||this.reportTimeType===6?" active":"";for(var E=0;E<7;E++){var H=moment(u).add(q,"day").date();var h=moment(u).add(q,"day").month();var J=moment(u).add(q,"day").valueOf();var I="available";var y="";if(this.options.disabledDate){y=moment(J).endOf("day").valueOf()>moment().endOf("day").valueOf()?" disabled":""}if(h>g){I="next-month"}else{if(h<g){I="prev-month"}}var z=moment(this.selectTime[0]).startOf("day").valueOf();var k=moment(this.selectTime[1]).startOf("day").valueOf();
var o=J===k&&(I==="available"||(this.reportTimeType===6&&!this.options.isDouble))?s:"";var x=J===z&&(I==="available"||(this.reportTimeType===6&&!this.options.isDouble))?t:"";var F=J>z&&J<k&&(I==="available"||(this.reportTimeType===6&&!this.options.isDouble))?n:"";var C=moment(J).format("YYYY-MM-DD");var f=C===moment().format("YYYY-MM-DD")&&h===g?" today":"";m+="<div class='"+b.cBCBRDate+" "+o+x+F+f+y+"' date='"+C+"'><span class='"+I+"'>"+H+"</span></div>";q++}p.append(m);A.append(p)}v.append(l).append(A)};a.prototype.createDomBodyMonth=function(s,p){p=moment(moment(p).format("YYYY-MM-01 00:00:00")).valueOf();var w=e("<div class='"+b.cBCBody+"'></div>");var o=0;for(var A=0;A<3;A++){var n=e("<div class='"+b.cBCBRow+"'></div>");var B=moment(p).startOf("year");var k="";var r=this.options.isDouble?" start-date active":" current";r=this.selectTime[0]?r:"";var q=this.options.isDouble?" end-date active":"";q=this.selectTime[1]?q:"";var l=this.options.isDouble?" active":"";for(var y=0;y<4;y++){var u="";var h=this.lowerConvertUpper(moment(B).add(o,"month").format("MM"))+"月";var D=moment(B).add(o,"month").valueOf();var C="available";var v=moment(moment(this.selectTime[0]).format("YYYY-MM-01 00:00:00")).valueOf();var g=moment(moment(this.selectTime[1]).format("YYYY-MM-01 00:00:00")).valueOf();var m=D===g&&C==="available"?q:"";var t=D===v&&C==="available"?r:"";var z=D>v&&D<g&&C==="available"?l:"";var x=moment(D).format("YYYY-MM");var f=x===moment().format("YYYY-MM")?" today":"";if(this.options.disabledDate){u=moment(D).endOf("month").valueOf()>moment().endOf("month").valueOf()?" disabled":""}k+="<div class='"+b.cBCBRDate+" "+m+t+z+f+u+"' date='"+x+"'><span class='"+C+"'>"+h+"</span></div>";o++}n.append(k);w.append(n)}s.append(w)};a.prototype.createDomBodyQuarter=function(s,p){p=moment(p).startOf("quarter").valueOf();var w=e("<div class='"+b.cBCBody+"'></div>");var o=0;for(var A=0;A<2;A++){var n=e("<div class='"+b.cBCBRow+"'></div>");var B=moment(p).startOf("year");var k="";var r=this.options.isDouble?" start-date active":" current";r=this.selectTime[0]?r:"";var q=this.options.isDouble?" end-date active":"";q=this.selectTime[1]?q:"";var l=this.options.isDouble?" active":"";for(var y=0;y<2;y++){var u="";var h="第"+this.lowerConvertUpper(moment(B).add(o,"quarter").quarter())+"季度";var D=moment(B).add(o,"quarter").valueOf();var C="available";var v=moment(this.selectTime[0]).startOf("quarter").valueOf();var g=moment(this.selectTime[1]).startOf("quarter").valueOf();var m=D===g&&C==="available"?q:"";var t=D===v&&C==="available"?r:"";var z=D>v&&D<g&&C==="available"?l:"";var x=moment(D).format("YYYY-MM");var f=x===moment().format("YYYY-MM")?" today":"";if(this.options.disabledDate){u=moment(D).endOf("month").valueOf()>moment().endOf("month").valueOf()?" disabled":""}k+="<div class='"+b.cBCBRDate+" "+m+t+z+f+u+"' date='"+x+"'><span class='"+C+"'>"+h+"</span></div>";o++}n.append(k);w.append(n)}s.append(w)};a.prototype.createDomBodyYear=function(s,p){p=moment(p).startOf("year").valueOf();var w=e("<div class='"+b.cBCBody+"'></div>");var o=0;for(var A=0;A<3;A++){var n=e("<div class='"+b.cBCBRow+"'></div>");var k="";var r=this.options.isDouble?" start-date active":" current";r=this.selectTime[0]?r:"";var q=this.options.isDouble?" end-date active":"";q=this.selectTime[1]?q:"";var l=this.options.isDouble?" active":"";for(var y=0;y<3;y++){var u="";var h=moment(p).add(o,"year").year()+"年";var C=moment(p).add(o,"year").valueOf();var B="available";var v=moment(this.selectTime[0]).startOf("year").valueOf();var g=moment(this.selectTime[1]).startOf("year").valueOf();var m=C===g&&B==="available"?q:"";var t=C===v&&B==="available"?r:"";var z=C>v&&C<g&&B==="available"?l:"";var x=moment(C).format("YYYY-MM");var f=x===moment().format("YYYY-MM")?" today":"";if(this.options.disabledDate){u=moment(C).endOf("month").valueOf()>moment().endOf("month").valueOf()?" disabled":""}k+="<div class='"+b.cBCBRDate+" "+m+t+z+f+u+"' date='"+x+"'><span class='"+B+"'>"+h+"</span></div>";o++}n.append(k);w.append(n)}s.append(w)};a.prototype.cutState=function(){var i=this.calendarDoms[0].find(("."+b.cBHRNextMonth+",."+b.cBHRNextYear));var g=this.calendarDoms[0].find("."+b.cBHRNextYear);var f=this.calendarDoms[1]&&this.calendarDoms[1].find("."+b.cBHRNextYear);var j=this.calendarDoms[1]&&this.calendarDoms[1].find("."+b.cBHRNextMonth);var l=this.calendarDoms[0].find("."+b.cBHRNextMonth);var k=this.calendarDoms[1]&&this.calendarDoms[1].find(("."+b.cBHLPrevMonth+",."+b.cBHLPrevYear));var h=this.calendarDoms[1]&&this.calendarDoms[1].find("."+b.cBHLPrevYear);i&&i.removeClass("is-disabled");k&&k.removeClass("is-disabled");if(this.options.isDouble){if(this.options.reportTimeType<9){if((this.options.endYear===this.options.startYear&&this.options.endMonth-this.options.startMonth===1)||(this.options.endYear-this.options.startYear===1&&this.options.startMonth===11&&this.options.endMonth===0)){i.addClass("is-disabled");k.addClass("is-disabled")}else{if((this.options.endYear===this.options.startYear&&this.options.endMonth-this.options.startMonth>1)||(this.options.endYear-this.options.startYear===1&&this.options.endMonth===this.options.startMonth)||(this.options.endYear-this.options.startYear===1&&this.options.endMonth<this.options.startMonth)){g.addClass("is-disabled");
h.addClass("is-disabled")}}}else{if(this.options.endYear-(this.options.startYear+this.reportEndYear)===this.reportStartYear){g.addClass("is-disabled");h.addClass("is-disabled")}}if(this.options.disabledDate){f.removeClass("is-disabled");j.removeClass("is-disabled");if(this.reportTimeType<7){if(moment().month()===this.options.endMonth&&moment().year()===this.options.endYear){f.addClass("is-disabled");j.addClass("is-disabled")}else{if((moment().year()===this.options.endYear&&moment().month()>this.options.endMonth)||(moment().year()-this.options.endYear===1&&moment().month()<this.options.endMonth)){f.addClass("is-disabled")}}}else{if(this.reportTimeType<9){if(this.options.endYear===moment().year()){f.addClass("is-disabled")}}else{if(this.reportTimeType===9){if(this.options.endYear+9===moment().year()+5){f.addClass("is-disabled")}}}}}}else{g.removeClass("is-disabled");l.removeClass("is-disabled");if(this.options.disabledDate){if(this.reportTimeType<7){if(this.options.startMonth===moment().month()&&this.options.startYear===moment().year()){g.addClass("is-disabled");l.addClass("is-disabled")}else{if((moment().year()===this.options.startYear&&moment().month()>this.options.startMonth)||(moment().year()-this.options.startYear===1&&moment().month()<this.options.startMonth)){g.addClass("is-disabled")}}}else{if(this.reportTimeType<9){if(this.options.startYear===moment().year()){g.addClass("is-disabled")}}else{if(this.reportTimeType===9){if(this.options.startYear+9===moment().year()+5){g.addClass("is-disabled")}}}}}}};a.prototype.createDomBodyHours=function(i,g){var h=i.attr("name-text").split(",");var f='<div class="'+b.cHLeft+'"><input placeholder="'+h[0]+'" class="hours-input '+b.cHLInput+'"></input></div><div class="'+b.cHRight+'"><input placeholder="'+h[1]+'" class="hours-input '+b.cHRInput+'"></input></div>';i.append(f)};a.prototype.createFastDom=function(){var j=e('<ul class="'+c.dCFUl+'"></ul>');var g="";var i=this.options.fastTime;for(var k in i){var h=i[k].startTime;var f=i[k].endTime;g+='<li class="'+c.dCFULi+'" start-time="'+h+'" end-time="'+f+'">'+k+"</li>"}j.append(g);e(this.container).find("."+c.dCFast).append(j)};a.prototype.createFooterDom=function(){if(this.options.reportTimeType===4){var f=e(this.container).find("."+c.dBottom);f.append('<div class="'+c.dBBtn+'"><span class="'+c.dBBCancel+'">清空</span><span class="'+c.dBBConfirm+' is-disabled">确认</span></div>')}};a.prototype.createHoursPullDown=function(t,m,l){var B=e(t).find("."+b.cHRPull);if(!B.length){var p=e('<div class="'+b.cHRPull+'"></div>');t.find("."+b.cHRight).append(p);var y=e('<div class="'+b.cHRPContent+'"></div>');var z=e('<div class="'+b.cHRPFooter+'"></div>');var h=e('<div class="'+b.cHRPCHours+'"></div>');var C=e('<ul class="'+b.cHRPHUl+'"></ul>');var u=e('<div class="'+b.cHRPCMinute+'"></div>');var o=e('<ul class="'+b.cHRPMUl+'"></ul>');var f=e('<div class="'+b.cHRPCSecond+'"></div>');var r=e('<ul class="'+b.cHRPSUl+'"></ul>');var k=moment(m).format("HH");var s=moment(m).format("mm");var E=moment(m).format("ss");var D="";var x=0;for(var w=0;w<24;w++){var n=w;n=n<=9?"0"+n:n;var j=String(k)===String(n)?"active":"";if(j){x=n*28}D+='<li class="'+j+'" text="'+n+'">'+n+"</li>"}var q="";var A=0;for(var w=0;w<60;w++){var n=w;n=n<=9?"0"+n:n;var j=String(s)===String(n)?"active":"";if(j){A=n*28}q+='<li class="'+j+'" text="'+n+'">'+n+"</li>"}var g="";var v=0;for(var w=0;w<60;w++){var n=w;n=n<=9?"0"+n:n;var j=String(E)===String(n)?"active":"";if(j){v=n*28}g+='<li class="'+j+'" text="'+n+'">'+n+"</li>"}z.append('<span class="'+b.cHRPFCancel+'">取消</span><span class="'+b.cHRPFConfirm+'">确定</span>');C.append(D);o.append(q);r.append(g);h.append(C);u.append(o);f.append(r);y.append(h).append(u).append(f);p.append(y).append(z);C.scrollTop(x);o.scrollTop(A);r.scrollTop(v);this.bindPullDown(p,m,l)}else{if(B.hasClass("hide")){B.removeClass("hide");this.changePullDonwTime(t.find("."+b.cHRPContent+" ul"),m)}}};a.prototype.calendarRender=function(g,f){this.resetTime();this.cutState();g&&this.setHeaderText(g,f);g&&this.createBodyDom(g,f);this.bindEventBodyDay()};a.prototype.changePullDonwTime=function(h,i){var f=moment(i).format("HH");var j=moment(i).format("mm");var g=moment(i).format("ss");e.each(h,function(k,l){if(e(l).hasClass(b.cHRPHUl)){e(this).children('[text="'+f+'"]').addClass("active").siblings().removeClass("active");e(this).scrollTop(f*28)}else{if(e(l).hasClass(b.cHRPMUl)){e(this).children('[text="'+j+'"]').addClass("active").siblings().removeClass("active");e(this).scrollTop(j*28)}else{if(e(l).hasClass(b.cHRPSUl)){e(this).children('[text="'+g+'"]').addClass("active").siblings().removeClass("active");e(this).scrollTop(g*28)}}}})};a.prototype.bindPullDown=function(g,h,f){var k=this;var j=[];g.find("ul > li").on("click",function(){if(e(this).hasClass("active")){return}var m=e(this).text()*e(this).outerHeight();e(this).addClass("active").siblings().removeClass("active");e(this).parent().scrollTop(m);var l=g.find("li.active");j=[];e.each(l,function(o,p){j.push(e(p).text())
});var n=j.join(":");g.prev().val(n)});var i=null;g.find("ul").scroll(function(){var n=this;var q=e(this).scrollTop();var p=q/28;var o=q%28;var m=o===0?p:o>14?(q+o)/28:(q-o)/28;m=Number(m.toFixed(0));m=m<=9?"0"+m:m;e(this).children('[text="'+m+'"]').addClass("active").siblings().removeClass("active");i&&clearTimeout(i);i=setTimeout(function(){var s=m*e(n).children().height();s=s>(e(n).children("li:last").text()*28)?(e(n).children("li:last").text()*28):s;e(n).scrollTop(s);i=null},300);var l=g.find("li.active");j=[];e.each(l,function(s,t){j.push(e(t).text())});var p=j.join(":");g.prev().val(p);var r=moment(moment(h).format("YYYY-MM-DD ")+p).valueOf();if(f===0){if(r>k.selectTime[1]){k.selectTime[1]=r;k.hourDoms[1]&&k.hourDoms[1].find("."+b.cHRInput).val(moment(k.selectTime[1]).format("HH:mm:ss"))}}else{if(f===1){if(r<k.selectTime[0]){k.selectTime[0]=r;k.hourDoms[0].find("."+b.cHRInput).val(moment(k.selectTime[0]).format("HH:mm:ss"))}}}});g.find("."+b.cHRPFCancel).on("click",function(){g.addClass("hide");var l=moment(k.selectTime[f]).format("HH:mm:ss");g.prev().val(l)});g.find("."+b.cHRPFConfirm).on("click",function(){k.selectTime[f]=moment(moment(h).format("YYYY-MM-DD ")+j.join(":")).valueOf();g.addClass("hide")});e(document).on("click",function(n){var m=n.target;if(!e(m).hasClass(b.cHRInput)&&!e(m).parents("."+b.cHRPull).length){g.addClass("hide");var l=moment(k.selectTime[f]).format("HH:mm:ss");g.prev().val(l)}})};a.prototype.bindEvent=function(){var g=this;var f=g.options.reportTimeType;switch(f){case 4:this.bindEventCut();this.bindEventFast();this.bindEventFooter();this.bindEventBodyDay();break;case 5:this.bindEventCut();this.bindEventFast();this.bindEventBodyDay();break;case 6:this.bindEventCut();this.bindEventFast();this.bindEventBodyDay();break;case 7:this.bindEventCut();this.bindEventFast();this.bindEventBodyDay();break;case 8:this.bindEventCut();this.bindEventFast();this.bindEventBodyDay();break;case 9:this.bindEventCut();this.bindEventFast();this.bindEventBodyDay();break}};a.prototype.bindEventFooter=function(){var f=this;f.container.find("."+c.dBBConfirm).on("click",function(){if(!e(this).hasClass("is-disabled")){var h=moment(f.selectTime[0]).format(f.options.format);var g=moment(f.selectTime[1]).format(f.options.format);f.options.startDom.val(h);f.options.isDouble?f.options.endDom.val(g):f.options.endDom.val(h);typeof f.options.yes==="function"&&f.options.yes(h,g);f.container.addClass("hide")}});f.container.find("."+c.dBBCancel).on("click",function(){f.options.startDom.val("");f.options.endDom.val("");f.container.remove()})};a.prototype.bindEventFast=function(){var f=this;e(this.container).find("."+c.dCFULi).on("click",function(){var r=e(this).attr("start-time");var v=moment(r).year();var y=moment(r).month();var l=e(this).attr("end-time");var o=moment(l).year();var w=moment(l).month();var p=moment(r).format("YYYY-MM-DD");var u=moment(r).format("HH:mm:ss");var j=moment(l).format("YYYY-MM-DD");var s=moment(l).format("HH:mm:ss");var z="YYYY-MM-DD HH:mm:ss";var x="YYYY-MM-DD HH:mm:ss";var m="YYYY-MM-DD HH:mm:ss";var B="YYYY-MM-DD HH:mm:ss";var h=moment(r).valueOf();var i=moment(l).valueOf();f.selectTime[0]=h;f.selectTime[1]=i||(f.reportTimeType===6&&moment(f.selectTime[0]).endOf("week").valueOf());switch(f.reportTimeType){case 4:if(v===o&&w===y){var g=f.options.isDouble?f.subtractTime(f.selectTime[0],"month"):f.selectTime[0];f.options.startTime=g;f.options.endTime=f.selectTime[0];f.calendarRender(f.calendarDoms[0],g);f.calendarRender(f.calendarDoms[1],f.selectTime[0])}else{f.options.startTime=h;f.options.endTime=i;f.calendarRender(f.calendarDoms[0],f.selectTime[0]);f.calendarRender(f.calendarDoms[1],f.selectTime[1])}f.hourDoms[0].find("."+b.cHLInput).val(p);f.hourDoms[0].find("."+b.cHRInput).val(u);f.hourDoms[1]&&f.hourDoms[1].find("."+b.cHLInput).val(j);f.hourDoms[1]&&f.hourDoms[1].find("."+b.cHRInput).val(s);e(f.container).find("."+c.dBBConfirm).removeClass("is-disabled");break;case 5:if(v===o&&w===y){var g=f.options.isDouble?f.subtractTime(f.selectTime[0],"month"):f.selectTime[0];f.options.startTime=g;f.options.endTime=f.selectTime[0];f.calendarRender(f.calendarDoms[0],g);f.calendarRender(f.calendarDoms[1],f.selectTime[0])}else{f.options.startTime=h;f.options.endTime=i;f.calendarRender(f.calendarDoms[0],f.selectTime[0]);f.calendarRender(f.calendarDoms[1],f.selectTime[1])}z="YYYY-MM-DD";x="YYYY-MM-DD";break;case 6:if(v===o&&w===y){var g=f.options.isDouble?f.subtractTime(f.selectTime[0],"month"):f.selectTime[0];f.options.startTime=g;f.options.endTime=f.selectTime[0];f.calendarRender(f.calendarDoms[0],g);f.calendarRender(f.calendarDoms[1],f.selectTime[0])}else{f.options.startTime=h;f.options.endTime=i;f.calendarRender(f.calendarDoms[0],f.selectTime[0]);f.calendarRender(f.calendarDoms[1],f.selectTime[1])}z="YYYY 第";x="YYYY 第";break;case 7:if(o===v){var g=f.subtractTime(f.selectTime[0],"year");f.options.startTime=g;f.options.endTime=f.selectTime[0];f.calendarRender(f.calendarDoms[0],g);f.calendarRender(f.calendarDoms[1],f.selectTime[0])
}else{f.options.startTime=h;f.options.endTime=i;f.calendarRender(f.calendarDoms[0],f.selectTime[0]);f.calendarRender(f.calendarDoms[1],f.selectTime[1])}z="YYYY-MM ";x="YYYY-MM ";break;case 8:if(o===v){var g=f.subtractTime(f.selectTime[0],"year");f.options.startTime=g;f.options.endTime=f.selectTime[0];f.calendarRender(f.calendarDoms[0],g);f.calendarRender(f.calendarDoms[1],f.selectTime[0])}else{f.options.startTime=h;f.options.endTime=i;f.calendarRender(f.calendarDoms[0],f.selectTime[0]);f.calendarRender(f.calendarDoms[1],f.selectTime[1])}z="YYYY 第 ";x="YYYY 第 ";break;case 9:if(o-v<9){if(v<f.options.startYear){f.options.startTime=f.date[0];f.options.endTime=f.date[1];var n=Math.ceil((f.options.startYear-v)/9)*9;f.options.startTime=moment(f.options.startTime).subtract(n,"year").valueOf();f.options.endTime=moment(f.options.startTime).add(n,"year").valueOf();f.calendarRender(f.calendarDoms[0],f.options.startTime);f.calendarRender(f.calendarDoms[1],f.options.endTime)}else{f.options.startTime=f.date[0];var g=f.addTime(f.options.startTime,"year",9);f.options.endTime=g;f.calendarRender(f.calendarDoms[0],f.options.startTime);f.calendarRender(f.calendarDoms[1],g)}}else{var n=Math.ceil((f.options.startYear-v)/9)*9;f.options.startTime=moment(f.options.startTime).subtract(n,"year").valueOf();f.calendarRender(f.calendarDoms[0],f.options.startTime);f.calendarRender(f.calendarDoms[1],f.options.endTime)}z="YYYY";x="YYYY";break}var A=f.options.isDouble?moment(f.selectTime[0]).format(z):moment(f.selectTime[0]).startOf(f.type[f.reportTimeType]).format(z);A=f.reportTimeType===6?A+moment(f.selectTime[0]).week()+" 周":A;A=f.reportTimeType===8?A+moment(f.selectTime[0]).quarter()+" 季度":A;var k=f.options.isDouble?moment(f.selectTime[1]).format(x):moment(f.selectTime[0]).endOf(f.type[f.reportTimeType]).format(x);k=f.reportTimeType===6?k+moment((f.selectTime[1]||f.selectTime[0])).week()+" 周":k;k=f.reportTimeType===8?k+moment((f.selectTime[1]||f.selectTime[0])).quarter()+" 季度":k;var q=f.options.isDouble?moment(f.selectTime[0]).startOf(f.type[f.reportTimeType]).format(m):moment(f.selectTime[0]).startOf(f.type[f.reportTimeType]).format(m);var t=f.options.isDouble?moment(f.selectTime[1]).endOf(f.type[f.reportTimeType]).format(B):moment(f.selectTime[0]).endOf(f.type[f.reportTimeType]).format(B);f.options.startDom.val(A);f.options.endDom.val(k);f.options.startDom.attr("sdtDate",q);f.options.endDom.attr("edtDate",t);typeof f.options.yes==="function"&&f.options.yes(q,t);e(f.container).addClass("hide")})};a.prototype.bindEventBodyDay=function(){var k=this;var l="."+b.cBCBRDate;var g="."+b.cBCBRDate+".active";var i="."+b.cBCBRDate+".start-date";var j="."+b.cBCBRDate+".end-date";var n=e(this.container).find(l);var m="YYYY-MM-DD HH:mm:ss";var f="YYYY-MM-DD HH:mm:ss";var o="YYYY-MM-DD HH:mm:ss";var h="YYYY-MM-DD HH:mm:ss";if(k.options.isDouble){n.off("click").on("click",function(){if(e(this).hasClass("disabled")){return}if(!k.selectTime[0]){var s=e(this).parent("[title]").children(":first").attr("date")||e(this).attr("date");var r=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).valueOf();var x=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).format("YYYY-MM-DD");var v=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).format("00:00:00");k.selectTime[0]=r;var w="[date='"+s+"']";k.selectDom[0]=e(k.container).find(w).children(":not(.prev-month, .next-month)").parent();k.selectDom[0].addClass("start-date");k.container.find("."+b.cHLInput).val(x);k.container.find("."+b.cHRInput).val(v);k.container.find(".hours-input").attr("disabled","disabled");k.container.find("."+c.dBBConfirm).addClass("is-disabled")}else{if(!k.selectTime[1]){var s=e(this).parent("[title]").children(":last").attr("date")||e(this).attr("date");var r=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).valueOf();var x=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).format("YYYY-MM-DD");var v=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).format("00:00:00");k.selectTime[1]=moment(r).valueOf();var w="[date='"+s+"']";k.selectDom[1]=e(k.container).find(w).children(":not(.prev-month, .next-month)").parent();if(k.selectTime[0]<=r){k.selectDom[1].addClass("end-date");switch(k.reportTimeType){case 4:k.hourDoms[1].find("."+b.cHLInput).val(x);k.hourDoms[1].find("."+b.cHRInput).val(v);if(k.selectTime[0]===r){k.hourDoms[1].find("."+b.cHRInput).val("23:00:00")}break;case 5:m="YYYY-MM-DD";f="YYYY-MM-DD";break;case 6:k.selectDom[1].prevAll().addClass("active");m="YYYY 第";f="YYYY 第";break;case 7:k.selectTime[1]=moment(k.selectTime[1]).endOf("month").valueOf();m="YYYY-MM ";f="YYYY-MM ";break;case 8:k.selectTime[1]=moment(k.selectTime[1]).endOf("quarter").valueOf();m="YYYY 第 ";f="YYYY 第 ";break;case 9:k.selectTime[1]=moment(k.selectTime[1]).endOf("year").valueOf();m="YYYY";f="YYYY";break}}else{switch(k.reportTimeType){case 4:k.hourDoms[0].find("."+b.cHLInput).val(x);k.hourDoms[0].find("."+b.cHRInput).val(v);
break;case 5:m="YYYY-MM-DD";f="YYYY-MM-DD";break;case 6:k.selectDom[1].siblings().removeClass("start-date");k.selectDom[0].removeClass("end-date");k.selectDom[1]=k.selectDom[1].parent().children(":first").addClass("start-date");k.selectDom[0]=k.selectDom[0].parent().children(":last").addClass("end-date");k.selectTime[0]=k.selectDom[0].parent().children(":last").attr("date");k.selectTime[1]=k.selectDom[1].parent().children(":first").attr("date");k.selectDom[1].nextAll().addClass("active");k.selectDom[0].prevAll().addClass("active");m="YYYY 第";f="YYYY 第";break;case 7:k.selectTime[0]=moment(k.selectTime[0]).endOf("month").valueOf();m="YYYY-MM ";f="YYYY-MM ";case 8:k.selectTime[0]=moment(k.selectTime[0]).endOf("quarter").valueOf();m="YYYY 第 ";f="YYYY 第 ";break;case 9:k.selectTime[0]=moment(k.selectTime[0]).endOf("year").valueOf();m="YYYY";f="YYYY";break}k.selectDom.reverse();k.selectTime.reverse()}k.container.find(".hours-input").removeAttr("disabled","disabled");k.container.find("."+c.dBBConfirm).removeClass("is-disabled");var q=k.options.isDouble?moment(k.selectTime[0]).format(m):moment(k.selectTime[0]).startOf(k.type[k.reportTimeType]).format(m);q=k.reportTimeType===6?q+moment(k.selectTime[0]).week()+" 周":q;q=k.reportTimeType===8?q+moment(k.selectTime[0]).quarter()+" 季度":q;var u=k.options.isDouble?moment(k.selectTime[1]).format(f):moment(k.selectTime[0]).endOf(k.type[k.reportTimeType]).format(f);u=k.reportTimeType===6?u+moment((k.selectTime[1]||k.selectTime[0])).week()+" 周":u;u=k.reportTimeType===8?u+moment((k.selectTime[1]||k.selectTime[0])).quarter()+" 季度":u;var t=k.options.isDouble?moment(k.selectTime[0]).startOf(k.type[k.reportTimeType]).format(o):moment(k.selectTime[0]).startOf(k.type[k.reportTimeType]).format(o);var p=k.options.isDouble?moment(k.selectTime[1]).endOf(k.type[k.reportTimeType]).format(h):moment(k.selectTime[0]).endOf(k.type[k.reportTimeType]).format(h);k.options.startDom.val(q);k.options.endDom.val(u);k.options.startDom.attr("sdtDate",t);k.options.endDom.attr("edtDate",p);typeof k.options.yes==="function"&&k.options.yes(t,p);k.options.reportTimeType!==4&&e(k.container).addClass("hide")}else{var s=e(this).parent("[title]").children(":first").attr("date")||e(this).attr("date");var r=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).valueOf();var x=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).format("YYYY-MM-DD");var v=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).format("00:00:00");k.selectTime=[];k.selectDom=[];e(k.container).find(l).removeClass("start-date").removeClass("end-date").removeClass("active");k.selectTime[0]=r;var w="[date='"+s+"']";k.selectDom[0]=e(k.container).find(w).children(":not(.prev-month, .next-month)").parent();k.selectDom[0].addClass("start-date");k.container.find("."+b.cHLInput).val(x);k.container.find("."+b.cHRInput).val(v);k.container.find(".hours-input").attr("disabled","disabled");k.container.find("."+c.dBBConfirm).addClass("is-disabled")}}});n.off("mouseenter").on("mouseenter",function(){if(k.selectTime[1]||e(this).hasClass("disabled")){return}var q=k.selectTime[0];var p=e(this).attr("date");var r=moment({y:moment(p).year(),M:moment(p).month(),d:moment(p).date()}).valueOf();if(k.selectTime[0]){e(k.container).find(g).removeClass("active");e(k.container).find(i+","+j).removeClass("start-date").removeClass("end-date");if(k.selectTime[0]<r){e(k.container).find(j).removeClass("end-date");if(!k.selectDom[0].hasClass("start-date")){k.selectDom[0].addClass("start-date").removeClass("end-date")}!e(this).children(".next-month, .prev-month").length&&k.options.reportTimeType!==6&&e(this).addClass("end-date")}else{if(!k.selectDom[0].hasClass("end-date")){k.selectDom[0].addClass("end-date").removeClass("start-date")}!e(this).children(".next-month, .prev-month").length&&k.options.reportTimeType!==6&&e(this).addClass("start-date")}e.each(n,function(t,u){var s=e(u).attr("date");var v=moment({y:moment(s).year(),M:moment(s).month(),d:moment(s).date()}).valueOf();if(q<r&&v<=r&&v>q&&!e(this).children("span.next-month, span.prev-month").length){e(u).addClass("active");if(v===r){if(k.options.reportTimeType!==6){e(u).addClass("end-date")}else{!e(u).parent().children(":last").children("span.next-month, span.prev-month").length&&e(u).parent().children(":last").addClass("end-date")}}}else{if(q>=r&&v>=r&&v<q&&!e(this).children("span.next-month, span.prev-month").length){e(u).addClass("active");if(v===r){if(k.options.reportTimeType!==6){e(u).addClass("start-date")}else{e(u).parent().children(":first").addClass("start-date")}}}}})}})}else{n.off("click").on("click",function(){if(e(this).hasClass("disabled")){return}var r=e(this).attr("date");var s=moment({y:moment(r).year(),M:moment(r).month(),d:moment(r).date()}).valueOf();var q=moment({y:moment(r).year(),M:moment(r).month(),d:moment(r).date()}).format("YYYY-MM-DD");var w=moment({y:moment(r).year(),M:moment(r).month(),d:moment(r).date()}).format("HH:mm:ss");if(k.options.reportTimeType===6){n.removeClass("start-date").removeClass("end-date").removeClass("active");
e(this).parent().children(":first").addClass("start-date");e(this).parent().children(":first").nextAll().addClass("active");e(this).parent().children(":last").addClass("end-date");k.selectTime[0]=e(this).parent().children(":first").attr("date");k.selectTime[1]=e(this).parent().children(":last").attr("date")}else{n.removeClass("current");e(this).addClass("current");k.selectTime[0]=s;k.selectTime[1]=s;if(k.options.reportTimeType===7){k.selectTime[1]=moment(k.selectTime[1]).endOf("month").valueOf()}else{if(k.options.reportTimeType===8){k.selectTime[1]=moment(k.selectTime[1]).endOf("quarter").valueOf()}}}switch(k.reportTimeType){case 4:k.container.find("."+c.dBBConfirm).removeClass("is-disabled");k.container.find("."+b.cHLInput).val(q);k.container.find("."+b.cHRInput).val(w);break;case 5:m="YYYY-MM-DD";f="YYYY-MM-DD";break;case 6:m="YYYY 第";f="YYYY 第";break;case 7:m="YYYY-MM ";f="YYYY-MM ";break;case 8:m="YYYY 第 ";f="YYYY 第 ";break;case 9:m="YYYY";f="YYYY";break}var v=k.options.isDouble?moment(k.selectTime[0]).format(m):moment(k.selectTime[0]).startOf(k.type[k.reportTimeType]).format(m);v=k.reportTimeType===6?v+moment(k.selectTime[0]).week()+" 周":v;v=k.reportTimeType===8?v+moment(k.selectTime[0]).quarter()+" 季度":v;var t=k.options.isDouble?moment(k.selectTime[1]).format(f):moment(k.selectTime[0]).endOf(k.type[k.reportTimeType]).format(f);t=k.reportTimeType===6?t+moment((k.selectTime[1]||k.selectTime[0])).week()+" 周":t;t=k.reportTimeType===8?t+moment((k.selectTime[1]||k.selectTime[0])).quarter()+" 季度":t;var u=k.options.isDouble?moment(k.selectTime[0]).startOf(k.type[k.reportTimeType]).format(o):moment(k.selectTime[0]).startOf(k.type[k.reportTimeType]).format(o);var p=k.options.isDouble?moment(k.selectTime[1]).endOf(k.type[k.reportTimeType]).format(h):moment(k.selectTime[0]).endOf(k.type[k.reportTimeType]).format(h);k.options.startDom.val(v);k.options.endDom.val(t);k.options.startDom.attr("sdtDate",u);k.options.endDom.attr("edtDate",p);typeof k.options.yes==="function"&&k.options.yes(u,p);k.options.reportTimeType!==4&&e(k.container).addClass("hide")})}};a.prototype.bindEventCut=function(){var f=this;if(this.options.isDouble){this.calendarDoms[0].find("."+b.cBHLPrevYear).on("click",function(){f.options.startTime=f.subtractTime(f.options.startTime,"year");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.calendarDoms[0].find("."+b.cBHLPrevMonth).on("click",function(){f.options.startTime=f.subtractTime(f.options.startTime,"month");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.calendarDoms[0].find("."+b.cBHRNextYear).on("click",function(){if(e(this).hasClass("is-disabled")){return}f.options.startTime=f.addTime(f.options.startTime,"year");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.calendarDoms[0].find("."+b.cBHRNextMonth).on("click",function(){if(e(this).hasClass("is-disabled")){return}f.options.startTime=f.addTime(f.options.startTime,"month");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.hourDoms[0].find("."+b.cHLInput).bind("input propertychange",function(){var g=e(this).val();var h=moment(g).valueOf();var i=moment(g).isValid();if(g.length===10&&i){f.options.startTime=f.selectTime[0]=f.setTime(h);f.calendarRender(f.calendarDoms[0],f.selectTime[0]);if((moment(f.selectTime[1]).month()-moment(f.selectTime[0]).month()<=1&&moment(f.selectTime[0]).year()===moment(f.selectTime[1]).year())||moment(f.selectTime[0]).year()>moment(f.selectTime[1]).year()){if(f.selectTime[0]>f.selectTime[1]){f.selectTime[1]=f.addTime(h,"day")}f.options.endTime=f.addTime(h,"month");f.calendarRender(f.calendarDoms[0],f.selectTime[0]);f.calendarRender(f.calendarDoms[1],f.options.endTime);f.hourDoms[1].find("."+b.cHLInput).val(moment(f.selectTime[1]).format("YYYY-MM-DD"))}}});this.hourDoms[0].find("."+b.cHRInput).on("click",function(){var k=e(this).val().trim();var i=f.hourDoms[0].find("."+b.cHLInput).val().trim();var g=f.hourDoms[1].find("."+b.cHLInput).val().trim();var l=f.hourDoms[1].find("."+b.cHRInput).val().trim();var j=moment(f.options.startTime).format("YYYY-MM-DD");var h=moment(f.options.startTime).format("HH:mm:ss");if(!k&&!i&&!g&&!l&&!f.selectTime[0]&&!f.selectTime[1]){f.selectTime[0]=f.options.startTime;f.selectTime[1]=f.options.startTime;e(this).val(h);f.hourDoms[1].find("."+b.cHRInput).val(h);f.hourDoms[0].find("."+b.cHLInput).val(j);f.hourDoms[1].find("."+b.cHLInput).val(j);f.calendarDoms[0].find('[date="'+j+'"]').addClass("active end-date start-date")}f.createHoursPullDown(f.hourDoms[0],f.selectTime[0],0)});this.hourDoms[0].find("."+b.cHRInput).bind("input propertychange",function(){var g=moment(moment(f.selectTime[0]).format("YYYY-MM-DD ")+e(this).val()).valueOf();if(g&&e(this).val().length===8){f.selectTime[0]=g;if(f.selectTime[0]>f.selectTime[1]){f.selectTime[1]=f.selectTime[0];f.hourDoms[1].find("."+b.cHRInput).val(moment(f.selectTime[1]).format("HH:mm:ss"))}var h=f.hourDoms[0].find("."+b.cHRPull+" ul");if(h.length){f.changePullDonwTime(h,f.selectTime[0])}}});this.calendarDoms[1].find("."+b.cBHLPrevYear).on("click",function(){if(e(this).hasClass("is-disabled")){return
}f.options.endTime=f.subtractTime(f.options.endTime,"year");f.calendarRender(f.calendarDoms[1],f.options.endTime)});this.calendarDoms[1].find("."+b.cBHLPrevMonth).on("click",function(){if(e(this).hasClass("is-disabled")){return}f.options.endTime=f.subtractTime(f.options.endTime,"month");f.calendarRender(f.calendarDoms[1],f.options.endTime)});this.calendarDoms[1].find("."+b.cBHRNextYear).on("click",function(){if(e(this).hasClass("is-disabled")&&f.options.disabledDate){return}f.options.endTime=f.addTime(f.options.endTime,"year");f.calendarRender(f.calendarDoms[1],f.options.endTime)});this.calendarDoms[1].find("."+b.cBHRNextMonth).on("click",function(){if(e(this).hasClass("is-disabled")&&f.options.disabledDate){return}f.options.endTime=f.addTime(f.options.endTime,"month");f.calendarRender(f.calendarDoms[1],f.options.endTime)});this.hourDoms[1].find("."+b.cHLInput).bind("input propertychange",function(){var g=e(this).val();var h=moment(g).valueOf();var i=moment(g).isValid();if(g.length===10&&i){f.options.endTime=f.selectTime[1]=f.setTime(h);f.calendarRender(f.calendarDoms[1],f.selectTime[1]);if((moment(f.selectTime[1]).month()-moment(f.selectTime[0]).month()<=1&&moment(f.selectTime[0]).year()===moment(f.selectTime[1]).year())||moment(f.selectTime[0]).year()>moment(f.selectTime[1]).year()){if(f.selectTime[0]>f.selectTime[1]){f.selectTime[0]=f.subtractTime(h,"day")}f.options.startTime=f.subtractTime(h,"month");f.calendarRender(f.calendarDoms[0],f.options.startTime);f.calendarRender(f.calendarDoms[1],f.selectTime[1]);f.hourDoms[0].find("."+b.cHLInput).val(moment(f.selectTime[0]).format("YYYY-MM-DD"))}}});this.hourDoms[1].find("."+b.cHRInput).on("click",function(){if(f.selectTime[1]){f.createHoursPullDown(f.hourDoms[1],f.selectTime[1],1)}});this.hourDoms[1].find("."+b.cHRInput).bind("input propertychange",function(){var h=moment(moment(f.selectTime[1]).format("YYYY-MM-DD ")+e(this).val()).valueOf();if(h&&e(this).val().length===8){f.selectTime[1]=h;if(f.selectTime[0]>f.selectTime[1]){f.selectTime[0]=f.selectTime[1];f.hourDoms[0].find("."+b.cHRInput).val(moment(f.selectTime[0]).format("HH:mm:ss"))}var g=f.hourDoms[1].find("."+b.cHRPull+" ul");if(g.length){f.changePullDonwTime(g,f.selectTime[1])}}})}else{this.hourDoms[0].find("."+b.cHRInput).on("click",function(){var j=e(this).val().trim();var h=f.hourDoms[0].find("."+b.cHLInput).val().trim();var i=moment(f.options.startTime).format("YYYY-MM-DD");var g=moment(f.options.startTime).format("HH:mm:ss");if(!j&&!h&&!f.selectTime[0]){f.selectTime[0]=f.options.startTime;e(this).val(g);f.hourDoms[0].find("."+b.cHLInput).val(i);f.calendarDoms[0].find('[date="'+i+'"]').addClass("current")}f.createHoursPullDown(f.hourDoms[0],f.selectTime[0],0)});this.hourDoms[0].find("."+b.cHLInput).bind("input propertychange",function(){var g=e(this).val();var h=moment(g).valueOf();var i=moment(g).isValid();if(g.length===10&&i){f.options.startTime=f.selectTime[0]=f.setTime(h);f.calendarRender(f.calendarDoms[0],f.selectTime[0])}});this.hourDoms[0].find("."+b.cHRInput).bind("input propertychange",function(){var g=moment(moment(f.selectTime[0]).format("YYYY-MM-DD ")+e(this).val()).valueOf();if(g&&e(this).val().length===8){f.selectTime[0]=g}});this.calendarDoms[0].find("."+b.cBHLPrevYear).on("click",function(){f.options.startTime=f.subtractTime(f.options.startTime,"year");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.calendarDoms[0].find("."+b.cBHLPrevMonth).on("click",function(){f.options.startTime=f.subtractTime(f.options.startTime,"month");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.calendarDoms[0].find("."+b.cBHRNextYear).on("click",function(){if(e(this).hasClass("is-disabled")){return}f.options.startTime=f.addTime(f.options.startTime,"year");f.calendarRender(f.calendarDoms[0],f.options.startTime)});this.calendarDoms[0].find("."+b.cBHRNextMonth).on("click",function(){if(e(this).hasClass("is-disabled")){return}f.options.startTime=f.addTime(f.options.startTime,"month");f.calendarRender(f.calendarDoms[0],f.options.startTime)})}};a.prototype.lowerConvertUpper=function(f){f=Number(f);switch(f){case 1:return"一";case 2:return"二";case 3:return"三";case 4:return"四";case 5:return"五";case 6:return"六";case 7:return"七";case 8:return"八";case 9:return"九";case 10:return"十";case 11:return"十一";case 12:return"十二"}};a.prototype.isBetween=function(i,g,f,h){h=h||"year";return moment(i).isBetween(g,f,h,"[]")};a.prototype.render=function(){var o=this;var i=o.selectTime[0];var q=moment(i).year();var s=moment(i).month();var m=o.selectTime[1];var t=moment(m).year();var n=moment(m).month();var p=moment(i).format("YYYY-MM-DD");var g=moment(i).format("HH:mm:ss");var h=moment(m).format("YYYY-MM-DD");var r=moment(m).format("HH:mm:ss");var f=moment(i).valueOf();var j=moment(m).valueOf();switch(o.reportTimeType){case 4:if(q===t&&n===s){var l=o.options.isDouble?o.subtractTime(o.selectTime[0],"month"):o.selectTime[0];o.options.startTime=l;o.options.endTime=o.selectTime[0];o.calendarRender(o.calendarDoms[0],l);
o.calendarRender(o.calendarDoms[1],o.selectTime[0])}else{o.options.startTime=f;o.options.endTime=j;o.calendarRender(o.calendarDoms[0],o.selectTime[0]);o.calendarRender(o.calendarDoms[1],o.selectTime[1])}o.hourDoms[0].find("."+b.cHLInput).val(p);o.hourDoms[0].find("."+b.cHRInput).val(g);o.hourDoms[1]&&o.hourDoms[1].find("."+b.cHLInput).val(h);o.hourDoms[1]&&o.hourDoms[1].find("."+b.cHRInput).val(r);e(o.container).find("."+c.dBBConfirm).removeClass("is-disabled");break;case 5:if(q===t&&n===s){var l=o.options.isDouble?o.subtractTime(o.selectTime[0],"month"):o.selectTime[0];o.options.startTime=l;o.options.endTime=o.selectTime[0];o.calendarRender(o.calendarDoms[0],l);o.calendarRender(o.calendarDoms[1],o.selectTime[0])}else{o.options.startTime=f;o.options.endTime=j;o.calendarRender(o.calendarDoms[0],o.selectTime[0]);o.calendarRender(o.calendarDoms[1],o.selectTime[1])}break;case 6:if(q===t&&n===s){var l=o.options.isDouble?o.subtractTime(o.selectTime[0],"month"):o.selectTime[0];o.options.startTime=l;o.options.endTime=o.selectTime[0];o.calendarRender(o.calendarDoms[0],l);o.calendarRender(o.calendarDoms[1],o.selectTime[0])}else{o.options.startTime=f;o.options.endTime=j;o.calendarRender(o.calendarDoms[0],o.selectTime[0]);o.calendarRender(o.calendarDoms[1],o.selectTime[1])}break;case 7:if(t===q){var l=o.options.isDouble?o.subtractTime(o.selectTime[0],"year"):o.selectTime[0];o.options.startTime=l;o.options.endTime=o.selectTime[0];o.calendarRender(o.calendarDoms[0],l);o.calendarRender(o.calendarDoms[1],o.selectTime[0])}else{o.options.startTime=f;o.options.endTime=j;o.calendarRender(o.calendarDoms[0],o.selectTime[0]);o.calendarRender(o.calendarDoms[1],o.selectTime[1])}break;case 8:if(t===q){var l=o.options.isDouble?o.subtractTime(o.selectTime[0],"year"):o.selectTime[0];o.options.startTime=l;o.options.endTime=o.selectTime[0];o.calendarRender(o.calendarDoms[0],l);o.calendarRender(o.calendarDoms[1],o.selectTime[0])}else{o.options.startTime=f;o.options.endTime=j;o.calendarRender(o.calendarDoms[0],o.selectTime[0]);o.calendarRender(o.calendarDoms[1],o.selectTime[1])}break;case 9:if(t-q<9){if(q<o.options.startYear){o.options.startTime=o.date[0];o.options.endTime=o.date[1];var k=Math.ceil((o.options.startYear-q)/9)*9;o.options.startTime=moment(o.options.startTime).subtract(k,"year").valueOf();o.options.endTime=o.options.isDouble?moment(o.options.startTime).add(k,"year").valueOf():this.date[1];o.calendarRender(o.calendarDoms[0],o.options.startTime);o.calendarRender(o.calendarDoms[1],o.options.endTime)}else{if(o.options.isDouble){o.options.startTime=o.date[0];var l=o.addTime(o.options.startTime,"year",9);o.options.endTime=l;o.calendarRender(o.calendarDoms[0],o.options.startTime);o.calendarRender(o.calendarDoms[1],l)}else{var k=Math.ceil((o.options.startYear-q)/9)*9;o.options.startTime=moment(o.options.startTime).subtract(k,"year").valueOf();o.calendarRender(o.calendarDoms[0],o.options.startTime)}}}else{var k=Math.ceil((o.options.startYear-q)/9)*9;o.options.startTime=moment(o.options.startTime).subtract(k,"year").valueOf();o.calendarRender(o.calendarDoms[0],o.options.startTime);o.calendarRender(o.calendarDoms[1],o.options.endTime)}break}};e.fn.extend({datePicker:function(f){f.id=this.attr("name");f.container=this;return new a(f)}})}(window,jQuery));