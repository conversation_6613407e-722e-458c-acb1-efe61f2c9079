1.�ύ�����ӿ�
URL�� POST  http://����/api/merchant/order.aspx
Form : api_userid=api_userid&api_secret=api_secret&type=order&appid=typeid&task_id=task_id&task_num=task_num&task_param=task_param&out_trade_no=out_trade_no

������
api_userid����ϵ�ͷ���ȡ
api_secret����ϵ�ͷ���ȡ
type���˲������䣬�̶�ֵorder
appid������ID��ǰ̨�ɼ���
task_id�������µ�����
task_num����������
task_param��������������������˲���Ϊʱ����
out_trade_no����ƽ̨�Ķ���ID����ѯʱ���أ�

����ֵ��json�ַ�������ʽ��{"code":1,"msg":"success","taskid":"10000"}


2.��ѯ�����ӿ�
URL�� POST  http://����/api/merchant/order.aspx

Form : api_userid=api_userid&api_secret=api_secret&type=info&taskid=10000

������
api_userid����ϵ�ͷ���ȡ
api_secret����ϵ�ͷ���ȡ
type���˲������䣬�̶�ֵinfo
taskid������ID

����ֵ��json�ַ�������ʽ��{"code":1,"msg":"success","DATA":[{"trade_no":10000,"time":"yyyy-MM-dd HH:mm:ss","appid":107,"task_url":"https://v.baidu.com/xxxx/","task_id":"xxx","task_num":"2000","renqi_hour":"2","out_trade_no":"2","process_s":"80","process_e":"2080","task_status":"�����"}]}


��ѯ���˵��
trade_no������ID
appid������ID
task_url�������µ�����
task_id���ڲ�����ID���ɺ��ԣ�
task_num����������
renqi_hour���µ���Сʱ��������������
out_trade_no����ƽ̨�Ķ���ID
process_s����ʼ����
process_e����ǰ����
task_status������״̬

״̬�б�
δ��ʼ
������
�����
�����쳣
�˵���
���˵�
δ������������
�����У�������


3.ȡ�������ӿ�
URL�� POST  http://����/api/merchant/order.aspx

Form : api_userid=api_userid&api_secret=api_secret&type=cancel&taskid=10000

������
api_userid����ϵ�ͷ���ȡ
api_secret����ϵ�ͷ���ȡ
type���˲������䣬�̶�ֵcancel
taskid������ID

����ֵ��json�ַ�������ʽ��{"code":1,"msg":"success"}


4.ˢ�¶����ӿ�
URL�� POST  http://����/api/merchant/order.aspx

Form : api_userid=api_userid&api_secret=api_secret&type=refresh&taskid=10000

������
api_userid����ϵ�ͷ���ȡ
api_secret����ϵ�ͷ���ȡ
type���˲������䣬�̶�ֵrefresh
taskid������ID

����ֵ��json�ַ�������ʽ��{"code":1,"msg":"success"}

