<%@ Application Language="C#" %>
<script RunAt="server">


    public static bool inRuning = false;
    public static bool startupTaskRuning = false;

    void Application_Start(object sender, EventArgs e)
    {
        if (!inRuning)
        {
            //sqlrecord("app启动", "");

            // 在应用程序启动时运行的代码
            System.Timers.Timer myTimer = new System.Timers.Timer(60000);//修改时间间隔
            //关联事件
            myTimer.Elapsed += new System.Timers.ElapsedEventHandler(mess);
            myTimer.AutoReset = true;
            myTimer.Enabled = true;

            inRuning = true;
        }

    }
    private void sqlrecord(string title, string content)
    {
        System.Data.DataTable dt = new System.Data.DataTable();
        System.Data.SqlClient.SqlParameter[] parames = null;
        string sql = string.Empty;
        dbClass db = new dbClass();


        parames = new System.Data.SqlClient.SqlParameter[]{
            new System.Data.SqlClient.SqlParameter("@title",title),
            new System.Data.SqlClient.SqlParameter("@content",content)
        };
        sql = " insert into appRunInfo values(@title,@content,GETDATE()) ";
        db.ExecuteNonQuery(sql, parames);

    }
    private void mess(object sender, System.Timers.ElapsedEventArgs e)
    {

        //sqlrecord("进入mess", "");
        int Minute = DateTime.Now.Minute;
        int hour = DateTime.Now.Hour;
        string errmsg = string.Empty;

        //每一分钟查询处理下任务

        dbClass db = new dbClass();
        System.Data.DataRow[] dr;
        System.Data.DataTable dt = new System.Data.DataTable();
        System.Data.DataSet ds = new System.Data.DataSet();

        string sql = "";
        string str = "";
        int res = 0;
        string[] g;
        LitJson.JsonData jd;
        System.Data.SqlClient.SqlParameter[] parames = null;

        globalClass gc = new globalClass();
        Dictionary<string, object> dic = new Dictionary<string, object>();




        string shopid = string.Empty;
        string rfToken = string.Empty;
        string token = string.Empty;
        string refresh_token = string.Empty;
        string strTemp = string.Empty;
        string str1 = string.Empty;
        string _code = string.Empty;
        string _start = string.Empty;
        string _now = string.Empty;
        string _status = string.Empty;
        string _appids = string.Empty;
        int checkCount = 0;


        //定时开启任务
        if (!startupTaskRuning)
        {
            startupTaskRuning = true;

            try
            {
                sql = string.Empty;


                sql = string.Empty;
                dt = db.getDataTable(" select id from orders with(nolock) where (order_type>0 and order_type<100) and status='退单中' ");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sql += " exec('[tuidan] " + dt.Rows[i]["id"] + ",''服务''') ";
                }


                dt = db.getDataTable(" select id from orders with(nolock) where market_id is not null and market_status=0 and status='已退单' or status='已完成' ");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sql += " exec('[market_order] " + dt.Rows[i]["id"] + "') ";
                }
                
                sql += " update orders set nowCount=(DATEDIFF(SECOND,createTime,GETDATE())/3600)+1,status=(case when GETDATE()>dateadd(hour,num,createTime) then '已完成' else status end),finishTime=(case when GETDATE()>dateadd(hour,num,createTime) then GETDATE() else finishTime end) where (order_type>0 and order_type<100) and (status='未开始' or status='进行中') ";



                //删除30天订单+记录
                if (hour == 3)//只在凌晨3点执行
                {
                    //sql += " delete orders where datediff(DAY,createTime,GETDATE())>10 and (status='已完成' or status='已退单')  ";
                    //sql += " delete orders where (code='dyzb-dc' or code='dyzb-bdc') and datediff(DAY,createTime,GETDATE())>10 and (status='已完成' or status='已退单') ";
                    //sql += " delete liveOrders where createTime<DATEADD(day,-10,GETDATE()) and GETDATE()>DATEADD(HOUR,total_day,createTime) ";
                    //sql += " delete records where datediff(DAY,createTime,GETDATE())>10 ";
                    //sql += " delete feeTotal where DATEDIFF(DAY,inDate,GETDATE())>3 ";
                }

                //if (hour == 2 && Minute == 38)//只在凌晨2点38分执行备份
                //{
                //    db.ExecuteNonQuery(@" BACKUP DATABASE zzzke TO DISK = 'C:\backup\database\sqlserver\zzzke_" + DateTime.Now.ToString("yyyyMMdd") + ".bak' WITH FORMAT, STATS = 1; ");
                //}

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                    errmsg = res.ToString();
                    sql = "";
                }

                // 更新慢刷期望值
                string speedSql = getSpssql();
                if (!string.IsNullOrEmpty(speedSql))
                {
                    sql += " update [orders] set expectNum=expectNum+" + speedSql + ",expectUpadate=GETDATE()  where speed=2 and datediff(SECOND,expectUpadate,getdate())>=3600 and (status='进行中' or status='未开始') ";
                }

                if (!string.IsNullOrEmpty(sql))
                {
                    res = db.ExecuteNonQuery(sql);
                    errmsg = res.ToString();
                    sql = "";
                }
            }
            catch (Exception ex)
            {
                errmsg = " 出错：" + ex.ToString() + "|sql:" + sql;
            }
            //sqlrecord("app执行结果", "操作成功：" + errmsg);

            startupTaskRuning = false;
        }

    }

    public string getSpssql()
    {
        dbClass db = new dbClass();
        System.Data.DataTable dt = new System.Data.DataTable();
        System.Data.DataRow[] dr;
        string sql = string.Empty;
        string defaultSpeed = string.Empty;


        sql = " select * from sps with(nolock) order by numin ";
        dt = db.getDataTable(sql);

        sql = string.Empty;
        dr = dt.Select("numin<>-1");
        if (dr.Length == 0)
        {
            return "";
        }
        for (int i = 0; i < dr.Length; i++)
        {
            //sql += string.Format(" when num<={0} then num/{1} ", dr[i]["numin"] + "", dr[i]["speed"] + "");

            //190409改版：speed改为时速
            sql += string.Format(" when num<={0} then {1} ", dr[i]["numin"] + "", dr[i]["speed"] + "");
        }

        dr = dt.Select("numin=-1");
        if (dr.Length > 0)
        {
            defaultSpeed = dr[0]["speed"] + "";
        }
        else
        {
            defaultSpeed = "500";
        }
        sql += string.Format(" else {0} ", defaultSpeed);
        sql = "(case " + sql + "end)+1";
        return sql;
    }

    void Application_End(object sender, EventArgs e)
    {
        try
        {
            startupTaskRuning = false;
            inRuning = false;
            System.Threading.Thread.Sleep(1000);
            string url = "http://baidu.com/";
            System.Net.HttpWebRequest myHttpWebRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);
            System.Net.HttpWebResponse myHttpWebResponse = (System.Net.HttpWebResponse)myHttpWebRequest.GetResponse();
            System.IO.Stream receiveStream = myHttpWebResponse.GetResponseStream();
        }
        catch (Exception)
        {
        }
    }

    void Application_Error(object sender, EventArgs e)
    {
        // 在出现未处理的错误时运行的代码

    }

    void Session_Start(object sender, EventArgs e)
    {
        // 在新会话启动时运行的代码
    }

    void Session_End(object sender, EventArgs e)
    {
        // 在会话结束时运行的代码。 
        // 注意: 只有在 Web.config 文件中的 sessionstate 模式设置为
        // InProc 时，才会引发 Session_End 事件。如果会话模式设置为 StateServer 
        // 或 SQLServer，则不会引发该事件。
    }
       
</script>
