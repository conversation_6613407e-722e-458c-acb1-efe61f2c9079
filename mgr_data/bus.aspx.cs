using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class admin_businessManager : baseClass
{
    public DataTable dt = new DataTable();
    public Dictionary<string, object> pmlist = new Dictionary<string, object>();
    protected void Page_Load(object sender, EventArgs e)
    {
        string sql = string.Empty;
        dbClass db = new dbClass();
        List<SqlParameter> pams = new List<SqlParameter>();

        pmlist["userid"] = Request.QueryString["userid"] + "";

        if (!string.IsNullOrEmpty(pmlist["userid"] + ""))
        {
            pams.Add(new SqlParameter("@userid", pmlist["userid"] + ""));
            sql = " select * from user_number_limit with(nolock) where userid=@userid ";
            dt = db.getDataTable(sql, pams.ToArray());
        }


    }
}