using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Security.Cryptography;
using System.Text;
using System.Net;
using System.Net.Cache;
using System.IO;
using System.Text.RegularExpressions;
using System.Net.Mail;
using System.Data.SqlClient;
using System.Data;
using LitJson;
using System.Web.Script.Serialization;
using System.Collections;
using System.Xml;

/// <summary>
/// globalClass 的摘要说明
/// </summary>
public class globalClass : System.Web.UI.Page
{
    public globalClass()
    {
    }
    #region 常用代码

    public static void newCookie(string cookieName, string cookieValue)
    {
        HttpContext.Current.Response.Cookies[cookieName].Expires = DateTime.Now.AddDays(-1);
        HttpContext.Current.Response.Cookies[cookieName].Value = HttpUtility.UrlEncode(cookieValue);
        HttpContext.Current.Response.Cookies[cookieName].Expires = DateTime.Now.AddDays(60);
    }

    public static string getCookie(string cookieName)
    {
        var ck = HttpContext.Current.Request.Cookies[cookieName];
        if (ck != null) return HttpUtility.UrlDecode(ck.Value);
        else return "";
    }

    public bool IsNaturalNumber(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    public bool IsDate(string strDate)
    {
        try
        {
            DateTime.Parse(strDate);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public Int64 getTimeStamp()
    {
        return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds);
    }

    public static bool IsNumeric(string value)
    {
        if (value == "")
        {
            return false;
        }
        return Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
    }

    public static string SafeSql(string str)
    {
        str = String.IsNullOrEmpty(str) ? "" : str.Replace("'", "''");
        str = new Regex("exec", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("xp_", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("sp_", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("net user", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("net localgroup", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("select", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("insert", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("update", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("delete", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("drop", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("create", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("rename", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("replace", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("truncate", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("alter", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("exists", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("master.", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("restore", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" set", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("set ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" and", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("and ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" where", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("where ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" like", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("like ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" order", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("order ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("print", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("convert", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" char", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("char ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" asc", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("asc ", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex(" mid", RegexOptions.IgnoreCase).Replace(str, "");
        str = new Regex("mid ", RegexOptions.IgnoreCase).Replace(str, "");
        return str;
    }

    public static string getHost(HttpRequest h)
    {
        string port = h.Url.Port.ToString();
        string _http = "http";
        if (port == "443")
        {
            port = "";
            _http = "https";
        }
        port = port == "80" ? "" : ":" + port;
        string host = _http + "://" + h.Url.Host + port;
        return host;
    }

    public static DataTable selectDateTable(DataTable oDT, string where = "", int TopItem = -1)
    {
        if (oDT.Rows.Count < TopItem) return oDT;

        DataTable NewTable = oDT.Clone();
        DataRow[] rows;
        int rowsLength;
        where = string.IsNullOrEmpty(where) ? "1=1" : where;
        rows = oDT.Select(where);
        rowsLength = rows.Length;
        if (TopItem > 0)
        {
            if (rowsLength > TopItem)
            {
                rowsLength = TopItem;
            }
        }
        for (int i = 0; i < rowsLength; i++)
        {
            NewTable.ImportRow((DataRow)rows[i]);
        }
        return NewTable;
    }

    public static string ToJson(DataTable dt)
    {
        JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
        javaScriptSerializer.MaxJsonLength = Int32.MaxValue; //取得最大数值
        ArrayList arrayList = new ArrayList();
        foreach (DataRow dataRow in dt.Rows)
        {
            Dictionary<string, object> dictionary = new Dictionary<string, object>();  //实例化一个参数集合
            foreach (DataColumn dataColumn in dt.Columns)
            {
                dictionary.Add(dataColumn.ColumnName, dataRow[dataColumn.ColumnName] + "");
            }
            arrayList.Add(dictionary); //ArrayList集合中添加键值
        }

        return javaScriptSerializer.Serialize(arrayList);  //返回一个json字符串
    }

    public string getResBody(string message, int code = -1, Dictionary<string, object> dic = null, string _code = "code", string _message = "msg")
    {
        if (dic == null)
        {
            dic = new Dictionary<string, object>();
        }
        Dictionary<string, object> _json = new Dictionary<string, object>();
        _json.Add(_code, code);
        _json.Add(_message, message);
        foreach (var k in dic)
        {
            _json.Add(k.Key, k.Value);
        }
        return JsonMapper.ToJson(_json);
    }

    public static string GetContentSummary(string content, int length, bool StripHTML)
    {
        if (string.IsNullOrEmpty(content) || length == 0)
            return "";
        if (StripHTML)
        {
            Regex re = new Regex("<[^>]*>");
            content = re.Replace(content, "");
            content = content.Replace("　", "").Replace(" ", "");
            if (content.Length <= length)
                return content;
            else
                return content.Substring(0, length) + "…";
        }
        else
        {
            if (content.Length <= length)
                return content;

            int pos = 0, npos = 0, size = 0;
            bool firststop = false, notr = false, noli = false;
            StringBuilder sb = new StringBuilder();
            while (true)
            {
                if (pos >= content.Length)
                    break;
                string cur = content.Substring(pos, 1);
                if (cur == "<")
                {
                    string next = content.Substring(pos + 1, 3).ToLower();
                    if (next.IndexOf("p") == 0 && next.IndexOf("pre") != 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                    }
                    else if (next.IndexOf("/p") == 0 && next.IndexOf("/pr") != 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                            sb.Append("<br/>");
                    }
                    else if (next.IndexOf("br") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                            sb.Append("<br/>");
                    }
                    else if (next.IndexOf("img") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                            size += npos - pos + 1;
                        }
                    }
                    else if (next.IndexOf("li") == 0 || next.IndexOf("/li") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                        }
                        else
                        {
                            if (!noli && next.IndexOf("/li") == 0)
                            {
                                sb.Append(content.Substring(pos, npos - pos));
                                noli = true;
                            }
                        }
                    }
                    else if (next.IndexOf("tr") == 0 || next.IndexOf("/tr") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                        }
                        else
                        {
                            if (!notr && next.IndexOf("/tr") == 0)
                            {
                                sb.Append(content.Substring(pos, npos - pos));
                                notr = true;
                            }
                        }
                    }
                    else if (next.IndexOf("td") == 0 || next.IndexOf("/td") == 0)
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        if (size < length)
                        {
                            sb.Append(content.Substring(pos, npos - pos));
                        }
                        else
                        {
                            if (!notr)
                            {
                                sb.Append(content.Substring(pos, npos - pos));
                            }
                        }
                    }
                    else
                    {
                        npos = content.IndexOf(">", pos) + 1;
                        sb.Append(content.Substring(pos, npos - pos));
                    }
                    if (npos <= pos)
                        npos = pos + 1;
                    pos = npos;
                }
                else
                {
                    if (size < length)
                    {
                        sb.Append(cur);
                        size++;
                    }
                    else
                    {
                        if (!firststop)
                        {
                            sb.Append("……");
                            firststop = true;
                        }
                    }
                    pos++;
                }

            }
            return sb.ToString();
        }
    }

    #endregion

    #region Http代码

    public string getContent(string url, int timeout = 0)
    {
        string content = "";
        try
        {
            HttpWebRequest webRequest = (HttpWebRequest)WebRequest.Create(url);
            if (timeout != 0)
            {
                webRequest.Timeout = timeout;
            }
            HttpWebResponse webResponse = (HttpWebResponse)webRequest.GetResponse();
            Stream streamOut = webResponse.GetResponseStream();
            StreamReader reader = new StreamReader(streamOut, Encoding.UTF8);
            content = reader.ReadToEnd();
            streamOut.Close();
            webResponse.Close();
        }
        catch (Exception es)
        {
            //throw;
            //content = es.ToString();
        }
        return content;
    }

    public static string GetHtml(string url, string cookieStr, string proxy = "")
    {
        return GetHtmlCon(url, cookieStr, true, proxy);
    }
    public static string GetHtmlCon(string url, string cookieStr, bool redire, string proxy = "")
    {
        string _local = string.Empty;
        return GetHtmlCon(url, cookieStr, redire, out _local, proxy);
    }
    public static string GetHtmlCon(string url, string cookieStr, bool redire, out string location, string proxy = "")
    {
        location = string.Empty;
        string header = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/538.38 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/538.38";
        string htmlCode;
        try
        {
            ServicePointManager.ServerCertificateValidationCallback += (s, cert, chain, sslPolicyErrors) => true;
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072;

            HttpWebRequest webRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(url);

            if (!string.IsNullOrEmpty(proxy))
            {
                WebProxy proxyObject = new WebProxy(proxy, true);//str为IP地址 port为端口号 代理类
                webRequest.Proxy = proxyObject; //设置代理   
            }

            webRequest.Timeout = 10000;
            webRequest.Method = "GET";
            webRequest.UserAgent = header;
            webRequest.Headers.Add("Accept-Encoding", "gzip, deflate");
            webRequest.Headers.Add("Cookie", cookieStr);
            webRequest.AllowAutoRedirect = redire;


            HttpWebResponse webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
            //获取目标网站的编码格式
            string contentype = webResponse.Headers["Content-Type"];
            Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);
            try
            {
                location = webResponse.Headers["location"];
            }
            catch (Exception)
            {

            }
            if (webResponse.ContentEncoding.ToLower() == "gzip")//如果使用了GZip则先解压
            {
                using (System.IO.Stream streamReceive = webResponse.GetResponseStream())
                {
                    using (var zipStream = new System.IO.Compression.GZipStream(streamReceive, System.IO.Compression.CompressionMode.Decompress))
                    {
                        //匹配编码格式
                        if (regex.IsMatch(contentype))
                        {
                            Encoding ending = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, ending))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                        else
                        {
                            using (StreamReader sr = new System.IO.StreamReader(zipStream, Encoding.UTF8))
                            {
                                htmlCode = sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            else
            {
                using (System.IO.Stream streamReceive = webResponse.GetResponseStream())
                {
                    using (System.IO.StreamReader sr = new System.IO.StreamReader(streamReceive, Encoding.Default))
                    {
                        htmlCode = sr.ReadToEnd();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            htmlCode = ex.Message;
        }
        return htmlCode;
    }

    public static string SendRequest(string formUrl, string formData, string content_type = "application/x-www-form-urlencoded")
    {
        try
        {

            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = content_type;
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR  3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)";
            request.ContentLength = postData.Length;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            HttpWebResponse response;
            Stream responseStream;
            StreamReader reader;
            string srcString;
            response = request.GetResponse() as HttpWebResponse;
            responseStream = response.GetResponseStream();
            reader = new System.IO.StreamReader(responseStream, Encoding.GetEncoding("UTF-8"));
            srcString = reader.ReadToEnd();
            string result = srcString;   //返回值赋值
            reader.Close();
            return result;
        }
        catch (Exception ex)
        {
            string a = ex.ToString();
            return a;
        }
    }

    /// <summary>
    /// Http同步Get同步请求
    /// </summary>
    /// <param name="url">Url地址</param>
    /// <param name="encode">编码(默认UTF8)</param>
    /// <returns></returns>
    public static string HttpGet(string url, Encoding encode = null)
    {
        string result;

        try
        {
            var webClient = new WebClient { Encoding = Encoding.UTF8 };

            if (encode != null)
                webClient.Encoding = encode;

            result = webClient.DownloadString(url);
        }
        catch (Exception ex)
        {
            result = ex.Message;
        }

        return result;
    }



    public static string SendRequest2(string formUrl, string formData)
    {
        try
        {

            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/json";
            request.UserAgent = "User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36";
            request.ContentLength = postData.Length;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            HttpWebResponse response;
            Stream responseStream;
            StreamReader reader;
            string srcString;
            response = request.GetResponse() as HttpWebResponse;
            responseStream = response.GetResponseStream();
            reader = new System.IO.StreamReader(responseStream, Encoding.Unicode);
            srcString = reader.ReadToEnd();
            string result = srcString;   //返回值赋值
            reader.Close();
            return result;
        }
        catch (Exception ex)
        {
            string a = ex.ToString();
            return a;
        }
    }

    public string getContentC(string url)
    {
        string content = "";
        try
        {
            string cookieStr = "";
            HttpWebRequest webRequest = (HttpWebRequest)WebRequest.Create(url);
            webRequest.KeepAlive = false;
            webRequest.AllowAutoRedirect = true;
            webRequest.ContentType = "application/x-www-form-urlencoded;charset=utf-8";
            webRequest.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36";
            webRequest.Headers.Add("Cookie", cookieStr);
            HttpWebResponse webResponse = (HttpWebResponse)webRequest.GetResponse();
            Stream streamOut = webResponse.GetResponseStream();
            StreamReader reader = new StreamReader(streamOut, System.Text.Encoding.UTF8);
            content = reader.ReadToEnd();
            streamOut.Close();
            webResponse.Close();
        }
        catch (Exception es)
        {
            content = es.ToString();
        }
        return content;
    }

    public static string SendRequestC(string formUrl, string formData, string cookieStr)
    {
        try
        {
            //注意提交的编码 这边是需要改变的 这边默认的是Default：系统当前编码
            byte[] postData = Encoding.UTF8.GetBytes(formData);

            // 设置提交的相关参数 
            HttpWebRequest request = WebRequest.Create(formUrl) as HttpWebRequest;
            Encoding myEncoding = Encoding.UTF8;
            request.Method = "POST";
            request.KeepAlive = false;
            request.AllowAutoRedirect = true;
            request.ContentType = "application/x-www-form-urlencoded";
            request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36";
            request.Headers.Add("Cookie", cookieStr);
            request.ContentLength = postData.Length;

            // 提交请求数据 
            System.IO.Stream outputStream = request.GetRequestStream();
            outputStream.Write(postData, 0, postData.Length);
            outputStream.Close();

            HttpWebResponse response;
            Stream responseStream;
            StreamReader reader;
            string srcString;
            response = request.GetResponse() as HttpWebResponse;
            responseStream = response.GetResponseStream();
            reader = new System.IO.StreamReader(responseStream, Encoding.UTF8);
            srcString = reader.ReadToEnd();
            string result = srcString;   //返回值赋值
            reader.Close();
            return result;
        }
        catch (Exception ex)
        {
            string a = ex.ToString();
            return a;
        }
    }

    #endregion

    #region ip相关
    public static string shortDob2(string dob)
    {
        if (Convert.ToDouble(IsNumeric(dob) ? dob : "0") == 0)
        {
            return "0";
        }
        else
        {
            return dob;
        }
    }

    public string getUserIP(HttpContext content)
    {
        string vno = content.Request.Url.Host;
        if (vno == "localhost")
        {
            return "*******";
        }
        string user_IP;
        if (System.Web.HttpContext.Current.Request.ServerVariables["HTTP_VIA"] != null)
        {
            user_IP = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            user_IP = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"].ToString();
        }

        if (user_IP == "" || user_IP == "...")
        {
            user_IP = GetIp2();
        }
        if (user_IP == "" || user_IP == "...")
        {
            user_IP = GetIp3();
        }
        return user_IP;
    }

    public string GetIp2()
    {
        return System.Web.HttpContext.Current.Request.ServerVariables.GetValues("REMOTE_ADDR")[0];
    }

    public string GetIp3()
    {
        return System.Web.HttpContext.Current.Request.UserHostAddress;
    }

    public bool IsValidIP(string ip)
    {
        if (System.Text.RegularExpressions.Regex.IsMatch(ip, "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}"))
        {
            string[] ips = ip.Split('.');
            if (ips.Length == 4)
            {
                try
                {
                    if (System.Int32.Parse(ips[0]) < 256 && System.Int32.Parse(ips[1]) < 256 & System.Int32.Parse(ips[2]) < 256 & System.Int32.Parse(ips[3]) < 256)
                        return true;
                    else
                        return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
            else
                return false;

        }
        else
            return false;
    }
    #endregion

    #region 取文本之间


    public string getContainer(string con, string leftStr, string rigStr)
    {
        string result = "";
        int sIndex = con.IndexOf(leftStr);
        if (sIndex != -1)
        {
            sIndex += leftStr.Length;
            int eIndex = con.IndexOf(rigStr, sIndex);
            if (eIndex != -1)
            {
                result = con.Substring(sIndex, eIndex - sIndex);
            }
        }
        return result;
    }

    /// <summary>
    /// 批量取文本之间
    /// </summary>
    /// <param name="con">源文本</param>
    /// <param name="leftStr">左文本</param>
    /// <param name="rigStr">右文本</param>
    /// <param name="isFilter">是否过滤重复</param>
    /// <returns></returns>
    public List<string> getContainerMul(string con, string leftStr, string rigStr, bool isFilter)
    {
        List<string> result = new List<string>();
        string fText = "";//过滤文本
        int sIndex = 0;
        int eIndex = 0 - rigStr.Length;

        do
        {
            sIndex = con.IndexOf(leftStr, eIndex + rigStr.Length);
            if (sIndex != -1)
            {
                sIndex += leftStr.Length;
                eIndex = con.IndexOf(rigStr, sIndex);
                if (eIndex != -1)
                {
                    string info = con.Substring(sIndex, eIndex - sIndex);
                    bool isAdd = isFilter ? fText.IndexOf(info) == -1 : true;
                    if (isAdd)
                    {
                        result.Add(info);
                        fText += info + ",";
                    }
                }
            }
        } while (sIndex != -1 && eIndex != -1);

        return result;
    }
    #endregion

    #region wky处理

    public string _http(string url, string type = "http")
    {
        string result = string.Empty;
        try
        {
            string temp = "http://127.0.0.1:12239/index?type=" + type + "&url=" + Uri.EscapeDataString(url) + "&r=1";

            if (HttpContext.Current.Request.Url.Host == "localhost")
            {
                temp = "http://***********:12239/index?type=" + type + "&url=" + Uri.EscapeDataString(url) + "&r=1";
            }
            //temp = "http://127.0.0.1:12239/index?type=jump&url=https://vt.tiktok.com/ZSdgkHPQJ/&r=1";

            result = getContent(temp);
            if (result == "error")
            {
                result = "";
            }
        }
        catch (Exception ex)
        {

        }
        return result;
    }

    public bool tiktokShortUrl(string short_url, out string new_url, out string source_url)
    {
        new_url = string.Empty;
        source_url = string.Empty;
        if (short_url.IndexOf("https://vt.tiktok.com/") == -1 && short_url.IndexOf("https://vm.tiktok.com/") == -1 && short_url.IndexOf("https://www.tiktok.com/t/") == -1)
        {
            return false;
        }

        string shortUrlId = getContainer(short_url + "/", "tiktok.com/", "/");
        if (short_url.IndexOf("https://vm.tiktok.com/") != -1)
        {
            source_url = "https://vm.tiktok.com/" + shortUrlId + "/";
        }
        else if (short_url.IndexOf("https://www.tiktok.com/t/") != -1)
        {
            shortUrlId = getContainer(short_url + "/", "tiktok.com/t/", "/");
            source_url = "https://www.tiktok.com/t/" + shortUrlId + "/";
        }
        else
        {
            source_url = "https://vt.tiktok.com/" + shortUrlId + "/";
        }

        string result = string.Empty;


        string jump_url = source_url;

        for (int i = 0; i < 3; i++)
        {
            if (i == 2 && new_url.IndexOf("/@/") == -1)
            {
                break;
            }

            result = _http(jump_url, "jump");
            //result = _http(jump_url);

            if (i == 2)
            {
                if (result.IndexOf('/') == 0)
                {
                    result = "https://www.tiktok.com" + result;
                }
            }


            log.WriteLog("tiktok_log", "surl", string.Format("URL[" + i.ToString() + "] # {0} \r\n result # {1}", jump_url, result));

            //new_url = getContainer(result, "href=\"", "\"");
            new_url = result;

            if (result.IndexOf("<html>") != -1)
            {
                //出现滑块，直接成功
                new_url = short_url;
                return true;
            }
            if (string.IsNullOrEmpty(new_url) || (new_url.IndexOf("https://t.tiktok.com/") != 0 && new_url.IndexOf("https://m.tiktok.com/v/") != 0 && new_url.IndexOf("/@/") == -1))
            {
                break;
            }
            jump_url = new_url.Replace("&amp;", "&");
        }
        new_url = getContainer("start_" + new_url + "?", "start_", "?");

        if (new_url == "")
        {
            log.WriteLog("tiktok_log", "surl_empty", string.Format("URL # {0} \r\n result # {1}", source_url, result));

            return false;
        }
        return true;
    }

    public bool checkTiktokUrl(string url, string code, out string id)
    {
        id = string.Empty;

        if (url.IndexOf("https://vm.tiktok.com/") != -1 || url.IndexOf("https://vt.tiktok.com/") != -1)
        {
            return true;
        }

        //log.WriteLog("tiktok_log", "check", string.Format("URL # {0} \r\n code # {1}", url, code));

        string _userName = string.Empty;
        string _userId = string.Empty;
        string _videoId = string.Empty;
        string _content = string.Empty;
        string _secret = string.Empty;

        //switch (code)
        //{
        //    case "tk-dz":
        //    case "tk-dz2":
        //    case "tk-bf":
        //    case "tk-bf2":
        //    case "tk-bf3":
        //    case "tk-pldz":
        //    case "tk-zrdz":
        //    case "tk-plzdy":
        //        //用户视频链接
        //        if (url.IndexOf("https://www.tiktok.com/@") != -1 && url.IndexOf("/video/") != -1)
        //        {
        //            _userName = getContainer(url + "/", "/@", "/");
        //            _videoId = getContainer(url + "/", "/video/", "/");
        //            _videoId = getContainer("/" + _videoId + "?", "/", "?");
        //            if (!string.IsNullOrEmpty(_userName) && !string.IsNullOrEmpty(_videoId))
        //            {
        //                //id = _videoId + "|" + _userName;
        //                id = "https://www.tiktok.com/@" + _userName + "/video/" + _videoId;
        //            }
        //        }
        //        break;
        //    default:
        //        if (code.IndexOf("tk-gz") == 0 || code.IndexOf("tk-zrgz") == 0 || code.IndexOf("tk-zb") == 0)
        //        {
        //            //用户主页链接 or 视频链接
        //            if (url.IndexOf("https://www.tiktok.com/@") != -1)
        //            {
        //                _userName = getContainer(url + "/", "/@", "/");
        //                _userName = getContainer("@" + _userName + "?", "@", "?");
        //                //_userId = getContainer(url + "&", "&user_id=", "&");

        //                if (!string.IsNullOrEmpty(_userName))
        //                {
        //                    id = "https://www.tiktok.com/@" + _userName;
        //                    //_content = _http(url);
        //                    //_userId = getContainer(_content, "{\"users\":{\"" + _userName + "\":{\"id\":\"", "\"");
        //                    //_secret = getContainer(_content, "\"secret\":", ",");

        //                    ////secret
        //                    //if (!string.IsNullOrEmpty(_userId) && _secret != "true")
        //                    //{
        //                    //    id = _userId + "|" + _userName;
        //                    //}
        //                    //else
        //                    //{
        //                    //    log.WriteLog("tiktok_log", "url", string.Format("URL # {0} \r\n Content # {1}\r\n userId # {2}\r\n", url, "{}", _userId, _secret));
        //                    //}
        //                }
        //            }
        //        }
        //        break;
        //}


        if (code.IndexOf("tk-gz") == 0 || code.IndexOf("tk-zrgz") == 0 || code.IndexOf("tk-zb") == 0)
        {
            //用户主页链接 or 视频链接
            if (url.IndexOf("https://www.tiktok.com/@") != -1 || url.IndexOf("https://tiktok.com/@") != -1)
            {
                _userName = getContainer(url + "/", "/@", "/");
                _userName = getContainer("@" + _userName + "?", "@", "?");
                //_userId = getContainer(url + "&", "&user_id=", "&");

                if (!string.IsNullOrEmpty(_userName))
                {
                    id = "https://www.tiktok.com/@" + _userName;
                    //_content = _http(url);
                    //_userId = getContainer(_content, "{\"users\":{\"" + _userName + "\":{\"id\":\"", "\"");
                    //_secret = getContainer(_content, "\"secret\":", ",");

                    ////secret
                    //if (!string.IsNullOrEmpty(_userId) && _secret != "true")
                    //{
                    //    id = _userId + "|" + _userName;
                    //}
                    //else
                    //{
                    //    log.WriteLog("tiktok_log", "url", string.Format("URL # {0} \r\n Content # {1}\r\n userId # {2}\r\n", url, "{}", _userId, _secret));
                    //}
                }
            }
        }
        else
        {
            //用户视频链接
            if (url.IndexOf("https://www.tiktok.com/@") != -1 && url.IndexOf("/video/") != -1)
            {
                _userName = getContainer(url + "/", "/@", "/");
                _videoId = getContainer(url + "/", "/video/", "/");
                _videoId = getContainer("/" + _videoId + "?", "/", "?");
                if (!string.IsNullOrEmpty(_userName) && !string.IsNullOrEmpty(_videoId))
                {
                    //id = _videoId + "|" + _userName;
                    id = "https://www.tiktok.com/@" + _userName + "/video/" + _videoId;
                }
            }
        }


        return id != string.Empty;
    }

    public bool checkDouyinUrl(string url, string code, out string videoID, out string userid)
    {
        string res = string.Empty;
        string rsp = string.Empty;
        string my_url = string.Empty;
        userid = string.Empty;
        if (url.IndexOf("video/") == -1)
        {
            my_url = url;
            Regex reg = new Regex("\\d{16,}");
            MatchCollection mats = reg.Matches(url);
            url = "";
            foreach (Match mat in mats)
            {
                url = mat.Groups[0].Value.Replace("s=", "");
                break;
            }
            res = url;


            if (string.IsNullOrEmpty(res))
            {
                rsp = GetHtmlCon(my_url, "", false);
                url = getContainer(rsp, "to target URL: <a href=\"", "\"");
                reg = new Regex("\\d{16,}");
                mats = reg.Matches(url);
                url = "";
                foreach (Match mat in mats)
                {
                    url = mat.Groups[0].Value.Replace("s=", "");
                    break;
                }
                res = url;
            }

        }
        else
        {
            res = getContainer(url + "/", "video/", "/");
        }
        videoID = res;


        if (code.IndexOf("dy-gz") != -1 || code.IndexOf("dy-gzm") != -1)
        {
            if (!string.IsNullOrEmpty(videoID))
            {
                log.WriteLog("douyin_id", videoID, "req");
                panda pd = new panda();
                try
                {
                    res = pd.dyPhotoIDToUserID(videoID);
                    //res = pd.dyPhotoIDToUserIDQlike(videoID);
                    JsonData jd = new JsonData();
                    jd = JsonMapper.ToObject(res);
                    userid = jd["uid"] + "";
                    //userid = jd["aweme_detail"]["author"]["uid"] + "";
                }
                catch (Exception)
                {

                }
                log.WriteLog("douyin_id", videoID, res + "----" + userid);
            }
        }
        return res != "";
    }


    public bool checkYtbUrl(string code, string url, out string yid)
    {
        string _result = string.Empty;
        yid = string.Empty;

        switch (code)
        {
            case "ytb-sub":
                //https://www.youtube.com/channel/UC79nHf3brWppKHhEPCUBAzA
                yid = getContainer(url + "/", "/channel/", "/");
                break;
            case "ytb-like":
                //https://www.youtube.com/watch?v=vfmfhYAvOYA
                yid = getContainer(url + "&", "/watch?v=", "&");
                break;
            default:
                break;
        }

        return yid != "";
    }

    public bool checkDouyinPersonUrl(string code, string url, out string videoID, out string dy_user_id)
    {
        JsonData jd;
        string res = string.Empty;
        string rsp = string.Empty;
        string my_url = string.Empty;
        string check_url = string.Empty;

        dy_user_id = string.Empty;



        //if (IsNumeric(url))
        //{
        //    videoID = url;
        //    return true;
        //}

        if (url.IndexOf("share/user/") != -1)
        {
            if (url.IndexOf("share/user/") != -1)
            {
                res = getContainer(url + "/", "share/user/", "/");
                //https://www.douyin.com/user/MS4wLjABAAAAm4eD_lCc-mDlpH6hPUJK5Y3l7TeRpeQ8e5EbAH1Hmb_wa9FT3LHEbKbPP5fQqtk6?previous_page=app_code_link
            }
            else
            {
                //res = getContainer(url + "/", "share/live/", "/");
            }
            if (res.IndexOf("?") != -1)
            {
                res = getContainer("!" + res, "!", "?");
            }

            res = getMsUid(res, res);


        }
        else if (url.IndexOf("share/live/") != -1)
        {
            res = getContainer(url + "/", "share/live/", "/");
            if (res.IndexOf("?") != -1)
            {
                res = getContainer("!" + res, "!", "?");
            }
        }
        else if (url.IndexOf("share/video/") != -1)
        {
            res = getContainer(url + "/", "share/video/", "/");
            if (res.IndexOf("?") != -1)
            {
                res = getContainer("!" + res, "!", "?");
            }
        }
        else if (url.IndexOf("webcast/reflow/") != -1)
        {
            res = getContainer(url + "?", "webcast/reflow/", "?");
        }
        else
        {
            check_url = url;
            if (IsNumeric(url))
            {
                check_url = "https://www.iesdouyin.com/share/user/" + url;
            }
            rsp = GetHtmlCon(check_url, "", false);
            res = getContainer(rsp, "uid: \"", "\"");


            if (string.IsNullOrEmpty(res))
            {
                if (rsp == "" || rsp.IndexOf("【抖音短视频】记录美好生活-Douyin.com") == -1)
                {
                    if (IsNumeric(url))
                    {
                        res = url;
                    }
                }
            }
            else
            {
                log.WriteLog("order-UrlError", "uid空", "result:" + rsp);
            }

            //if (string.IsNullOrEmpty(res) && url.Length > 16)
            //{
            //    res = getLiveUserId(url);
            //}
        }
        if (!string.IsNullOrEmpty(res) && (code == "dyzb-rc" || code == "dyzb-dz" || code == "dyzb-gzzs"))
        {
            res = getLiveId(res);
            string liveid = res;
            //if (!string.IsNullOrEmpty(res) && code == "dyzb-rc")
            //{
            //    if (!checkDyzbRenci(res))
            //    {
            //        res = string.Empty;//不支持人次的订单（room_layout=0）
            //    }
            //}

            log.WriteLog("dyrc_check", url, "直播间ID：" + liveid + ",检测结果：" + res);
        }
        else
        {
            url = res;
            if (code.IndexOf("dyzb-") == -1 || code.IndexOf("dc") == -1)
            {
                res = getLiveUserIdNew(url);
            }
        }
        videoID = res;
        return res != "";
    }

    public string getLiveId(string liveid)
    {
        string rsp = string.Empty;
        if (checkIsLiveId(liveid))
        {
            return liveid;
        }
        else
        {
            rsp = GetHtmlCon(uConfig._static_dyApiurl + "douyin_live_room_id?token=dyfuck&proxy=1&user_id=" + liveid, "", false);
            if (!string.IsNullOrEmpty(rsp) && checkIsLiveId(liveid))
            {
                if (IsNumeric(rsp))
                {
                    return rsp;
                }
                log.WriteLog("api-query-error", liveid, rsp);
            }
            return string.Empty;
        }
    }

    public bool checkDyzbRenci(string liveid)
    {
        string rsp = string.Empty;
        JsonData jd;

        try
        {
            rsp = GetHtmlCon("https://webcast-hl.amemv.com/webcast/room/reflow/info/?room_id=" + liveid + "&type_id=0&user_id=0&live_id=1&app_id=1128", "", false);
            jd = JsonMapper.ToObject(rsp);
            if (jd["data"]["room"]["room_layout"] + "" == "1")
            {
                return true;
            }
        }
        catch (Exception)
        {

        }
        return false;
    }

    public string getVideoUserid(string liveid)
    {
        string check_url = string.Empty;
        string rsp = string.Empty;
        string res = string.Empty;
        JsonData jd;
        liveid = liveid.Replace(" ", "");
        if (checkIsLiveId(liveid))
        {
            rsp = GetHtmlCon("https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids=" + liveid, "", false);
            try
            {
                jd = JsonMapper.ToObject(rsp);
                res = jd["item_list"][0]["author_user_id"] + "";
            }
            catch (Exception)
            {
                res = string.Empty;
            }
        }
        else
        {
            res = liveid;
        }
        return res;
    }

    public string getLiveUserIdNew(string liveid)
    {
        string check_url = string.Empty;
        string rsp = string.Empty;
        string res = string.Empty;
        JsonData jd;
        liveid = liveid.Replace(" ", "");
        if (checkIsLiveId(liveid))
        {
            string temp = string.Empty;
            temp = getVideoUserid(liveid);
            if (string.IsNullOrEmpty(temp))
            {
                rsp = GetHtmlCon("https://webcast-hl.amemv.com/webcast/room/reflow/info/?room_id=" + liveid + "&type_id=0&live_id=1&app_id=1128", "", false);
                try
                {
                    jd = JsonMapper.ToObject(rsp);
                    res = jd["data"]["room"]["owner_user_id"] + "";
                }
                catch (Exception)
                {
                    res = string.Empty;
                }
            }
            else
            {
                res = temp;
            }
        }
        else
        {
            res = liveid;

            res = getMsUid(res, res);

        }
        if (string.IsNullOrEmpty(res))
        {
            log.WriteLog("order-UrlError", "msuid-liveid空", "result:" + rsp);
        }
        return res;
    }

    public string getLiveUserId(string liveid)
    {
        string check_url = string.Empty;
        string rsp = string.Empty;
        string res = string.Empty;

        check_url = "https://webcast-hl.amemv.com/webcast/room/reflow/info/?room_id=" + liveid + "&type_id=0&user_id=1&live_id=1&app_id=1128";
        rsp = GetHtmlCon(check_url, "", false);
        res = getContainer(rsp, "owner_user_id\":", ",");
        return res;
    }

    public static string douyin_short_urlid(string _url)
    {
        string result = string.Empty;
        if (_url.IndexOf("://v.douyin.com/") != -1)
        {
            globalClass gc = new globalClass();
            string code_temp = gc.getContainer(_url + "/", "://v.douyin.com/", "/");
            _url = "http://v.douyin.com/" + code_temp + "/";
            string rsp_temp = GetHtmlCon(_url, "", false);
            _url = gc.getContainer(rsp_temp, "<a href=\"", "\"");
            //return _url + "___" + rsp_temp;
            result = _url;
            if (_url.IndexOf("/share/user/") != -1)
            {
                result = gc.getContainer(_url + "?", "share/user/", "?");
            }
            else if (_url.IndexOf("/webcast/reflow/") != -1)
            {
                result = gc.getContainer(_url + "?", "webcast/reflow/", "?");
            }
        }
        else
        {
            result = _url;
        }
        return result;
    }

    #endregion


    #region 百度评论检测


    public static string bdaicechekey = "bdai_access_token";
    public static string getBAIAccessToken()
    {
        string access_token = string.Empty;
        access_token = (string)cae.GetCache(bdaicechekey);
        if (access_token == null || string.IsNullOrEmpty(access_token))
        {
            string client_id = "";
            string client_secret = "";
            string httpurl = string.Format("https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={0}&client_secret={1}", client_id, client_secret);
            string res = GetHtml(httpurl, "");
            JsonData jd;
            string expires_in = string.Empty;

            access_token = string.Empty;
            try
            {
                jd = JsonMapper.ToObject(res);
                access_token = jd["access_token"] + "";
                expires_in = jd["expires_in"] + "";
            }
            catch (Exception)
            { }

            if (!string.IsNullOrEmpty(access_token))
            {
                cae.SetCache(bdaicechekey, access_token, DateTime.Now.AddHours(1));
            }
        }
        return access_token;
    }
    public static int checkComment(string comment)
    {
        int res = 10001;
        string access_token = getBAIAccessToken();
        if (string.IsNullOrEmpty(access_token))
        {
            //access_token获取失败
            return res;
        }
        string httpurl = "https://aip.baidubce.com/rest/2.0/antispam/v2/spam?access_token=" + access_token;
        string body = "content=" + Uri.EscapeDataString(comment);

        //新内容审核
        httpurl = "https://aip.baidubce.com/rest/2.0/solution/v1/text_censor/v2/user_defined?access_token=" + access_token;
        body = "text=" + Uri.EscapeDataString(comment);

        string rsp = SendRequest(httpurl, body);
        JsonData jd;
        jd = JsonMapper.ToObject(rsp);
        string spam = string.Empty;
        try
        {
            //spam = jd["result"]["spam"] + "";
            spam = jd["conclusionType"] + "";//1.合规，2.不合规，3.疑似，4.审核失败
        }
        catch (Exception)
        { }
        if (string.IsNullOrEmpty(spam))
        {
            //检测失败
            return 10002;
        }
        if (spam != "1")
        {
            log.WriteLog("comment_check_log", "", "result:" + spam + "，comment:" + comment + "，result:" + rsp);
        }
        res = Convert.ToInt16(spam);
        return res;
    }

    #endregion

    public static void logerror(string type, string tile, string content)
    {
        dbClass db = new dbClass();
        SqlParameter[] parames = new SqlParameter[]{
            new SqlParameter("@type",type),
            new SqlParameter("@title",tile),
            new SqlParameter("@content",content)
        };

        string sql = " insert into [logerr] values(@type,@title,@content,GETDATE()) ";
        db.ExecuteNonQuery(sql, parames);
    }

    public string _config(string text, string type = "messages", int index = -1)
    {
        try
        {
            string json = File.ReadAllText(HttpContext.Current.Server.MapPath("~/App_Code/config.json"));
            JsonData jd = JsonMapper.ToObject(json);
            jd = jd[type];
            string result = string.Empty;
            string[] g = text.Split('.');
            text = g[g.Length - 1];
            for (int i = 0; i < g.Length - 1; i++)
            {
                jd = jd[g[i]];
            }

            if (index == -1)
            {
                result = jd[text].ToString();
            }
            else
            {
                result = jd[text][index].ToString();
            }
            return result;
        }
        catch (Exception ex)
        {
            string errmsg = ex.ToString();
        }
        return null;
    }

    public bool checkIsLiveId(string liveid)
    {
        return ((liveid.IndexOf("68") == 0 || liveid.IndexOf("69") == 0 || liveid.IndexOf("70") == 0 || liveid.IndexOf("71") == 0 || liveid.IndexOf("72") == 0 || liveid.IndexOf("73") == 0) && liveid.Length > 16);
    }

    public string getMsUid(string id, string default_val = "")
    {
        string result = string.Empty;
        string temp_result = string.Empty;
        if (!string.IsNullOrEmpty(id) && (id.IndexOf("MS") == 0 || id.Length > 30))
        {
            JsonData jd;
            for (int i = 0; i < 3; i++)
            {
                result = getContent("https://www.iesdouyin.com/web/api/v2/user/info/?sec_uid=" + id);
                temp_result += result + "\r\n------------------------";
                try
                {
                    jd = JsonMapper.ToObject(result);
                    result = jd["user_info"]["uid"] + "";
                }
                catch (Exception)
                {
                    result = string.Empty;
                }
                if (!string.IsNullOrEmpty(result))
                {
                    break;
                }
            }
        }
        else
        {
            result = default_val;
        }
        if (string.IsNullOrEmpty(result))
        {
            log.WriteLog("order-UrlError", "msuid空", "result:" + temp_result);

        }
        return result;
    }

    public string formatOrderids(string orderids)
    {
        string[] s = orderids.Split(',');
        int check_count = 0;

        orderids = "";
        for (int i = 0; i < s.Length; i++)
        {
            if (IsNumeric(s[i]) && s[i].Length < 10)
            {
                if (orderids != "")
                {
                    orderids += ",";
                }
                orderids += s[i];
                check_count += 1;
            }
        }
        return orderids;
    }

    #region 获取用户登陆IP
    ///<summary>
    /// 获取用户登陆IP
    /// </summary>
    /// <returns>返回用户IP</returns>
    public string getUserIP()
    {
        string user_IP;
        if (System.Web.HttpContext.Current.Request.ServerVariables["HTTP_VIA"] != null)
        {
            user_IP = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            user_IP = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"].ToString();
        }

        if (user_IP == "" || user_IP == "...")
        {
            user_IP = GetIp2();
        }
        if (user_IP == "" || user_IP == "...")
        {
            user_IP = GetIp3();
        }
        return user_IP;
    }
    #endregion


    public static string GetMD5HashFromStream(Stream stream)
    {
        try
        {
            stream.Seek(0, SeekOrigin.Begin);
            System.Security.Cryptography.MD5CryptoServiceProvider md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] data = md5.ComputeHash(stream);
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }
            return sBuilder.ToString();
        }
        catch (Exception ex)
        {
            throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
        }
    }

    public bool checkPath(string path)
    {
        string _path = System.Web.HttpContext.Current.Server.MapPath(path);
        if (!System.IO.Directory.Exists(_path))
        {
            System.IO.Directory.CreateDirectory(_path);
            return false;
        }
        else
        {
            return true;
        }
    }


    public string md5(string str)
    {
        byte[] result = Encoding.Default.GetBytes(str);    //tbPass为输入密码的文本框
        MD5 md5 = new MD5CryptoServiceProvider();
        byte[] output = md5.ComputeHash(result);
        str = BitConverter.ToString(output).Replace("-", "");
        return str.ToLower();
    }


    public static object ConvertDateToTimeSpan(object date)
    {
        if (date == DBNull.Value || string.IsNullOrEmpty(date.ToString()))
        {
            return null;
        }
        return (Convert.ToDateTime(date) - new DateTime(1970, 1, 1, 8, 0, 0, 0)).TotalSeconds.ToString();
    }



    /// <summary>
    /// 将字典参数转为XML
    /// </summary>
    /// <param name="_params"></param>
    /// <returns></returns>
    public static string toXml(SortedDictionary<string, object> dic)
    {
        StringBuilder sb = new StringBuilder("<xml>");
        foreach (var item in dic)
        {
            string key = item.Key + "";
            sb.Append("<").Append(key).Append("><![CDATA[").Append(item.Value.ToString()).Append("]]></").Append(key).Append(">");
        }
        return sb.Append("</xml>").ToString();
    }

    public string xmlToJson(string content)
    {
        Dictionary<string, object> dic = new Dictionary<string, object>();
        try
        {
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(content);
            XmlNode root = xmlDoc.SelectSingleNode("xml");
            XmlNodeList xnl = root.ChildNodes;

            foreach (XmlNode xnf in xnl)
            {
                dic.Add(xnf.Name, xnf.InnerText);
            }
        }
        catch (Exception)
        {
        }
        return JsonMapper.ToJson(dic);
    }


    public DataTable market(string sp)
    {
        var id = "market_" + sp;
        DataTable dt = (DataTable)cae.GetCache(id);
        if (dt == null)
        {
            List<SqlParameter> pams = new List<SqlParameter>();
            pams.Add(new SqlParameter("@sp", sp));
            string sql = " select * from user_market with(nolock) where sp=@sp ";
            dbClass db = new dbClass();
            dt = db.getDataTable(sql, pams.ToArray());

            cae.SetCache(id, dt);
        }
        return dt;
    }


    public string market_price(string appid, string sp, string userid, string def = "")
    {
        if (string.IsNullOrEmpty(sp))
        {
            return def;
        }
        DataTable dt = market(sp);
        string total_fee = string.Empty;

        dt = selectDateTable(dt, " appid=" + appid + " and uid=" + userid);
        if (dt.Rows.Count > 0)
        {
            total_fee = dt.Rows[0]["total_fee"] + "";
        }
        else
        {
            total_fee = def;
        }
        return total_fee;
    }
}