<?xml version="1.0"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <system.web>
    <customErrors mode="Off" defaultRedirect="~/404.html"/>
    <compilation debug="true" targetFramework="4.0"/>
    <httpRuntime requestValidationMode="2.0"/>
  </system.web>
  <connectionStrings>
    <!--阿里云-->
	  <!--<add name="ConnectionSql" connectionString="server=.;database=tksqldb;uid=sa;pwd=********************************" providerName="System.Data.SqlClient" />-->
    
    <add name="servHttpUrl" connectionString="/api/fastOrder.aspx" providerName="System.Data.SqlClient" />

    <add name="ConnectionSql" connectionString="server=.;database=zzzke;uid=sa;pwd=sqltest123." providerName="System.Data.SqlClient" />

    
  </connectionStrings>
  <system.webServer>
    <directoryBrowse enabled="false"/>
    <defaultDocument>
      <files>
        <add value="../index.aspx" />
      </files>
    </defaultDocument>
    
    
    <rewrite>
      <rules>
        <rule name="已导入的规则 1">
          <match url="^api/user/order/(.*)/(.*)" ignoreCase="false" />
          <action type="Rewrite" url="/api/order.aspx?apiUid={R:1}&amp;apiToken={R:2}" appendQueryString="true" />
        </rule>
        <rule name="已导入的规则 2">
          <match url="^wiky/login" ignoreCase="false" />
          <action type="Rewrite" url="/wiky/login.aspx" appendQueryString="true" />
        </rule>
        <rule name="已导入的规则 3">
          <match url="^wiky/index" ignoreCase="false" />
          <action type="Rewrite" url="/wiky/index.aspx" appendQueryString="true" />
        </rule>
        <rule name="已导入的规则 4">
          <match url="^wiky/api" ignoreCase="false" />
          <action type="Rewrite" url="/wiky/apis.aspx" appendQueryString="true" />
        </rule>
        <rule name="已导入的规则 5">
          <match url="^wiky/order" ignoreCase="false" />
          <action type="Rewrite" url="/wiky/tasks.aspx" appendQueryString="true" />
        </rule>
        <rule name="已导入的规则 6">
          <match url="^wiky/flowOrder" ignoreCase="false" />
          <action type="Rewrite" url="/wiky/flowOrder.aspx" appendQueryString="true" />
        </rule>
        <rule name="已导入的规则 7">
          <match url="^json/(.*)" ignoreCase="false" />
          <action type="Rewrite" url="/json.aspx?do={R:1}" appendQueryString="true" />
        </rule>
      </rules>
    </rewrite>



  </system.webServer>
  <system.serviceModel>
    <bindings/>
    <client/>
  </system.serviceModel>
</configuration>