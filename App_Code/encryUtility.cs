using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;

/// <summary>
/// encryUtility 的摘要说明
/// </summary>
public class encryUtility
{
    //默认密钥
    private static string AESKey = "DOUYIN/ee7EQQJC5";
    private static string DESKey = "[&HdN72]";

    public encryUtility()
    {
    }

    /// <summary> 
    /// AES加密 
    /// </summary>
    public static string AESEncrypt(string value, string _aeskey = null)
    {
        if (string.IsNullOrEmpty(_aeskey))
        {
            _aeskey = AESKey;
        }

        byte[] keyArray = Encoding.UTF8.GetBytes(_aeskey);
        byte[] toEncryptArray = Encoding.UTF8.GetBytes(value);

        RijndaelManaged rDel = new RijndaelManaged();
        rDel.Key = keyArray;
        rDel.Mode = CipherMode.ECB;
        rDel.Padding = PaddingMode.PKCS7;

        ICryptoTransform cTransform = rDel.CreateEncryptor();
        byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

        return Convert.ToBase64String(resultArray, 0, resultArray.Length);
        //return ToBCDStringLower(resultArray);
        
    }

    /// <summary> 
    /// AES解密 
    /// </summary>
    public static string AESDecrypt(string value, string _aeskey = null)
    {
        try
        {
            if (string.IsNullOrEmpty(_aeskey))
            {
                _aeskey = AESKey;
            }
            byte[] keyArray = Encoding.UTF8.GetBytes(_aeskey);
            byte[] toEncryptArray = Convert.FromBase64String(value);
            //byte[] toEncryptArray = FromBCDString(value);
            
            RijndaelManaged rDel = new RijndaelManaged();
            rDel.Key = keyArray;
            rDel.Mode = CipherMode.ECB;
            rDel.Padding = PaddingMode.PKCS7;

            ICryptoTransform cTransform = rDel.CreateDecryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

            return Encoding.UTF8.GetString(resultArray);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary> 
    /// DES加密 
    /// </summary>
    public static string DESEncrypt(string value, string _deskey = null)
    {
        if (string.IsNullOrEmpty(_deskey))
        {
            _deskey = DESKey;
        }

        byte[] keyArray = Encoding.UTF8.GetBytes(_deskey);
        byte[] toEncryptArray = Encoding.UTF8.GetBytes(value);

        DESCryptoServiceProvider rDel = new DESCryptoServiceProvider();
        rDel.Key = keyArray;
        rDel.Mode = CipherMode.ECB;
        rDel.Padding = PaddingMode.PKCS7;

        ICryptoTransform cTransform = rDel.CreateEncryptor();
        byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

        return Convert.ToBase64String(resultArray, 0, resultArray.Length);
    }

    /// <summary> 
    /// DES解密 
    /// </summary>
    public static string DESDecrypt(string value, string _deskey = null)
    {
        try
        {
            if (string.IsNullOrEmpty(_deskey))
            {
                _deskey = DESKey;
            }
            byte[] keyArray = Encoding.UTF8.GetBytes(_deskey);
            byte[] toEncryptArray = Convert.FromBase64String(value);

            DESCryptoServiceProvider rDel = new DESCryptoServiceProvider();
            rDel.Key = keyArray;
            rDel.Mode = CipherMode.ECB;
            rDel.Padding = PaddingMode.PKCS7;

            ICryptoTransform cTransform = rDel.CreateDecryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

            return Encoding.UTF8.GetString(resultArray);
        }
        catch
        {
            return string.Empty;
        }
    }

    public static string MD5(string value)
    {
        byte[] result = Encoding.UTF8.GetBytes(value);
        MD5 md5 = new MD5CryptoServiceProvider();
        byte[] output = md5.ComputeHash(result);
        return BitConverter.ToString(output).Replace("-", "");
    }

    public static string HMACMD5(string value, string hmacKey)
    {
        HMACSHA1 hmacsha1 = new HMACSHA1(Encoding.UTF8.GetBytes(hmacKey));
        byte[] result = System.Text.Encoding.UTF8.GetBytes(value);
        byte[] output = hmacsha1.ComputeHash(result);


        return BitConverter.ToString(output).Replace("-", "");
    }

    /// <summary>
    /// base64编码
    /// </summary>
    /// <returns></returns>
    public static string Base64Encode(string value)
    {
        string result = Convert.ToBase64String(Encoding.Default.GetBytes(value));
        return result;
    }
    /// <summary>
    /// base64解码
    /// </summary>
    /// <returns></returns>
    public static string Base64Decode(string value)
    {
        string result = Encoding.Default.GetString(Convert.FromBase64String(value));
        return result;
    }



    public static byte[] FromBCDString(string buffer)
    {
        if (buffer == null) return null;
        int start = 0;
        int count = buffer.Length;
        bool inCase = false;
        byte cur = 0;
        int dataEnd = start + count;
        List<byte> lst = new List<byte>(count / 2);
        while (start < dataEnd)
        {
            byte num = (byte)buffer[start++];
            if (num == ' ' || num == '\r' || num == '\n' || num == '\t')
            {
                if (inCase)
                {
                    lst.Add((byte)(cur / 16));
                    inCase = false;
                }
                continue;
            }
            byte tmp = 0;
            if (num >= '0' && num <= '9')
                tmp = (byte)(num - '0');
            else if (num >= 'a' && num <= 'f')
                tmp = (byte)(num - 'a' + 10);
            else if (num >= 'A' && num <= 'F')
                tmp = (byte)(num - 'A' + 10);
            else
                throw new ArgumentException("需要传入一个正确的BCD字符串，BCD字符串中只能包含 0-9 A-F a-f 和空格，回车 制表符!");
            if (!inCase)
            {
                cur = (byte)(tmp * 16);
                inCase = true;
            }
            else
            {
                cur += tmp;
                inCase = false;
                lst.Add(cur);
            }
        }
        if (inCase)
        {
            lst.Add((byte)(cur / 16));
            inCase = false;
        }
        return lst.ToArray();
    }
    public static string ToBCDStringLower(byte[] buffer)
    {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < buffer.Length; i++)
        {
            sb.Append(buffer[i].ToString("x2"));
        }
        return sb.ToString();//result;
    }


}