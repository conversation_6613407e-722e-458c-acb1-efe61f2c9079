<%@ Master Language="C#" AutoEventWireup="true" CodeFile="MasterPage.master.cs" Inherits="MasterPage" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=no" />

    <title>
        <asp:ContentPlaceHolder ID="TitleContent" runat="server"><%=uConfig.stcdata("sitename") %></asp:ContentPlaceHolder>
    </title>
    <script src="js/jquery.min.js"></script>
    <link href="css/style.css" rel="stylesheet" />

    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <script src="js/bootstrap.min.js"></script>
    <script src="js/bootstrap-paginator.min.js"></script>
    <script src="js/momo.js"></script>

    <style>
        body, html, form, h1, h2, h3, h4, h5, h6, p {
            margin: 0px;
            padding: 0px;
        }

        body {
            background: #fff!important;
        }

        .icontainer {
            max-width: 1800px;
            margin: 0 auto;
        }


        @media screen and (max-width: 768px) {
            .open_menu {
                display: block!important;
            }

            .list-menu {
                display: none!important;
            }
        }


        .list-menu a {
            padding: 0 10px;
            cursor: pointer;
            color: #333;
        }

        .bottom-menu {
            display: flex;
            position: fixed;
            bottom: 0;
            width: 100%;
            /*border-top: 1px solid #ddd;*/
            background: #fff;
            display: none;
        }

            .bottom-menu > a {
                padding: 8px;
                padding-bottom: 2px;
                width: 100%;
                text-align: center;
                font-size: 13px;
                font-weight: 600;
                color: #000;
            }

                .bottom-menu > a.active {
                    color: #0092ff;
                }


                .bottom-menu > a svg path {
                    fill: currentColor;
                }

        .open_menu {
            display:none;
        }

        .table tr th,.table tr td {            
            white-space: nowrap;
        }
        
            .table-cell{
                position: sticky;
                left: 0;
                background:#fff;
            }

             .search_wrapper {
            background-color: #f5f5f5;
            border-radius: 20px;
            color: #999;
            height: 39px;
            padding: 0px 10px;
            display: flex;
            align-items: center;
        }


            .search_wrapper svg {
                margin-right: 5px;
            }


            .search_wrapper input {
                background: transparent;
                border: none;
                outline: none;
                color: #333;
                height: 39px;
                line-height: 39px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
                padding-right: 10px;
                font-size: 14px;
                font-weight: bold;
            }
    </style>

    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">

        <!--头部模块-->
        <div style="height:80px;"></div>
        <div style="background: #fff;position: fixed;top:0;width: 100%;z-index: 9;">
            <div class="icontainer" style="display: flex; padding: 20px; align-items: center;">
                <svg t="1690327981551" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1155" width="32" height="32">
                    <path d="M384.6656 518.2464s117.6576-290.56 371.5584-259.6352c61.9008 167.1168-156.3136 354.4576-255.488 377.6512-39.424-38.7072-116.0704-118.016-116.0704-118.016z" fill="#D9FFEC" p-id="1156"></path><path d="M591.872 933.0176c-7.3728 0-14.7968-1.1264-22.1184-3.3792-26.2656-8.2432-45.6704-30.1568-50.5344-57.2416l-18.9952-105.0624-23.4496 7.2192c-24.1152 7.424-50.2272 0.9728-68.096-16.896l-149.7088-149.7088a67.84 67.84 0 0 1-16.896-68.096l7.2192-23.4496-105.1136-18.9952c-27.0848-4.9152-49.0496-24.2688-57.2416-50.5344s-1.2288-54.6816 18.2272-74.1888l134.7584-134.7584c13.7216-13.7216 32.7168-21.6064 52.1216-21.6064h0.256c28.16 0.1024 98.6112 0.2048 173.0048-0.4096C660.992 37.8368 884.3264 90.3168 893.952 92.672c7.2704 1.792 13.8752 5.5296 19.1488 10.8032 7.0656 7.0656 12.6464 12.6464 17.4592 61.952 3.2768 33.792 2.9184 68.7104-1.024 103.68-8.3968 74.0864-37.7344 181.9136-128.768 282.112-0.6656 74.3936-0.512 144.8448-0.4096 173.0048 0.0512 19.7632-7.5776 38.4-21.6064 52.3776L643.9424 911.36a73.54368 73.54368 0 0 1-52.0704 21.6576z m-71.168-255.1808c10.1376 0 20.1728 2.7648 29.0304 8.1408 13.6192 8.2944 23.04 22.016 25.856 37.7344l21.504 118.7328 121.2928-121.2928c-0.1024-32.512-0.2048-108.9024 0.6144-186.624 0.1024-10.4448 4.1984-20.48 11.4688-28.0064 126.8736-131.4816 125.0816-279.6032 118.1696-338.4832-58.88-6.9632-207.0016-8.7552-338.4832 118.1184a40.89856 40.89856 0 0 1-28.0064 11.4688c-77.6704 0.8192-154.112 0.7168-186.624 0.6144L174.1312 419.5328l118.7328 21.4528c15.7184 2.8672 29.44 12.288 37.7344 25.9072 8.2944 13.6192 10.3424 30.1568 5.632 45.3632l-13.4144 43.6224 137.8304 137.8304 43.6224-13.4144c5.376-1.6384 10.9056-2.4576 16.4352-2.4576z m-67.9936 18.3296c-0.0512 0.0512-0.0512 0.0512 0 0z" fill="#34333A" p-id="1157"></path><path d="M194.1504 881.92c-17.2032 0-33.7408-6.7584-46.2848-19.2512a65.24928 65.24928 0 0 1-17.5616-60.9792l28.2624-124.16c5.3248-23.296 22.8352-41.8816 45.824-48.4864 22.9376-6.6048 47.7184-0.256 64.6144 16.64l95.8976 95.8976c16.896 16.896 23.296 41.6256 16.64 64.6144a65.6896 65.6896 0 0 1-48.4864 45.824l-124.16 28.2624c-4.9152 1.0752-9.8816 1.6384-14.7456 1.6384z m-3.4304-81.5616c-0.0512 0-0.0512 0 0 0z m41.1648-75.8784l-15.9744 70.144 70.144-15.9744-54.1696-54.1696z" fill="#3AD285" p-id="1158"></path><path d="M749.3632 269.056m-54.9888 0a54.9888 54.9888 0 1 0 109.9776 0 54.9888 54.9888 0 1 0-109.9776 0Z" fill="#45D18B" p-id="1159"></path></svg>
                <h2 style="margin-left: 10px; margin-right: 50px;"><%=uConfig.stcdata("sitename") %></h2>

                <div class="list-menu">

                    <a href="index.aspx">提交订单</a>
                    <a href="serverorders.aspx">我的订单</a>
                    <a href="transaction.aspx">交易明细</a>

                </div>

                <div class="open_menu" style="margin-left: auto;">
                    <svg t="1690822176022" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4705" width="25" height="25" onclick="openModal();">
                        <path d="M923.52 809.28V896h-832v-86.72h832zM247.04 454.4v86.656H91.52V454.4h155.52z m676.48 0v86.656H402.56V454.4h520.96zM620.288 128v86.72H91.52V128h528.768z m303.232 0v86.72h-209.92V128h209.92z" fill="#333333" p-id="4706"></path></svg>
                </div>

            </div>

        </div>




        <!--用户信息-->
        <div class="icontainer" style="color: #000; font-size: 12px; display: flex; padding: 10px; padding-right: 20px;">
            <div style="margin-left: auto; display: flex; align-items: center;">
                <span style="text-decoration: underline;"><%=uConfig.p_userNick %></span>&nbsp;&nbsp;
                <span style="color: #a39b8d; display: flex; align-items: center;">
                    <svg t="1690287699857" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3887" width="16" height="16">
                        <path d="M479.214202 911.847619L15.976107 463.238095c-17.066667-17.066667-21.942857-46.32381-7.314286-65.828571L196.395154 119.466667c9.752381-14.628571 26.819048-21.942857 41.447619-21.942857h548.571429c17.066667 0 31.695238 7.314286 41.447619 21.942857l187.733333 275.504762c14.628571 21.942857 12.190476 48.761905-7.314285 65.828571L547.480869 911.847619c-19.504762 19.504762-48.761905 19.504762-68.266667 0z" fill="#FFC600" p-id="3888"></path><path d="M479.214202 741.180952c2.438095 2.438095 7.314286 7.314286 12.190476 9.752381-4.87619-2.438095-9.752381-4.87619-12.190476-9.752381" fill="#FFE27F" p-id="3889"></path><path d="M513.347535 672.914286l-153.6-151.161905c-7.314286-7.314286-17.066667-9.752381-24.380952-9.752381-31.695238 0-48.761905 39.009524-24.380953 63.390476l170.666667 165.790476c2.438095 2.438095 7.314286 7.314286 12.190476 9.752381s9.752381 4.87619 12.190477 4.876191h9.75238c12.190476 0 24.380952-4.87619 34.133334-14.628572l170.666666-165.790476c24.380952-21.942857 7.314286-63.390476-24.380952-63.390476-9.752381 0-19.504762 4.87619-24.380952 9.752381L513.347535 672.914286z" fill="#FFFFFF" p-id="3890"></path></svg>&nbsp;
                    <span>余额：<%=Convert.ToDouble(uConfig.gd(dt,"score")).ToString("0.00") %></span>


                    <a href="out.aspx" style="display: inline-block; background: #000; color: #eee; padding: 3px 8px; font-size: 12px; border-radius: 3px; margin-left: 10px; cursor: pointer;">退出</a>
                </span>
            </div>
        </div>



        <!--公告-->
        <div class="icontainer">


            <!--公告-->

            <div style="margin: 10px;padding: 22px;border-radius: 5px;font-size: 13px;color: #333;box-shadow: 0 6px 0 0 rgb(0 0 0 / 1%), 0 15px 32px 0 #eda9c324;">
                <%=uConfig.stcdata("notify_business") %>
            </div>


            <!--内容模块-->

        </div>




        <div class="icontainer" style="padding-bottom: 100px;">
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
            </asp:ContentPlaceHolder>
        </div>


        <!--手机端菜单-->
        <%--<div class="bottom-menu">
            <a class="">
                <svg t="1690329754372" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2627" width="27" height="27">
                    <path d="M256 896a128 128 0 0 1-128-128v-321.408A128 128 0 0 1 177.408 345.6L407.210667 166.826667a170.666667 170.666667 0 0 1 209.578666 0l229.802667 178.773333A128 128 0 0 1 896 446.549333V768a128 128 0 0 1-128 128h-85.333333a85.333333 85.333333 0 0 1-85.333334-85.333333v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667h-85.333334a42.666667 42.666667 0 0 0-42.624 40.405334L426.666667 637.653333h-0.042667L426.666667 640a42.666667 42.666667 0 1 1-85.290667-2.346667A128 128 0 0 1 469.333333 512h85.333334a128 128 0 0 1 128 128v128a42.666667 42.666667 0 0 0 42.666666 42.666667h42.666667a42.666667 42.666667 0 0 0 42.666667-42.666667v-321.408a42.666667 42.666667 0 0 0-16.469334-33.706667L564.394667 234.24a85.333333 85.333333 0 0 0-104.789334 0L229.802667 412.928a42.666667 42.666667 0 0 0-16.469334 33.706667V768a42.666667 42.666667 0 0 0 42.666667 42.666667h64a21.333333 21.333333 0 0 0 21.333333-21.333334 42.666667 42.666667 0 1 1 85.333334 0v21.333334a85.333333 85.333333 0 0 1-85.333334 85.333333H256z" fill="#14101C" p-id="2628"></path></svg>
                <div>首页</div>
            </a>
            <a class="">
                <svg t="1690330516672" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2213" width="27" height="27">
                    <path d="M262.792195 358.772176l498.414587 0c22.469758 0 40.676421-18.212804 40.676421-40.676421s-18.206664-40.676421-40.676421-40.676421L262.792195 277.419334c-22.463618 0-40.676421 18.212804-40.676421 40.676421S240.328577 358.772176 262.792195 358.772176z" fill="#1A1A1A" p-id="2214"></path><path d="M262.792195 738.403426l213.893508 0.013303c22.456455 0 40.676421-18.212804 40.676421-40.676421s-18.206664-40.676421-40.676421-40.676421l-213.893508-0.013303c-22.463618 0-40.676421 18.212804-40.676421 40.676421C222.115774 720.190623 240.322437 738.403426 262.792195 738.403426z" fill="#1A1A1A" p-id="2215"></path><path d="M262.792195 548.594453l498.414587 0c22.469758 0 40.676421-18.212804 40.676421-40.676421s-18.206664-40.676421-40.676421-40.676421L262.792195 467.24161c-22.463618 0-40.676421 18.212804-40.676421 40.676421S240.328577 548.594453 262.792195 548.594453z" fill="#1A1A1A" p-id="2216"></path><path d="M764.927523 78.118172 259.071454 78.118172c-107.251699 0-194.509019 87.25732-194.509019 194.509019l0 478.719013c0 107.265002 87.25732 194.535625 194.509019 194.535625l215.615733 0c22.469758 0 40.676421-18.206664 40.676421-40.676421 0-22.469758-18.206664-40.676421-40.676421-40.676421L259.071454 864.528985c-62.398142 0-113.157199-50.7785-113.157199-113.183805L145.914254 272.627191c0-62.398142 50.759057-113.157199 113.157199-113.157199l505.85607 0c62.390979 0 113.157199 50.759057 113.157199 113.157199l0 340.053859-79.525218 0c-97.188506 0-176.263469 79.074963-176.263469 176.269609l0 111.661126c0 9.688663 3.52529 18.464537 9.178033 25.449625 7.113001 11.828396 19.944238 19.820418 34.755572 19.820418l44.462654 0c33.208333 0 83.629699-34.029024 149.847843-101.160981 65.621557-66.508763 98.896404-116.175953 98.896404-147.616012l0-424.477644C959.437565 165.375491 872.179222 78.118172 764.927523 78.118172zM708.243523 864.530009l-4.594645 0 0-75.579349c0-52.334949 42.582841-94.91779 94.910627-94.91779l79.468936 0C864.123756 731.62607 743.378741 852.994278 708.243523 864.530009z" fill="#1A1A1A" p-id="2217"></path></svg>

                <div>订单</div>

            </a>
            <a class="">
                <svg t="1690330445570" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1605" width="27" height="27">
                    <path d="M772.725116 65.804728 251.302513 65.804728c-102.279455 0-185.494715 83.21526-185.494715 185.494715l0 521.401114c0 102.286618 83.21526 185.494715 185.494715 185.494715l521.422603 0c102.272291 0 185.465039-83.208097 185.465039-185.494715L958.190155 251.299443C958.191178 149.019988 874.997408 65.804728 772.725116 65.804728zM869.048929 772.700557c0 53.131081-43.207058 96.352465-96.323812 96.352465L251.302513 869.053022c-53.131081 0-96.352465-43.221384-96.352465-96.352465L154.950048 251.299443c0-53.131081 43.221384-96.352465 96.352465-96.352465l521.422603 0c53.116755 0 96.323812 43.221384 96.323812 96.352465L869.048929 772.700557z" fill="#1A1A1A" p-id="1606"></path><path d="M649.40137 525.311167c24.62177 0 44.571125-19.956517 44.571125-44.571125 0-24.613584-19.949354-44.571125-44.571125-44.571125L581.170382 436.168917l79.141478-79.152734c17.410532-17.403369 17.410532-45.630247 0-63.033615-17.410532-17.396205-45.644573-17.403369-63.026452 0.007163L512.017396 379.271046l-85.296665-85.279269c-17.410532-17.396205-45.644573-17.403369-63.026452 0.007163-17.410532 17.410532-17.410532 45.630247 0 63.033615l79.154781 79.138408-68.228941 0c-24.62177 0-44.571125 19.956517-44.571125 44.571125 0 24.614607 19.949354 44.571125 44.571125 44.571125l92.827175 0 0 39.057551-92.827175 0c-24.62177 0-44.571125 19.956517-44.571125 44.571125 0 24.613584 19.949354 44.571125 44.571125 44.571125l92.827175 0 0 62.213947c0 24.613584 19.949354 44.571125 44.571125 44.571125 24.62177 0 44.571125-19.956517 44.571125-44.571125l0-62.213947 92.812849 0c24.62177 0 44.571125-19.956517 44.571125-44.571125 0-24.614607-19.949354-44.571125-44.571125-44.571125l-92.812849 0 0-39.057551L649.40137 525.313214z" fill="#1A1A1A" p-id="1607"></path></svg>

                <div>流水</div>

            </a>
        </div>--%>



        <style>
            /* 样式化蒙版 */
            .overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色 */
                opacity: 0; /* 初始时设置透明度为 0，隐藏蒙版 */
                transition: opacity 0.1s ease; /* 过渡效果，渐变显示和隐藏 */
                z-index: 9;
            }

            /* 样式化居中显示的 div 模块 */
            .modal-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, 0%);
                /*background-color: #fff;*/
                padding: 20px;
                border-radius: 5px;
                opacity: 0; /* 初始时设置透明度为 0，隐藏 modal 内容 */
                /*transform: translateY(-20px);*/ /* 初始时向上平移 20px，用于按钮滚出效果 */
                transition: opacity 0.3s ease, transform 0.3s ease; /* 过渡效果，渐变显示和平移动画 */
            }

            .modal-button {
                background-color: #dfdfdf;
                color: #000!important;
                width: 200px;
                text-align: center;
                padding: 14px 0;
                font-size: 18px;
                border-radius: 21px;
                font-weight: 600;
                box-shadow: 3px 5px 8px #000000a6;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 30px;
                text-decoration:none!important;
                outline:none;
                transition: all 0.3s ease; /* 过渡效果，渐变显示和平移动画 */
            }

            .modal-button:hover {
                    background-color: #d0e7e4;
            }

            .modal-container.open {
                opacity: 1; /* 设置透明度为 1，显示 modal 内容 */
                transform: translate(-50%, -50%); /* 平移动画还原为原位 */
            }
        </style>
        <!-- 蒙版 -->
        <div class="overlay" id="overlay" onclick="closeModal()">
            <div class="modal-container" onclick="event.stopPropagation();">

                <a class="modal-button" href="index.aspx">
                    <svg t="1690823374855" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="52846" width="32" height="32">
                        <path d="M717.7216 860.1088H392.96c-77.5168 0-140.544-63.0272-140.544-140.544V478.1568c0-39.2192 12.1344-76.6976 35.072-108.4928 0.8192-1.1264 1.6896-2.1504 2.6112-3.1744l140.0832-147.6608c17.152-22.784 42.1376-37.4272 70.5024-41.3696 29.0816-3.9936 57.8048 3.8912 80.7936 22.1696 45.312 36.0448 53.6576 100.6592 18.9952 147.0464l-28.416 37.9904 178.944-11.0592c43.5712-2.6624 84.992 14.4896 113.8688 47.104 28.8768 32.6144 40.9088 75.9296 32.9728 118.784-0.0512 0.2048-0.0512 0.3584-0.1024 0.512l-41.8304 205.4144c-12.4928 66.4576-70.5536 114.688-138.1888 114.688zM336.1792 407.1936a123.14112 123.14112 0 0 0-22.3232 70.912v241.408c0 43.6224 35.4816 79.104 79.104 79.104h324.7616c38.144 0 70.8096-27.1872 77.7728-64.7168 0.0512-0.2048 0.0512-0.3584 0.1024-0.512l41.8304-205.3632c4.352-24.0128-2.4064-48.2816-18.5856-66.6112a78.464 78.464 0 0 0-64.1024-26.5216l-222.6688 13.7728c-16.128 1.024-31.0272-7.2192-38.7584-21.504s-6.4512-31.232 3.2256-44.2368l54.6304-73.0624a44.71808 44.71808 0 0 0-8.0384-62.1568 44.6464 44.6464 0 0 0-34.1504-9.3696 44.7488 44.7488 0 0 0-30.3616 18.2272c-0.8192 1.1264-1.6896 2.1504-2.6112 3.1744l-139.8272 147.456z" fill="#474747" p-id="52847"></path><path d="M166.9632 839.1168c-16.9472 0-30.72-13.7728-30.72-30.72V418.4064c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v389.9904c0 16.9984-13.7728 30.72-30.72 30.72z" fill="#45CFFF" p-id="52848"></path></svg>&nbsp;提交订单</a>



                <a class="modal-button" href="serverorders.aspx">
                    <svg t="1690823527941" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="53077" width="32" height="32">
                        <path d="M576.256 863.4368H309.0432c-73.4208 0-133.12-59.6992-133.12-133.12V392.4992c0-119.0912 96.9216-216.0128 216.0128-216.0128h267.2128c73.4208 0 133.12 59.6992 133.12 133.12v337.8176c0 119.0912-96.8704 216.0128-216.0128 216.0128z m-184.32-625.5104c-85.248 0-154.5728 69.3248-154.5728 154.5728v337.8176c0 39.5264 32.1536 71.68 71.68 71.68h267.2128c85.248 0 154.5728-69.3248 154.5728-154.5728V309.6064c0-39.5264-32.1536-71.68-71.68-71.68H391.936z" fill="#474747" p-id="53078"></path><path d="M590.2336 628.992h-207.872c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72h207.872c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72zM517.12 481.7408H382.3616c-16.9472 0-30.72-13.7728-30.72-30.72s13.7728-30.72 30.72-30.72H517.12c16.9472 0 30.72 13.7728 30.72 30.72s-13.7728 30.72-30.72 30.72z" fill="#45CFFF" p-id="53079"></path></svg>&nbsp;我的订单</a>

                <a class="modal-button" href="transaction.aspx">
                    <svg t="1690823551677" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="53370" width="32" height="32">
                        <path d="M732.4672 839.3216H312.832c-85.4528 0-154.9824-74.8544-154.9824-166.912v-57.088c0-16.9472 13.7728-30.72 30.72-30.72 29.184 0 52.8896-27.2896 52.8896-60.8768 0-33.5872-23.7056-60.8768-52.8896-60.8768-16.9472 0-30.72-13.7728-30.72-30.72V375.0912c0-92.0064 69.5296-166.912 154.9824-166.912h419.6352c85.4528 0 154.9824 74.8544 154.9824 166.912V432.128c0 16.9472-13.7728 30.72-30.72 30.72-29.184 0-52.8896 27.2896-52.8896 60.8768 0 33.5872 23.7056 60.8768 52.8896 60.8768 16.9472 0 30.72 13.7728 30.72 30.72v57.088c0 92.0576-69.5296 166.912-154.9824 166.912zM219.2384 641.5872v30.8224c0 58.1632 41.984 105.472 93.5424 105.472h419.6352c51.6096 0 93.5424-47.3088 93.5424-105.472v-30.8224C777.8816 627.2 742.4 579.84 742.4 523.776c0-56.064 35.4304-103.424 83.6096-117.8624v-30.8224c0-58.1632-41.984-105.472-93.5424-105.472H312.832c-51.6096 0-93.5424 47.3088-93.5424 105.472v30.8224c48.1792 14.3872 83.6096 61.7984 83.6096 117.8624-0.0512 56.064-35.4816 103.424-83.6608 117.8112z" fill="#474747" p-id="53371"></path><path d="M444.5184 418.3552c-16.9472 0-30.72-13.7728-30.72-30.72v-51.2c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v51.2c0 16.9472-13.7216 30.72-30.72 30.72zM444.5184 587.6736c-16.9472 0-30.72-13.7728-30.72-30.72V489.216c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v67.7376c0 16.9472-13.7216 30.72-30.72 30.72zM444.5184 740.5056c-16.9472 0-30.72-13.7728-30.72-30.72v-51.2c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v51.2c0 16.9472-13.7216 30.72-30.72 30.72z" fill="#45CFFF" p-id="53372"></path></svg>&nbsp;交易明细</a>


            </div>
        </div>

        <script>
            function openModal() {
                // 获取蒙版元素
                var overlay = document.getElementById('overlay');
                var modal = document.querySelector('.modal-container');

                // 显示蒙版
                overlay.style.display = 'block';

                // 设置延时，以便动画效果生效
                setTimeout(function () {
                    overlay.style.opacity = 1; // 将透明度设置为 1，显示蒙版
                    modal.classList.add('open'); // 添加 open 类，触发按钮向下滚出效果
                }, 10);
            }

            function closeModal() {
                // 获取蒙版元素
                var overlay = document.getElementById('overlay');
                $('.modal-container').removeClass("open");

                overlay.style.opacity = 0;

                // 设置延时，以便动画效果生效
                setTimeout(function () {
                    // 设置透明度为 0，隐藏蒙版
                    overlay.style.display = 'none'; // 隐藏蒙版
                }, 100); // 等待动画结束后再隐藏蒙版，300 毫秒对应 transition 的过渡时间
            }
        </script>

    </form>
</body>
</html>
