<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="index.aspx.cs" Inherits="index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    首页 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="" style="margin-bottom: 0px;">
        <div style="padding: 20px 10px;">

            <div style="margin-bottom: 10px; display: flex; overflow: auto; padding: 20px 0;" class="classtype1">

                <a class="classtype_active" type="tk">
                    <img src="../static/images/b/tiktok.JPG" style="height: 30px;" />
                </a>
                <a type="ins">
                    <img src="../static/images/b/INS.JPG" style="height: 30px;" />
                </a>
                <a type="fb">
                    <img src="../static/images/b/FB.JPG" style="height: 30px;" />
                </a>
                <a type="ytb">                   
                    <img src="../static/images/b/youtube.JPG" style="height: 30px;" />
                </a>
                
                <a type="twitch">
                    <img src="../static/images/b/twitch.png" style="height: 30px;" />
                </a>
                
                <a type="shopee">
                    <img src="../static/images/b/shopee.png" style="height: 30px;" />
                </a>
                <a type="tw">
                        <img src="../static/images/b/twitter.JPG" style="height: 30px;" />
                    </a>
                <%--<a type="tg">
                        <img src="../static/images/b/telegram.PNG" style="height: 30px;" />
                    </a>--%>
                <a type="bigo">
                    <img src="../static/images/b/bigo.png" style="height: 30px;" />
                    </a>
                <a type="steam">
                    <img src="../static/images/b/steam.png" style="height: 30px;" />
                </a>

                <a type="lazada">
                    <img src="../static/images/b/lazada.png" style="height: 30px;" />
                </a>


            </div>

            <script id="order_business" type="text/ecmascript">
                <%=order_business %>
            </script>

            <style>
                .classtype1 a {
                    display: inline-block;
                    /*border: 2px solid #eee;*/
                    padding: 12px;
                    background: #fff;
                    cursor: pointer;
                    border-radius: 8px;
                    margin: 0 10px;
                    display: flex;
                    align-items: center;
                }

                    .classtype1 a.classtype_active {
                        box-shadow: 0 7px 6px rgb(50 147 231 / 47%) !important;
                    }

                    .classtype1 a:hover {
                        transition: all 0.3s;
                        transform: translateY(-4px);
                        box-shadow: 0 7px 6px rgb(195 64 106 / 47%) !important;
                    }

                .classtype2 a {
                    position: relative;
                    display: inline-block;
                    background: #fff;
                    /*border: 1px solid slategrey;*/
                    color: slategrey;
                    font-weight: bold;
                    padding: 12px;
                    border-radius: 5px;
                    cursor: pointer;
                    text-decoration: none;
                }

                    .classtype2 a.classtype_active {
                        color: #000;
                    }

                        .classtype2 a.classtype_active::after {
                            content: "";
                            position: absolute;
                            bottom: 5px;
                            left: 25%;
                            width: 50%;
                            height: 8px;
                            background-color: yellow; /* 横条的颜色 */
                            border-radius: 18px;
                        }
            </style>

            <div style="margin-bottom: 10px;" class="classtype2">
                <a type="" class="classtype_active">全部</a>
<%--                
                <a type="like">点赞</a>
                <a type="comment">评论</a>--%>
                <a type="online">人气</a>
                <a type="fans">粉丝</a>
                <a type="view">播放</a>

                <%--<a style="display:inline-block;background:#dc5454;color:#eee;font-weight:bold;padding:12px;border-radius:5px;cursor:pointer;text-decoration:none;">YouTube</a>--%>
            </div>

            <script>
                $('.classtype1 a').on('click', function () {
                    $(this).addClass('classtype_active').siblings().removeClass('classtype_active');

                    $(".classtype2 a").eq(0).addClass('classtype_active').siblings().removeClass('classtype_active');
                    refresh_classtype();
                })
                $('.classtype2 a').on('click', function () {
                    $(this).addClass('classtype_active').siblings().removeClass('classtype_active');


                    refresh_classtype();
                })

                function refresh_classtype() {
                    var obj = JSON.parse($('#order_business').html());
                    $('#orderTypes').html('<option value="">请选择业务</option>');

                    var type1 = $('.classtype1 a.classtype_active').attr("type");
                    var type2 = $('.classtype2 a.classtype_active').attr("type");

                    for (var i = 0; i < obj.length; i++) {
                        if (obj[i].typename == type1 && (type2 == "" || type2 == obj[i].type2)) {
                            var pretext = '★★★推荐★★★ | ';
                            pretext = "";
                            $('#orderTypes').append('<option value="' + obj[i].id + '/' + obj[i].param + '">' + pretext + obj[i].appname + '</option>');
                        }
                    }

                    $("#orderTypes").change();
                }



            </script>


            <div class="ccvoYb">
                <select id="orderTypes" class="w_input">
                    <asp:Repeater ID="items" runat="server">
                        <ItemTemplate>
                            <option value="<%#Eval("id") %>/<%#Eval("param") %>">★★★推荐★★★ | <%#Eval("appname") %></option>
                        </ItemTemplate>
                    </asp:Repeater>
                </select>
            </div>

            <script>

                refresh_classtype();
            </script>

            <div class="ccvoYb">
                <textarea id="orderUrls" class="w_input" placeholder="链接信息，批量下单格式（一行代表一个订单）：链接,数量" autocomplete="off" style="height: 200px;"></textarea>
            </div>

            <div class="ccvoYb hInput" style="display: none;">
                <input id="orderNote" class="w_input" placeholder="订单备注" autocomplete="off">
            </div>

            <div class="ccvoYb hInput" style="display: none;">
                <textarea id="orderComments" class="w_input" placeholder="评论内容" autocomplete="off" style="height: 200px;"></textarea>
            </div>

            <div class="ccvoYb">
                <input id="orderTotal" class="w_input" style="background: #e0e4e7; color: #091f33;" disabled="disabled" value="" autocomplete="off">
            </div>


            <div class="ccvoYb">
                <input id="orderNum" class="w_input" placeholder="订单数量" autocomplete="off">
            </div>

            <div class="ccvoYb hInput" style="display: none;">
                <input id="orderTime" class="w_input" placeholder="直播时长（小时）" autocomplete="off">
            </div>


            <div style="margin-top: 20px; display: flex; justify-content: center;">
                <a class="gb_9d" style="width: 250px; text-align: center; cursor: pointer; display: block; font-size: 16px; padding: 13px 0;" id="orderTask_Btn">提交订单</a>
                <div style="clear: both;"></div>
            </div>

        </div>
        <div style="text-align: center; color: #666; font-size: 13px; padding: 2px 0;">
            <div id="OrderText">
            </div>
        </div>
    </div>
    <script>
        var kData = {};
        var kTask = new Array();

        var r = function (type, data, callback) {
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '?do=' + type,
                data: data,
                success: function (result) {
                    callback(result)
                }
            });
        }
        $("#orderTypes").on('change', function () {
            var typeid = $(this).val();
            if (typeid == null || typeid == "") {
                $('#orderTotal').val('请选择业务');
                return;
            }
            var param = typeid.split('/')[1];
            typeid = typeid.split('/')[0];

            $('#orderUrls').val('');
            $('#orderNum').val('');
            $('.hInput').hide();
            $('.hInput').find('.w_input').val('');

            //console.log('check_param', param);
            switch (param) {
                case "customTimes":
                    $('#orderTime').closest('.ccvoYb').show();
                    break;
                case "comment_list":
                    $('#orderNote').closest('.ccvoYb').show();
                    break;
                case "custom-comment":
                    $('#orderComments').closest('.ccvoYb').show();
                    break;
                default:
                    break;
            }
            r('json_info', { typeid: typeid }, function (result) {
                //console.log("result", result);
                if (result.code == 1) {
                    $('#OrderText').text(result.tixin);
                    kData.appid = typeid;
                    kData.dianshu = parseFloat(result.dianshu);
                    $('#orderTotal').val('单价' + kData.dianshu + '点，累计消费' + kData.dianshu + '点');
                }
            })
        })

        //去除数组重复
        function unique(arr) {
            var result = [], hash = {};
            for (var i = 0, elem; (elem = arr[i]) != null; i++) {
                if (!hash[elem]) {
                    result.push(elem);
                    hash[elem] = true;
                }
            }
            return result;
        }

        $('#orderComments').on('keyup', function () {
            var comment_data = $(this).val();
            var arr = comment_data.split('\n');
            arr = unique(arr);
            //console.log('comment_data', arr);
            var new_comment_data = arr.join('\n');
            $('#orderNum').val(arr.length);
            check_total_dianshu();
            if (new_comment_data != comment_data) {
                $('#orderComments').val(new_comment_data);
            }
        });

        $("#orderUrls").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderNum").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderTime").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderTask_Btn").on('click', function () {
            if ($(this).hasClass("submit_status")) {
                return;
            }
            $(this).addClass("submit_status");
            check_total_dianshu();
            pushTask();
        })

        var check_total_dianshu = function () {
            kTask = [];
            var taskNum = $("#orderNum").val();
            var taskID = $("#orderUrls").val();
            if (taskID.indexOf("    ") != -1 || taskID.indexOf("----") != -1 || taskID.indexOf(",") != -1 || taskID.indexOf("，") != -1) {
                var arr = taskID.split('\n');
                taskNum = 0;
                for (var i = 0; i < arr.length; i++) {
                    var arr2;
                    if (arr[i].indexOf("----") != -1) {
                        arr2 = arr[i].split("----");
                    } else if (arr[i].indexOf(",") != -1) {
                        arr2 = arr[i].split(",");
                    } else if (arr[i].indexOf("，") != -1) {
                        arr2 = arr[i].split("，");
                    } else {
                        arr2 = arr[i].split("    ");
                    }
                    if (arr2.length == 2) {
                        taskNum += parseInt(arr2[1]);

                        kTask.push({
                            id: arr2[0],
                            num: parseInt(arr2[1])
                        })
                    }
                }
            } else {
                if (taskNum == "" || isNaN(taskNum) || parseInt(taskNum) < 0) {
                    taskNum = "0";
                }

                kTask.push({
                    id: taskID,
                    num: parseInt(taskNum)
                })
            }
            taskInfo = new Array();

            var total_fee = (kData.dianshu * parseInt(taskNum)).toFixed(4);

            var customTimes = $('#orderTime').val();
            if (customTimes != "") {
                total_fee = parseFloat(total_fee) * parseInt(customTimes);
            }

            $('#orderTotal').val('单价' + kData.dianshu + '点，累计消费' + parseFloat(total_fee) + '点');

            //console.log("kTask", JSON.stringify(kTask));
        }

        $("#orderTypes").trigger('change');

        var taskInfo = new Array();
        var _task;
        var pushTask = function () {
            _task = kTask.shift();
            if (typeof (_task) == "undefined") {
                var task_result = "";
                for (var i = 0; i < taskInfo.length; i++) {
                    task_result += '链接:' + taskInfo[i].task.id + ' 数量:' + taskInfo[i].task.num + ' 下单结果:' + taskInfo[i].result + '\n';
                }
                alert(task_result);
                $("#orderTask_Btn").removeClass("submit_status");

                $("#orderTypes").trigger('change');
                return;
            }
            var typeid = $("#orderTypes").val();
            var param = typeid.split('/')[1];
            typeid = typeid.split('/')[0];

            var task_param = "";
            switch (param) {
                case "customTimes":
                    task_param = $('#orderTime').val();
                    break;
                case "comment_list":
                    task_param = $('#orderNote').val();
                    break;
                case "custom-comment":
                    task_param = $('#orderComments').val();
                    break;
                default:
                    break;
            }

            $.ajax({
                type: 'post',
                url: '?do=orderTask',
                dataType: "json",
                data: { typeid: kData.appid, task_id: _task.id, task_num: _task.num, task_param: task_param },
                success: function (result) {
                    console.log(result, 'login', result.code);
                    taskInfo.push({ task: _task, result: (result.code == 1 ? '成功' : result.msg) });
                    pushTask();
                }, error: function (ex) {
                    taskInfo.push({ task: _task, result: '异常' });
                }
            });
        }
        var taskFinish = function () { }
    </script>

    <style>
        .submit_status {
            background: silver;
            color: gray!important;
            -webkit-touch-callout: none; /* iOS Safari */
            -webkit-user-select: none; /* Chrome/Safari/Opera */
            -khtml-user-select: none; /* Konqueror */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently

not supported by any browser */
        }

            .submit_status:hover {
                background: silver!important;
                color: gray!important;
                box-shadow: none!important;
            }
    </style>
</asp:Content>

