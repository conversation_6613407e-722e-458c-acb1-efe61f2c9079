body, html {
    margin: 0px;
    padding: 0px;
}

body {
    background: #f1f1f1!important;
}



/*我的 样式*/
.gb_9d {
    font-family: Google Sans,Roboto,RobotoDraft,Helvetica,Arial,sans-serif;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.25px;
    line-height: 16px;
    min-width: 96px;
    padding: 9px 23px;
    text-align: center;
    vertical-align: middle;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #1a73e8;
    border: 1px solid transparent;
    color: #fff!important;
    cursor: pointer;
    outline: none;
    text-decoration: none!important;
    display: inline-block;
}

    .gb_9d.gbs {
        font-size: 12px;
        padding: 2px 5px;
        margin: 0 3px;
        min-width:0px;
    }

    .gb_9d:hover {
        background: #2b7de9;
        -webkit-box-shadow: 0 1px 2px 0 rgba(66,133,244,0.3), 0 1px 3px 1px rgba(66,133,244,0.15);
        box-shadow: 0 1px 2px 0 rgba(66,133,244,0.3), 0 1px 3px 1px rgba(66,133,244,0.15);
    }

    .gb_9d.gb_error {
        background: #e81a53;
    }

.ccvoYb {
    border-top: 0;
    background: #fbfbfb;
    margin-bottom: 30px;
    border-radius:3px;
        background: #f8f8f8;
        box-shadow: 0 7px 6px rgb(155 176 193 / 39%) !important;
}

.w_input {
    width: 100%;
    height: 100%;
    border: 0px;
    outline: none;
    font-size: 13px;
    color: #333;
    line-height: 32px;
    padding: 10px;
    max-width: 100%;
    min-width: 100%;
    box-sizing: border-box;
    font-weight: 100;    background: #f8f8f8;
        border-radius: 3px;
}


.notice-title {
    font-weight: bold;
    margin-bottom: .3rem;
}

.notice-content p {
    margin: 0;
}

        #task .ccvoYb {
            margin-bottom: 10px;
            font-size: 13px;
        }

        #task .w_input {
            font-size: 13px;
            line-height: 20px;
        }


        .document_content {
            display: table-cell;
            background: #fff;
            border: 1px solid #eee;
            border-left: none;
            overflow: hidden;
        }

            .document_content .prettyprint {
                max-width: 98%;
            }

            .document_content .crumbs {
                height: 60px;
                line-height: 60px;
                padding: 0 28px;
            }

                .document_content .crumbs span, .document_content .crumbs a {
                    color: #3e7af6;
                }

            .document_content .document_detail {
                padding: 10px 28px 20px;
            }

                .document_content .document_detail .title {
                    font-size: 24px;
                    margin-bottom: 25px;
                }

                .document_content .document_detail .paragraph {
                    margin-top: 20px;
                }

                    .document_content .document_detail .paragraph p {
                        line-height: 28px;
                        color: #666;
                    }


                    
/*button*/
.moButton {
    padding: 10px 28px;
    font-weight: bold;
    font-size: 12px;
    background: #3C89FF;
    color: #fff;
    border: 0 solid transparent;
    border-radius: 3px;
    cursor: pointer;
    outline: none;
    margin-bottom: 10px;
    text-decoration: none!important;
    display: inline-block;
    box-sizing: border-box;
    transition:all 0.2s;
}

.moButton:hover {
    color: #fff;
    background: #357dec;
}

    .moButton.disable {
        background: #bbbbbb;
        color: rgb(233 237 243);
    }

.moButtonAction {
    position: fixed;
    left: calc(50% - 100px)!important;
    bottom: 0px;
    text-align: center;
    left: 0px;
    padding-bottom: 20px;
}

    .moButtonAction > a {
        margin-bottom: 0px;
        position: relative;
        width: 200px;
        opacity: 0.9;
    }

.moButton.momo-button-gray {
        background: rgb(242, 243, 245)!important;
    color: #000!important;
}
.moButton.momo-button-gray:hover {
            background: rgb(229, 230, 235)!important;
}


                    
/*Modal 模態框*/

        .momo-modal-mask,.momo-modal-dialog  {
            position:fixed;
            height:100%;
            width:100%;
            left:0;
            top:0;
        }

        .momo-modal-mask {
            background:rgba(29, 33, 41, 0.6);
            z-index:1001;
        }

        .momo-modal-dialog {
            z-index:1002;
            display:flex;
            justify-content: center;/*水平主轴居中*/
            align-items: center;/*垂直交叉轴居中*/            
        }

        .momo-modal-simple {
            background:#fff;
            border-radius:5px;
            padding:22px 30px;
            max-width:700px;
            margin:10px;
            min-width: 200px;
            position:relative;
        }
        

        .momo-modal-title {
            text-align:left;
            margin-bottom: 24px;
        }

        
        .momo-modal-title svg {
            color:rgb(22, 93, 255);
            position:relative;
            top:5px;
            width:1.5em;
            height:1.5em;
            margin-right: 8px;
        }

        .momo-modal-error svg {
            color:rgb(213, 73, 65);
        }
        .momo-modal-warning svg {
            color:rgb(227, 115, 24);
        }
        .momo-modal-success svg {
            color:rgb(43, 164, 113);
        }

        .momo-modal-content {
            margin:10px 0;
            font-size:14px;
            line-height:20px;
            text-align:center;
        }
        .momo-modal-buttom {
            text-align:center;
                margin-top: 32px;
        }
        .momo-modal-buttom .moButton {
            margin-bottom:0px;margin:0 5px;
        }