*{
    margin: 0;
    padding: 0;
    list-style: none;
}

.hide{
    display: none !important;
}


[name="datePicker"] .is-disabled{
    cursor: not-allowed;
}
[name="datePicker"] .datePicker-title-text{
    cursor: pointer;
}
[name="datePicker"] .datePicker-title-text:hover{
    color: #409eff;
}
[name="datePicker"] .datePicker-title-text.is-double{
    cursor: default;
}
[name="datePicker"] .datePicker-title-text.is-double:hover{
    color: #606266;
}
[name="datePicker"][reporttimetype="7"] .calendarBody-header-left-prevMonth, [name="datePicker"][reporttimetype="7"] .calendarBody-header-right-nextMonth, [name="datePicker"][reporttimetype="8"] .calendarBody-header-left-prevMonth, [name="datePicker"][reporttimetype="8"] .calendarBody-header-right-nextMonth, [name="datePicker"][reporttimetype="9"] .calendarBody-header-left-prevMonth, [name="datePicker"][reporttimetype="9"] .calendarBody-header-right-nextMonth{
    display: none;
}
[name="datePicker"][reporttimetype="7"] .calendarBody-content-body-row-date{
    line-height: 40px;
    margin: 8px 0;
}
[name="datePicker"][reporttimetype="7"] .calendarBody-content-body-row-date > span{
    width: 40px;
    line-height: 30px;
}
[name="datePicker"][reporttimetype="8"] .calendarBody-content-body-row-date{
    line-height: 60px;
    margin: 10px 0;
}
[name="datePicker"][reporttimetype="8"] .calendarBody-content-body-row-date > span{
    width: 60px;
    line-height: 50px;
    font-size: 14px;
}
[name="datePicker"][reporttimetype="9"] .calendarBody-content-body-row-date{
    line-height: 40px;
    margin: 8px 0;
}
[name="datePicker"][reporttimetype="9"] .calendarBody-content-body-row-date > span{
    width: 50px;
    line-height: 30px;
    font-size: 14px;
}

[name="datePicker"]{
    max-width: 300px;
    margin: 10px auto;
}
[name="datePicker"].is-fast{
    max-width: 400px;
}   
[name="datePicker"].is-double{
    max-width: 650px;
    margin: 10px auto;
}
[name='datePicker'][reporttimetype="4"] .datePicker-content-calendar-body{
    border-top: 1px solid #e4e7ed;  
}
[name='datePicker'][reporttimetype="4"] .datePicker-bottom{
    border: 1px solid #e4e4e4;
    border-top: none;
}
[name="datePicker"] .datePicker-content-fast{
    display: none;
}
[name="datePicker"].is-fast .datePicker-content-fast{
    display: block;
}
.datePicker-content-calendar-hours.hide{
    border: none;
}
[name="datePicker"] .datePicker-content-calendar-body, [name="datePicker"] .datePicker-content-calendar-hours{
    border-left: none;
}
[name="datePicker"].is-fast .datePicker-content-calendar-body, [name="datePicker"].is-fast .datePicker-content-calendar-hours{
    border-left: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
}
[name="datePicker"][reporttimetype="4"] .datePicker-content-calendar-hours, [name="datePicker"][reporttimetype="4"] .datePicker-content-calendar-body{
    border-bottom: none;
}
.datePicker-content-calendar-body{
    flex-direction: column;
}
[name="datePicker"].is-double .datePicker-content-calendar-body{
    flex-direction: row;
}

[name="datePicker"] *{
    -webkit-user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
}


/*日历主体样式*/
.datePicker-content{
    display: flex;
    border: 1px solid #e4e7ed;
}
.datePicker-content-fast{
    width: 100px;
}
.datePicker-content-fast-ul{
    padding: 10px 6px 0 12px;
    box-sizing: border-box;
}
.datePicker-content-fast-ul-li{
    font-size: 14px;
    line-height: 28px;
    color: #606266;
    cursor: pointer;
}
.datePicker-content-fast-ul-li:hover{
    color: #409eff;
}
.datePicker-content-calendar{
    flex: 1;
}
.datePicker-content-calendar-body{
    color: #606266;
    display: flex;
    border: 1px solid #e4e7ed;
    border-right: none;
    border-bottom: none;
    border-top: none;
}

.datePicker-content-calendar-body-left, .datePicker-content-calendar-body-right{
    flex: 1;
    padding: 16px;
}

.datePicker-content-calendar-body-left{
    border-right: 1px solid #e4e4e4;
}

/*日历头部样式*/

.calendarBody-header{
    line-height: 30px;
    display: flex;
    justify-content: space-around;
}
.calendarBody-header .ali-iconfont{
    font-size: 12px;
    margin: 0 6px;
    cursor: pointer;
}
.calendarBody-header .ali-iconfont.is-disabled, .calendarBody-header .ali-iconfont.is-disabled:hover{
    cursor: not-allowed;
    color: #bbb;
}
.calendarBody-header .ali-iconfont:hover{
    color: #409eff;
}

.calendarBody-header-content{
    width: 70%;
    text-align: center;
}
/*日历头部样式结束*/

.calendarBody-content{
    text-align: center;
}
.calendarBody-content-title{
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 8px;
    cursor: default;
}
.calendarBody-content-title-text{
    flex: 1;
    width: 30px;
    line-height: 30px;
    font-size: 12px;
}

.calendarBody-content-body{
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.calendarBody-content-body-row{
    display: flex;
    justify-content: center;
}
.calendarBody-content-body-row[title]:hover > div{
    background-color: #f2f6fc;
}
.calendarBody-content-body-row[title]:hover > div:not(.active):not(.end-date):not(.disabled):first-child{
    border-radius: 15px 0 0 15px;
}
.calendarBody-content-body-row[title]:hover > div:not(.active):not(.start-date):not(.disabled):last-child{
    border-radius: 0 15px 15px 0;
}
.calendarBody-content-body-row-date {
    flex: 1;
    width: 30px;
    line-height: 30px;
    font-size: 12px;
    cursor: pointer;
}
.calendarBody-content-body-row-date.disabled {
    background-color: #f5f7fa;
}
.calendarBody-content-body-row-date.disabled > span, .calendarBody-content-body-row-date.disabled:hover > span{
    cursor: not-allowed;
    color: #c0c4cc;
}
.calendarBody-content-body-row-date.active, .calendarBody-content-body-row-date.start-date, .calendarBody-content-body-row-date.end-date{
    background-color: #f2f6fc;
}
.calendarBody-content-body-row-date.start-date{
    border-radius: 15px 0 0 15px;
}
.calendarBody-content-body-row-date.end-date{
    border-radius: 0 15px 15px 0;
}

.calendarBody-content-body-row-date.current > .prev-month, .calendarBody-content-body-row-date.current > .next-month{
    background-color: unset;
}
.calendarBody-content-body-row-date.current:hover > .prev-month, .calendarBody-content-body-row-date.current:hover > .next-month{
    color: #c0c4cc;
}
.calendarBody-content-body-row-date.current > span, .calendarBody-content-body-row-date.today.current > span{
    color: #fff;
    background-color: #409eff;
}
.calendarBody-content-body-row-date.current:hover > span{
    color: #FFF;
}

.calendarBody-content-body-row-date.start-date.end-date{
    border-radius: 50%;
}

.calendarBody-content-body-row-date > span.prev-month, .calendarBody-content-body-row-date > span.next-month{
    color: #c0c4cc;
}
.calendarBody-content-body-row-date:hover > .available, .calendarBody-content-body-row-date.today > .available{
    color:#409eff;
}

.calendarBody-content-body-row[title] .calendarBody-content-body-row-date.start-date > .prev-month, .calendarBody-content-body-row[title] .calendarBody-content-body-row-date.end-date > .prev-month, .calendarBody-content-body-row[title] .calendarBody-content-body-row-date.start-date > .next-month, .calendarBody-content-body-row[title] .calendarBody-content-body-row-date.end-date > .next-month{
    background-color: #409eff;
    color: #FFF;
}

.calendarBody-content-body-row-date.start-date > .available, .calendarBody-content-body-row-date.end-date > .available{
    background-color: #409eff;
    color: #FFF;
}

.calendarBody-content-body-row-date > span{
    display: inline-block;
    width: 24px;
    line-height: 24px;
    border-radius: 50%;
}
/*日历主体样式结束*/

/* 日历小时样式 */

.datePicker-content-calendar-hours{
    display: flex;
    height: 40px;
    line-height: 40px;
    border-left: 1px solid #e4e7ed;
    justify-content: center;
    align-items: center;
}
.is-double .datePicker-content-calendar-hours > div{
    padding: 0;
}

.datePicker-content-calendar-hours-left, .datePicker-content-calendar-hours-right{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 5px;
    width: 49%;
}
.datePicker-content-calendar-hours-content{
    flex: 1;
    font-size: 14px;
}
.datePicker-content-calendar-hours .calendarHour-day, .datePicker-content-calendar-hours .calendarHour-hour{
    flex: 1;
    padding: 0 5px;
    height: 28px;
    line-height: 28px;
}

.datePicker-content-calendar-hours > div > div > input{
    width: 100%;
    height: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    color: #606266;
    transition: border-color .2s ;
}
.datePicker-content-calendar-hours > div > div > input:hover{
    border-color: #c0c4cc;
}
.datePicker-content-calendar-hours > div > div > input:focus{
    outline: none;
    border-color: #409eff;
}
.datePicker-content-calendar-hours-left, .datePicker-content-calendar-hours-right{
    display: flex;
}

.calendarHour-day-input, .calendarHour-hour-input{
    padding: 0 10px;
    box-sizing: border-box;
    width: 100%;
    line-height: 24px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    color: #606266;
    transition: border-color .2s;
}
.calendarHour-day-input:hover, .calendarHour-hour-input:hover{
    border-color: #c0c4cc;
}
.calendarHour-day-input:focus, .calendarHour-hour-input:focus{
    outline: none;
    border-color: #409eff;
}
.calendarHour-hour-pull{
    background-color: #FFF;
    border: 1px solid #e4e4e4;
    position: relative;
    margin-top: 5px;
    border-top: none;
}
.calendarHour-hour-pull-content{
    display: flex;
    text-align: center;
    height: 150px;
    overflow: hidden;
}
.calendarHour-hour-pull-content > div{
    flex: 1;
}

.calendarHour-hour-pull-content > div > ul {
    overflow-y: auto;
    height: 100%;
}
.calendarHour-hour-pull-content > div > ul::before, .calendarHour-hour-pull-content > div > ul::after{
    content: '';
    display: block;
    clear: both;
    height: 50%;
}
.calendarHour-hour-pull-content > div > ul::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
.calendarHour-hour-pull-content > div > ul > li{
    color: #606266;
    font-size: 12px;
    cursor: pointer;
}
.calendarHour-hour-pull-content > div > ul > li:not(.active):hover{
    background-color: #f5f7fa;
}
.calendarHour-hour-pull-content > div > ul > li.active{
    border-top: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    color: #303133;
    font-weight: 700;
    cursor: default;
}
.calendarHour-hour-pull-footer{
    border-top: 1px solid #e4e4e4;
    text-align: right;
    padding-right: 5px;
}
.calendarHour-hour-pull-footer span{
    font-size: 12px;
    margin: 0 5px;
    cursor: pointer;
}
.calendarHour-hour-pull-footer-confirm{
    color: #409eff;
}

/* 快速选择DOM结构 */


/* 快速选择DOM结构结束 */

/* 底部样式 */
.datePicker-bottom{
    line-height: 40px;
    text-align: right;
}
.datePicker-bottom-btn span{
    font-size: 12px;
    padding: 4px 13px;
    cursor: pointer;
}
.datePicker-bottom-btn-cancel{
    color: #409eff;
}
.datePicker-bottom-btn-confirm{
    background-color: #fff;
    border: 1px solid #dcdfe6;
    margin-right: 8px;
    border-radius: 4px;
    color: #606266;
    transition: border-color .2s;
}
.datePicker-bottom-btn-confirm:hover{
    background-color: #fff;
    border-color: #409eff;
    color: #409eff;
}
.datePicker-bottom-btn-confirm.is-disabled, .datePicker-bottom-btn-confirm.is-disabled:hover, .datePicker-bottom-btn-confirm.is-disabled:active{
    background-color: #fff;
    border-color: #ebeef5;
    color: #c0c4cc;
    cursor: not-allowed;
}
/* 底部样式结束 */

/* 日历小时样式结束 */
