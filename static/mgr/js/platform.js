(function () {
    function e(n) { var t = $(n); t.find("li").each(function () { var n = $(this); n.children(".sub-menu").length > 0 && n.children("a").click(function () { var i = n.hasClass("open"); t.find("li").removeClass("open"); i || n.addClass("open") }) }) } function o() { n.css("width", "0"); var t = !0; n.animate({ width: "60%" }, { step: function (n) { n < 40 && $(this).css("opacity", .025 * n); t && ($(this).show(), t = !1); } }) } function s() { var i = 60, t; i < 100 && (n.css("width", i + "%"), t = !0, i >= 40 ? n.show() : t = !1, n.animate({ width: "100%" }, { complete: function () { n.fadeOut(); }, step: function (i) { i < 40 ? $(this).css("opacity", .025 * i) : $(this).css("opacity", 1); t && (n.show(), t = !1) } })) } var n; if (false) {
        $("#user-message").hide(); $("#user-account").hide(); $("#user-manager").hide(); var i = document.getElementById("login-dropdown-form"), u = document.getElementById("nav-login-button"), t = document.getElementById("login-dropdown"), f = $(document.getElementById("nav-login-submit")).button(); t.addEventListener("click", function (n) { n.stopPropagation() }, !1);  document.addEventListener("click", function () { t.className = null }, !1); function r() {} $(f).click(r); $(i).keydown(function (n) { n.keyCode == 13 && r() })
    } else { $("#login-dropdown").hide(); } $.prototype.postJson = function (n, t, i) { $.post(n, JSON.stringify(t), i, "json") }; $(".sidebar ul.nav-sidebar").each(function () { e(this) }); window.showLoading = function (n) { return n ? $("#loading-text").text(n) : $("#loading-text").text("加载中..."), $("#loading-box").show() }; window.hideLoading = function () { $("#loading-box").hide() }; n = $("#page-loading-bar"); window.addEventListener("beforeunload", function () { o() }); s(); $("#pager li").on("click", "a", function () { $(this).closest("li").hasClass("active") || toPage($(this).attr("title")) }); window.toPage = function (n, t) { t ? replaceSearch({ page: n, pageSize: t }) : replaceSearch({ page: n }) }; window.replaceSearch = function (n) { for (var e = /[\?&]?([^=]+)=([^&]*)/g, o = window.location.search, t = {}, r, f, u, i; m = e.exec(o) ;) r = m[1], f = m[2], t[r] ? t[r].push(f) : t[r] = [f]; for (i in n) t[i] = [n[i]]; u = []; for (i in t) u.push(t[i].map(function (n) { return i + "=" + n })); u && (window.location.search = "?" + u.join("&")) }
})()