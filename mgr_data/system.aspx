<%@ Page Title="系统管理" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="system.aspx.cs" Inherits="admin_system" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div class="index-ly-top">
        <div class="inedx-ly">
            <h2>系统管理</h2>
            <hr />
            <p style="text-align: left; color: #888; font-size: 14px;">管理员账号</p>
            <input id="unick" class="form-control" placeholder="管理员账号" />
            <p style="text-align: left; color: #888; font-size: 14px;">管理员密码</p>
            <input id="pwd" class="form-control" placeholder="管理员密码" />
            <a class="btn btn-primary" onclick="systemSave()" style="margin-top: 10px;">保存修改</a>
        </div>
    </div>


    <script>
        function systemSave() {

            var nick = $("#unick").val();
            var pwd = $("#pwd").val();

            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=systemSave",
                data: { nick: nick, pwd: pwd },
                datatype: "json",
                success: function (json) {
                    _tip(json.msg, 1500);
                },
                error: function () {
                    _tip(_expText, 1500);
                }
            });
        }
    </script>
</asp:Content>

