using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

/// <summary>
/// baseClass 的摘要说明
/// </summary>
public class baseClass : globalClass
{
    public baseClass()
    {
        string refer = HttpContext.Current.Request.Url.ToString();
        if (refer.IndexOf("/mgr_data/") != -1)
        {
            if (!uConfig._adminIsLogin)
            {
                HttpContext.Current.Response.Redirect("~/login.aspx?login=admin");
            }
        }
        else
        {
            if (!uConfig._userIsLogin)
            {
                HttpContext.Current.Response.Redirect("~/login.aspx");
            }
        }
    }
}