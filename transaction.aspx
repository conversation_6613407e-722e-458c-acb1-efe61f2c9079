<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="transaction.aspx.cs" Inherits="transaction" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    交易明细 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="" style="margin-bottom: 0px;">
        <div style="padding: 20px 10px;">

            
            <div style="margin-bottom: 20px;">

                <label class="search_wrapper" style="width: 100%;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="#999" class="ZDI ZDI--Search24 css-15ro776">
                        <g fill-rule="evenodd" clip-rule="evenodd">
                            <path d="M11.5 18.389c3.875 0 7-3.118 7-6.945 0-3.826-3.125-6.944-7-6.944s-7 3.118-7 6.944 3.125 6.945 7 6.945Zm0 1.5c4.694 0 8.5-3.78 8.5-8.445C20 6.781 16.194 3 11.5 3S3 6.78 3 11.444c0 4.664 3.806 8.445 8.5 8.445Z"></path>
                            <path d="M16.47 16.97a.75.75 0 0 1 1.06 0l3.5 3.5a.75.75 0 1 1-1.06 1.06l-3.5-3.5a.75.75 0 0 1 0-1.06Z"></path>
                        </g></svg>
                    <input placeholder="输入搜索的内容" value="" id="search_text">



                    <a style="width: 50px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 13px; background: #7bf37a; color: #333; border-radius: 4px; padding: 3px 8px;" onclick="touch();">搜索
                    </a>
                </label>

            </div>


            <div  style="overflow-y: auto;">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th class="table-cell">ID</th>
                            <th>业务类型</th>
                            <th>积分变化</th>
                            <th>信息备注</th>
                            <th>流水时间</th>
                        </tr>
                    </thead>
                    <tbody id="list">
                    </tbody>
                </table>
            </div>

            <div id="example" style="text-align: center">
                <ul id="pageLimit"></ul>
            </div>


        </div>
    </div>

    <script>
        var clearTableData = function () {
            $('#list').html('');
        }
        var pushTableData = function (data) {
            var tbody = $('#list');
            var tr = $('<tr></tr>');
            tbody.append(tr);

            tr.append("<td  class='table-cell'>" + data.id + "</td>");
            tr.append("<td>" + data.type + "</td>");
            tr.append("<td>" + (data.dianshu > 0 ? "<span style='color:green;'>" + '+' + data.dianshu + "</span>" : "<span style='color:lightsteelblue;'>" + data.dianshu + "</span>") + " 点</td>");
            tr.append("<td>" + data.remark + "</td>");
            tr.append("<td>" + data.time + "</td>");
            //data.qianbao
        }

        var touch_data = {
            data: {
                table: 'flowOrder',
                num: 10,
                index: 0,
            }
        }
        var touch = function () {
            touch_data.data.search_text = $("#search_text").val();
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '<%=unified.apiurl%>list',
                data: touch_data.data,
                success: function (result) {
                    $('#pageLimit').bootstrapPaginator({
                        currentPage: result.index + 1,
                        totalPages: result.pager,
                        size: "normal",
                        bootstrapMajorVersion: 3,
                        alignment: "right",
                        numberOfPages: 5,
                        itemTexts: function (type, page, current) {
                            switch (type) {
                                case "first": return "首页";
                                case "prev": return "上一页";
                                case "next": return "下一页";
                                case "last": return "末页";
                                case "page": return page;
                            }//默认显示的是第一页。
                        },
                        onPageClicked: function (event, originalEvent, type, page) {//给每个页眉绑定一个事件，其实就是ajax请求，其中page变量为当前点击的页上的数字。
                            touch_data.data.index = page - 1;
                            touch();
                        }
                    });



                    clearTableData();

                    if (result.status == 0) {

                        for (var i = 0; i < result.result_list.length; i++) {

                            pushTableData(result.result_list[i]);

                        }

                    }
                }
            });
        }
        touch();
    </script>
</asp:Content>

