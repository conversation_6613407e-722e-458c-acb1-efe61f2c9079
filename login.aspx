<%@ Page Language="C#" AutoEventWireup="true" CodeFile="login.aspx.cs" Inherits="serv_login" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=no" />
    <title>后台登录</title>
    <link rel="stylesheet" type="text/css" href="demo/style/register-login.css">
    <script src="demo/js/jquery.min.js"></script>
</head>
<body>
    <form id="form1" runat="server">


        <div id="box"></div>
        <div class="cent-box">
            <div class="cent-box-header">

                <%=(HttpContext.Current.Request.UrlReferrer+"").IndexOf("/mgr_data/") != -1 ? "<svg viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4889' width='64' height='64'><path d='M815.020408 1024H208.979592C94.040816 1024 0 929.959184 0 815.020408V208.979592C0 94.040816 94.040816 0 208.979592 0h606.040816c114.938776 0 208.979592 94.040816 208.979592 208.979592v606.040816c0 114.938776-94.040816 208.979592-208.979592 208.979592z' fill='#257CFF' p-id='4890'></path><path d='M731.428571 815.020408H292.571429c-45.97551 0-83.591837-37.616327-83.591837-83.591837v-20.897959c0-103.444898 84.636735-188.081633 188.081632-188.081632h229.877552c103.444898 0 188.081633 84.636735 188.081632 188.081632v20.897959c0 45.97551-37.616327 83.591837-83.591837 83.591837z' fill='#FFFFFF' opacity='.6' p-id='4891'></path><path d='M720.979592 773.22449H303.020408c-29.257143 0-52.244898-22.987755-52.244898-52.244898 0-86.726531 70.008163-156.734694 156.734694-156.734694h208.979592c86.726531 0 156.734694 70.008163 156.734694 156.734694 0 29.257143-22.987755 52.244898-52.244898 52.244898z' fill='#FFFFFF' p-id='4892'></path><path d='M512 522.44898c-86.726531 0-156.734694-70.008163-156.734694-156.734694s70.008163-156.734694 156.734694-156.734694 156.734694 70.008163 156.734694 156.734694-70.008163 156.734694-156.734694 156.734694z' fill='#FFFFFF' opacity='.88' p-id='4893'></path><path d='M512 470.204082c-57.469388 0-104.489796-47.020408-104.489796-104.489796s47.020408-104.489796 104.489796-104.489796 104.489796 47.020408 104.489796 104.489796-47.020408 104.489796-104.489796 104.489796z' fill='#FFFFFF' p-id='4894'></path><path d='M544.391837 564.244898h-62.693878L459.755102 689.632653l52.244898 41.795918 52.244898-41.795918c-6.269388-41.795918-13.583673-83.591837-19.853061-125.387755z' fill='#257CFF' p-id='4895'></path></svg>" : "<svg t='1688413826839' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='946' width='64' height='64'>                <path d='M298.666667 896c141.397333 0 256-115.968 256-259.029333C554.666667 541.568 469.333333 371.925333 298.666667 128c-170.666667 243.925333-256 413.568-256 508.970667C42.666667 780.032 157.269333 896 298.666667 896z' fill='#FFCCCC' p-id='947'></path><path d='M554.666667 85.333333c235.648 0 426.666667 191.018667 426.666666 426.666667s-191.018667 426.666667-426.666666 426.666667c-66.986667 0-131.84-15.488-190.464-44.8a42.666667 42.666667 0 0 1 38.144-76.330667 341.333333 341.333333 0 1 0 1.834666-612.010667 42.666667 42.666667 0 1 1-37.717333-76.544A425.088 425.088 0 0 1 554.666667 85.333333z m0 256a170.666667 170.666667 0 1 1 0 341.333334 170.666667 170.666667 0 0 1 0-341.333334z m0 85.333334a85.333333 85.333333 0 1 0 0 170.666666 85.333333 85.333333 0 0 0 0-170.666666z' fill='#363637' p-id='948'></path></svg>" %>


                <h2 class="sub-title"><%=(HttpContext.Current.Request.UrlReferrer+"").IndexOf("/mgr_data/") != -1 || Request.QueryString["login"]+""=="admin" ? "<span style='color:red;'>管理员登录</span>" : "客户登录" %></h2>
            </div>

            <div class="cont-main clearfix">
                <div class="index-tab">
                    <div class="index-slide-nav">
                        <a class="active">登录</a>
                        <!--<a href="register.html">注册</a>-->
                        <div class="slide-bar"></div>
                    </div>
                </div>

                <div class="login form">
                    <div class="group">
                        <div class="group-ipt email">
                            <input type="text" name="username" id="username" class="ipt" placeholder="账号" required>
                        </div>
                        <div class="group-ipt password">
                            <input type="password" name="password" id="password" class="ipt" placeholder="密码" required>
                        </div>
                    </div>
                </div>

                <div class="button">
                    <button type="button" class="login-btn register-btn" id="button">登录</button>
                </div>

                <div class="remember clearfix">
                    <label class="remember-me">
                        <span class="icon"><span class="zt"></span></span>
                        <input type="checkbox" name="remember-me" id="remember-me" class="remember-mecheck" checked>记住我</label>
                    <%--<label class="forgot-password">
                    <a href="#">忘记密码？</a>
                </label>--%>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Designed By HangTong & DT</p>
        </div>

        <script src='demo/js/particles.js' type="text/javascript"></script>
        <script src='demo/js/background.js' type="text/javascript"></script>


        <script>
            $("#button").on("click", function () {
                var data = {};
                data["do"] = 'admin_login';
                data["login"] = '<%=Request.QueryString["login"] + "" %>';
                data["username"] = $("#username").val();
                data["password"] = $("#password").val();
                data["refer"] = document.referrer + '';
                $.ajax({
                    type: "POST",
                    data: data,
                    datatype: "json",
                    success: function (json) {
                        if (json.code == 1) {
                            location.href = json.redirect_url;
                        } else {
                            alert(json.msg);
                        }
                    },
                    error: function () {
                    }
                });
            })
        </script>

    </form>
</body>




</html>
