<%@ Page Title="网站前台设置" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="sitecore.aspx.cs" Inherits="admin_sitecore" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <link rel="stylesheet" href="../kindeditor/themes/default/default.css" />
    <link rel="stylesheet" href="../kindeditor/plugins/code/prettify.css" />
    <script charset="utf-8" src="../kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="../kindeditor/lang/zh-CN.js"></script>
    <script charset="utf-8" src="../kindeditor/plugins/code/prettify.js"></script>
<%--    <style>
        button[type='button'] {
            height: 34px;
        }

        .input-group .form-control {
            border-color: #e7eaec!important;
        }

        /*.btn-default,.input-group-addon {
            border:0px;
        }*/
        .btn-default {
            width: 128px;
            text-align: center;
        }
    </style>--%>

     <style>
        button[type='button'] {
            height: 34px;
        }

        .input-group .form-control {
            border-color: #ccc!important;
        }

        .inputtitle {
            border: 0px!important;
            cursor: default!important;
            background: none!important;
            box-shadow: none!important;
            width: 150px;
            text-align: right!important;
        }

            .inputtitle i {
                font-family: tahoma;
                vertical-align: middle;
                color: #ff6f00;
                margin-right: 4px;
                font-size: 14px;
                vertical-align: middle;
            }

        .btn-default {
            display: block;
            border: #bbb 1px solid;
            color: #333!important;
            background: #f8f8f8;
            background: -webkit-gradient(linear,0% 0%, 0% 100%, from(#FFFFFF), to(#E8E8E8));
            background: -moz-linear-gradient(0% 0% 270deg, #FFFFFF,#E8E8E8);
            text-align: center;
            color: #333;
            font-weight: normal;
        }

        .com-btn-01 {
            display: inline-block;
            width:100%;
            max-width: 200px;
            height: 44px;
            line-height: 43px;
            font-size: 18px;
            font-family: "microsoft yahei";
            text-align: center;
            color: #fff!important;
            border-radius: 3px;
            vertical-align: middle;
            cursor: pointer;
        }

            .com-btn-01.color01 {
                background: #ff6700;
            }

                .com-btn-01.color01:hover {
                    background: #ff7700;
                }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div style="padding: 3px 8px;">
        <div class="ibox float-e-margins">
            <div class="ibox-content layui-form">

                <table class="coreT" style="border: 0px; margin: 0px;">

                    <tbody>
                        <tr class="none">
                            <td class="p-title">
                                <span class="pre-tip">*</span>自动唤醒链接<br>
                                （当前网站的一个.aspx页面）：
                            </td>
                            <td>
                                <input type="text" class="update" id="siteurl" value="<%=Request.Url %>" />
                            </td>
                        </tr>

                         <%for (int i = 0; i < arrlist.Count; i++)
                          {%>

                        <%listdata temp = arrlist[i];
                          switch (temp.type)
                          {
                              case "textarea":%>
                        <tr>
                            <div class="col-md-12 form-group  m-b-sm">
                                <div class="input-group " style="width: 100%;">
                                    <span class="input-group-addon inputtitle"><%=(temp.nostar?"":"<i>*</i> ")+temp.name %>：</span>
                                    <textarea style="width: 100%; height: <%=emptyValue(getContainer(temp.param,"h:",";"),"120") %>px; resize: none;" class="form-control update <%=temp.remark %>" id="<%=temp.id %>" placeholder="" maxlength="5000"><%=uConfig.stcdata(temp.id) %></textarea>
                                </div>
                            </div>
                        </tr>
                        <% break;
                              case "images":%>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default inputtitle"><%=(temp.nostar?"":"<i>*</i> ")+temp.name %>：</button>
                                    </span>
                                    <div class="update" id="<%=temp.id %>" style="display: none;" data-type="<%=temp.remark %>">
                                        <img src="<%=uConfig.stcdata(temp.id) %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata(temp.id).Replace("../", myhost + "/") %>" placeholder="" disabled="disabled" style="display: none;">

                                    <button type="button" class="btn btn-default imgbtn" onclick="_upload('<%=temp.id %>')">上传图片</button>

                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: left; padding-left: 160px;">
                            </div>
                        </tr>
                        <% break;
                              default:%>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default inputtitle"><%=(temp.nostar?"":"<i>*</i> ")+temp.name %>：</button>
                                    </span>
                                    <input type="text" class="form-control update" id="<%=temp.id %>" value="<%=uConfig.stcdata(temp.id) %>" placeholder="请输入<%=temp.name %>">
                                </div>
                            </div>
                        </tr>
                        <%
                           break;
                          } %>

                        <% } %>


                       <%-- <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">网站名称标题</button>
                                    </span>
                                    <input type="text" class="form-control update" id="sitename" value="<%=uConfig.stcdata("sitename").Replace("\"", "&quot;") %>" placeholder="请输入网站要显示的名称">
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">网站的关键字</button>
                                    </span>
                                    <input type="text" class="form-control update" id="sitekey" value="<%=uConfig.stcdata("sitekey").Replace("\"", "&quot;") %>" placeholder="请输入网站要显示的关键字">
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">网站描述信息</button>
                                    </span>
                                    <input type="text" class="form-control update" id="sitedesc" value="<%=uConfig.stcdata("sitedesc").Replace("\"", "&quot;") %>" placeholder="请输入网站要显示的描述信息">
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">网站公司名称</button>
                                    </span>
                                    <input type="text" class="form-control update" id="sitegs" value="<%=uConfig.stcdata("sitegs").Replace("\"", "&quot;") %>" placeholder="">
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">网站备案号</button>
                                    </span>
                                    <input type="text" class="form-control update" id="sitebah" value="<%=uConfig.stcdata("sitebah").Replace("\"", "&quot;") %>" placeholder="">
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">前台标志Logo</button>
                                    </span>
                                    <div class="update" id="bzlogo" style="display: none;">
                                        <img src="<%=uConfig.stcdata("bzlogo") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("bzlogo").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('bzlogo')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">注册登录Logo</button>
                                    </span>
                                    <div class="update" id="zcdllogo" style="display: none;">
                                        <img src="<%=uConfig.stcdata("zcdllogo") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("zcdllogo").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('zcdllogo')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">注册页面背景</button>
                                    </span>
                                    <div class="update" id="regbackimg" style="display: none;">
                                        <img src="<%=uConfig.stcdata("regbackimg") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("regbackimg").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('regbackimg')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">登录页面背景</button>
                                    </span>
                                    <div class="update" id="loginbackimg" style="display: none;">
                                        <img src="<%=uConfig.stcdata("loginbackimg") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("loginbackimg").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('loginbackimg')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">前台网站Logo</button>
                                    </span>
                                    <div class="update" id="sitelogo" style="display: none;">
                                        <img src="<%=uConfig.stcdata("sitelogo") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("sitelogo").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('sitelogo')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">首页轮播图</button>
                                    </span>
                                    <div class="update" id="index_rotary_img" style="display: none;" data-type="1">
                                        <img src="" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("index_rotary_img").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('index_rotary_img')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">联系二维码</button>
                                    </span>
                                    <div class="update" id="gslxewm" style="display: none;">
                                        <img src="" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("gslxewm").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('gslxewm')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                         <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-addon">
                                        关于我们
                                    </span>
                                    <textarea type="text" style="width: 100%; height: 250px;" class="form-control update" id="gywm" placeholder=""><%=uConfig.stcdata("gywm").Replace("\"", "&quot;") %></textarea>
                                </div>
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">后台内页Logo</button>
                                    </span>
                                    <div class="update" id="sitelogoad" style="display: none;" data-type="1">
                                        <img src="<%=uConfig.stcdata("sitelogoad") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("sitelogoad").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('sitelogoad')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>
                        <tr>
                            <div class=" col-md-12 form-group  m-b-sm">
                                <div class="input-group ">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default ">后台标志Logo</button>
                                    </span>
                                    <div class="update" id="htbzlogo" style="display: none;">
                                        <img src="<%=uConfig.stcdata("htbzlogo") %>" />
                                    </div>
                                    <input type="text" class="form-control" value="<%=uConfig.stcdata("htbzlogo").Replace("\"", "&quot;").Replace("../", myhost + "/") %>" placeholder="">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default " onclick="_upload('htbzlogo')">选择文件</button>
                                    </span>
                                </div>
                            </div>
                            <div class=" col-md-12 form-group  m-b-sm imgshowList" style="text-align: center;">
                            </div>
                        </tr>--%>

                        <tr>
                            <td class="p-title"></td>
                            <td>
                                <div style="padding: 12px 0;">
                                    <a class="btn btn-primary" onclick="updateConfig()" style="text-decoration: none;">
                                        <i class="fa-floppy-o fa"></i>&nbsp;
                            保存更改</a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <script>
        var radios = ",";
        $("input[type='radio']").each(function () {
            var name = $(this).attr("name");
            if (radios.indexOf("," + name + ",") == -1) {
                radios += name + ",";
                console.log(radios, name);
                if ($("#" + name).length > 0) {
                    $("input[name='" + name + "']").eq($("#" + name + "").val()).attr("checked", "checked");
                    $("input[name='" + name + "']").click(function () {
                        $("#" + name + "").val($(this).index("input[name='" + name + "']"));
                    })
                }
            }
        })

        $("#needExa").val('<%=uConfig.stcdata("needExa") %>');
    </script>

    <script>
        var host_url = '<%=myhost %>';

        function resetImgsInput() {            
            $(".imgshowList").each(function () {
                var imgstr = "";
                $(this).find("img").each(function () {
                    var img = $(this).attr("src");
                    imgstr += img + ",";
                });
                $(this).closest(".col-md-12").prev().find("input").val(imgstr);
            });
        }

        function show_imgs_list() {
            $(".imgshowList").each(function () {
                var input = $(this).closest(".col-md-12").prev().find("input");
                var imgs = input.val();
                if (imgs) {
                    $(this).html("");
                    imgs = imgs.split(',');
                    var imgstr = "";
                    for (var i = 0; i < imgs.length; i++) {
                        var img = imgs[i];
                        if (img != "") {
                            var timestamp = (new Date()).valueOf() + "_" + randomString(32) + "_" + randomString(32);
                            //$(this).append("<div class='img _" + timestamp + "a' style=\"display:inline-block;margin:2px;\"><p style=\"background: gainsboro;padding:3px 0;\"><a class='del' onclick=\"javascript:$('._" + timestamp + "a').remove();resetImgsInput();\">删除图片</a><p><a href=\"" + img + "\" target=\"_blank\" ><img src=\"" + img + "\" style=\"height: 120px;width: 120px;background:#000;\" /></a></div>");

                            $(this).append("<div class='img _" + timestamp + "a' style=\"display:inline-block;margin:2px;position:relative;\"><a href=\"" + img + "\" target=\"_blank\" ><img src=\"" + img + "\" style=\"height: 80px;width: 80px;background:#000;\" /></a><div class=\"remove-this\" onclick=\"javascript:$('._" + timestamp + "a').remove();resetImgsInput();\"></div></div>");

                            imgstr += img + ",";
                        }
                    }
                    imgstr = imgstr.trim(',');
                    input.val(imgstr);
                }
            })
        }

        function _upload(name) {
            uploadImages(function (json) {
                if (json.success) {
                    var name = json.path;
                    var obj = $("#" + name);
                    var tagname = obj.prop("tagName");
                    var imgtype = obj.data("type");
                    imgurl = json.imgurl.replace("..", host_url);
                    console.log(json, imgurl, name, tagname, imgtype);
                    if (tagname == "INPUT") {
                        obj.val(imgurl);
                    } else {
                        if (imgtype == "1") {
                            obj.next().val(obj.next().val() + "," + imgurl);
                        } else {
                            obj.next().val(imgurl);
                        }
                        obj.html("<img src='" + imgurl + "'>");
                        show_imgs_list();
                    }
                } else {
                    layAlert("上传失败");
                }
            }, name)

        }
        show_imgs_list();
    </script>

    <script>
        function updateConfig() {
            for (var i = 0; i < editors.length; i++) {
                editors[i].sync();
            }
            var jsondata = eval('([])');
            $(".update").each(function () {
                var id = $(this).attr("id");
                var arr;
                var img = $("#" + id + " img");
                switch (id) {
                    case "carousel":
                        var data = eval('([])');
                        $("#carousel .img").each(function () {
                            var img = {
                                "url": $(this).find("input").val(),
                                "img": $(this).find("img").attr("src")
                            };
                            data.push(img);
                        });
                        arr = {
                            "name": id,
                            "data": (JSON.stringify(data) + ""),
                            "view": data
                        };
                        console.log(data);
                        console.log(JSON.stringify(data));
                        break;
                    default:
                        if (img.length > 0) {
                            if ($(this).css("display") == "none") {
                                img = $(this).next("input").val();
                                img = selectImgs(img).join(',');
                                console.log("input_img", img);
                            } else {
                                img = img ? img.attr("src") : "";
                            }
                            arr = {
                                "name": id,
                                "data": img
                            };
                        } else {
                            arr = {
                                "name": id,
                                "data": $(this).val()
                            };
                        }
                        break;

                }
                jsondata.push(arr);
            })

            console.log(jsondata);
            jsondata = JSON.stringify(jsondata);
            console.log(jsondata);

            $.ajax({
                type: "POST",
                url: "apidata.aspx?do=updateConfig",
                data: { data: jsondata },
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }

                },
                error: function () {
                    serr();
                }
            });
        }

        function updateCache() {
            $.ajax({
                type: "POST",
                url: "apidata.aspx?do=updateCache",
                data: {},
                datatype: "json",
                success: function (data) {
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1000 });
                    } else {
                        layer.msg(data.msg, { icon: 2, time: 1500 });
                    }

                },
                error: function () {
                    serr();
                }
            });
        }


        layui.use(['form', 'layedit'], function () {
            var form = layui.form
            , layer = layui.layer
            , layedit = layui.layedit;
            layui.form.render();
        });
    </script>

    <script>
        var editors = new Array();
        KindEditor.ready(function (K) {
            console.log("test");
            $(".kedit").each(function () {
                var id = $(this).attr("id");
                var editor = K.create('#' + id, {
                    cssPath: '../kindeditor/plugins/code/prettify.css',
                    uploadJson: '../kindeditor/upload_json.ashx',
                    fileManagerJson: '../kindeditor/file_manager_json.ashx',
                    allowFileManager: true,
                    afterCreate: function () {
                        var self = this;
                        K.ctrl(document, 13, function () {
                            self.sync();
                            K('form[name=example]')[0].submit();
                        });
                        K.ctrl(self.edit.doc, 13, function () {
                            self.sync();
                            K('form[name=example]')[0].submit();
                        });
                    }
                });
                editors.push(editor);
            })
            prettyPrint();

            console.log("editors", editors);
        });
    </script>
</asp:Content>

