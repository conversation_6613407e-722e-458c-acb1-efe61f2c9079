using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Collections.Specialized;
using System.Net;
using LitJson;
using System.Data.SqlClient;

/// <summary>
/// chelper 的摘要说明
/// </summary>
public class chelper : globalClass
{
    public chelper()
    {
    }

    public static void cdt(string name, string where = "")
    {
        where = where == "" ? "" : " where " + where;
        cae.RemoteCache("tempdt_" + name + where.Replace(" ", "_"));
    }

    public static DataTable gdt(string name, string where = "")
    {
        DataTable dt = new DataTable();
        dbClass db = new dbClass();
        string sql = string.Empty;
        where = where == "" ? "" : " where " + where;

        dt = (DataTable)cae.GetCache("tempdt_" + name + where.Replace(" ", "_"));
        if (dt != null)
        {
            return dt;
        }
        switch (name)
        {
            default:
                sql = " select * from " + name + " with(nolock) " + where;
                dt = db.getDataTable(sql);
                break;
        }
        return dt;
    }

    public static string formatDate(string datestr)
    {
        string result = null;
        try
        {
            result = Convert.ToDateTime(datestr).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception)
        {

        }
        return result;
    }


    public static string transNum(string num)
    {
        try
        {
            double b = Convert.ToDouble(num);
            num = (b / 100).ToString();
        }
        catch (Exception)
        {

        }
        return num;
    }
    //网站自有功能


}