using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class back : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        dbClass db = new dbClass();
        db.ExecuteNonQuery(@" BACKUP DATABASE zzzke TO DISK = 'C:\backup\database\sqlserver\zzzke_" + DateTime.Now.ToString("yyyyMMdd") + ".bak' WITH FORMAT, STATS = 1; ");

        Response.Write("backup ok");
        Response.End();

    }
}