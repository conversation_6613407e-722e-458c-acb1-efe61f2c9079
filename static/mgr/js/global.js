var _expText = "error";

var userNick = getCookie("_usernick");
/*
 * jQuery placeholder, fix for IE6,7,8,9
 * <AUTHOR>
 * @since 20131115.1504
 * @website ishere.cn
 */
var JPlaceHolder = {
    //检测
    _check: function () {
        return 'placeholder' in document.createElement('input');
    },
    //初始化
    init: function () {
        if (!this._check()) {
            this.fix();
        }
    },
    //修复
    fix: function () {
        jQuery(':input[placeholder]').each(function (index, element) {
            var self = $(this), txt = self.attr('placeholder');
            self.wrap($('<div></div>').css({ position: 'relative', zoom: '1', border: 'none', background: 'none', padding: 'none', margin: 'none' }));
            var pos = self.position(), h = self.outerHeight(true), paddingleft = self.css('padding-left');
            var holder = $('<span></span>').text(txt).css({ position: 'absolute', left: pos.left, top: pos.top+12, height: h, lienHeight: h, paddingLeft: paddingleft, color: '#aaa' }).appendTo(self.parent());
            self.focusin(function (e) {
                holder.hide();
            }).focusout(function (e) {
                if (!self.val()) {
                    holder.show();
                }
            });
            holder.click(function (e) {
                holder.hide();
                self.focus();
            });
        });
    }
};
//执行
jQuery(function () {
    JPlaceHolder.init();
});

function toTarget(obj) {
    var ico = $(obj).data("ico");
    var hf = $(obj).data("hf");

    if (hf != "#" && hf != "") {
        location.href = hf;
    }
}

function smenu(name) {
    var obj = $(".m-" + name);

    var ico = obj.data("ico");
    var hf = obj.data("hf");

    obj.find("img").attr("src", "../icos/" + ico + "-act.png");
    obj.find("p").css({ "color": "#1296db" });

    obj.siblings().find("img").each(function () {
        $(this).attr("src", "../icos/" + $(this).closest(".col-3").data("ico") + ".png");

    });
    obj.siblings().find("p").css({ "color": "#707070" })
}

//自定义字典对象
function Dictionary() {
    this.data = new Array();

    this.put = function (key, value) {
        this.data[key] = value;
    };

    this.get = function (key) {
        return this.data[key];
    };

    this.remove = function (key) {
        this.data[key] = null;
    };

    this.isEmpty = function () {
        return this.data.length == 0;
    };

    this.size = function () {
        return this.data.length;
    };
}

    $(function () {
        var url = document.location.href.toUpperCase();
        try {
            url = decodeURI(url);
        } catch (e) {}
        var isFinded = false;

        isFinded = false;
        //$(".sidebar-menu a").each(function () {
        //    var obj = $(this);
        //    var nUrl = obj.attr("href").replace("../", "/").replace("./", "/");
        //    if (nUrl != "#") {
        //        nUrl = nUrl.toUpperCase();
        //        if (url.indexOf(nUrl) != -1) {
        //            obj.closest("li").addClass("active").closest(".treeview").addClass("active");
        //            return false;
        //        }
        //    }
        //});

        $("#msNav a").each(function () {
            var obj = $(this);
            var nUrl = obj.attr("href").replace("../", "/").replace("./", "/");
            if (nUrl != "#") {
                nUrl = nUrl.toUpperCase();
                if (url.indexOf(nUrl) != -1) {
                    obj.closest("li").addClass("active").siblings().removeClass("active");
                    return false;
                }
            }
        });

        //cMenu
        isFinded = false;
        $("#cMenu a").each(function () {
            var obj = $(this);
            var nUrl = obj.attr("href").replace("../", "/").replace("./", "/");
            if (nUrl != "#") {
                nUrl = nUrl.toUpperCase();
                if (url.indexOf(nUrl) != -1) {
                    obj.closest("li").addClass("act").siblings().removeClass("act");
                    return false;
                }
            }
        });

        isFinded = false;
        $(".nav-sidebar a").each(function () {
            var obj = $(this);
            var nUrl = obj.attr("href").replace("../", "/").replace("./", "/");
            if (nUrl != "#") {
                nUrl = nUrl.toUpperCase();
                if (url.indexOf(nUrl) != -1) {
                    obj.closest("li").addClass("active").siblings().removeClass("act");
                    var ma = obj.closest("li").closest(".sub-menu2");
                    if (ma.html()) {
                        ma.closest("li").addClass("open2");
                    }

                    ma = obj.closest("li").closest(".sub-menu");
                    if (ma.html()) {
                        ma.closest("li").addClass("open");
                    }
                    return false;
                }
            }
        });

        //cMenu
        isFinded = false;
        $("#menuitemTop a").each(function () {
            var obj = $(this);
            var nUrl = obj.attr("href").replace("../", "/").replace("./", "/");
            if (nUrl != "#") {
                nUrl = nUrl.toUpperCase();
                if (url.indexOf(nUrl) != -1) {
                    obj.addClass("hov").siblings().removeClass("hov");
                    return false;
                }
            }
        });

        initTable();
    })

    function initTable() {
        $(".iTable tbody tr").each(function () {
            var i = $(this).index();
            if (i % 2 == 1) {
                $(this).find("td").css("background", "#FFF");
            } else {
                $(this).find("td").css("background", "#FBFBFB");
            }
        })

        $(".ctTable:not(.discolor) tbody tr").each(function () {
            var i = $(this).index();
            if (i % 2 == 1) {
                $(this).css("background", "#FFF").data("bak", "#FFF");
            } else {
                $(this).css("background", "#FBFBFB").data("bak", "#FBFBFB");
            }
        })

        $(".ctTable:not(.discolor) tbody tr").mouseover(function () {
            $(this).css("background", "#F6F6F6");
        });
        $(".ctTable:not(.discolor) tbody tr").mouseleave(function () {
            $(this).css("background", $(this).data("bak"));
        })
    }


    // ck __

    function getCookie(name) {
        var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
        if (arr != null) return unescape(arr[2]); return null;
    }


    function addCookie(CookiesName, name, value) {
        var p = getCookie(CookiesName);
        if (p) {
            document.cookie = CookiesName + "=" + p + "&" + name + "=" + value + ";path=/";
        } else {
            document.cookie = CookiesName + "=" + name + "=" + value + ";path=/";
        }
        return true;
    }



    function findCookies(CookiesName, Cname) {
        var ps = null;
        var p = getCookie(CookiesName);
        if (p) { ps = p.split("&"); }
        if (p == null || ps == null) { return ""; }
        for (var i = 0; i < ps.length; i++) {
            var cookieValue = ps[i];
            var temp = cookieValue.split("=");
            if (temp.length >= 2) {
                if (temp[0] == Cname) {
                    return temp[1];
                }
            }
        }
        return "";
    }

    function delCookies(CookiesName) {
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        document.cookie = name + "=v;expires=" + exp.toGMTString() + ";path=/";
    }



    function addCookie(CookiesName, name, value) {
        var ps = null;
        var p = getCookie(CookiesName);
        var newCookies = "";
        var flag = "&";
        if (p) { ps = p.split("&"); }
        var maxLen = ps.length;
        if (p == null || ps == null) { return false; }
        for (var i = 0; i < maxLen; i++) {
            if (i == maxLen - 1) {
                flag = "";
            }
            var cookieValue = ps[i];
            var temp = cookieValue.split("=");
            if (temp.length >= 2) {
                if (temp[0] == name) {
                    newCookies += temp[0] + "=" + value + flag;
                } else {
                    newCookies += ps[i] + flag;
                }
            } else {
                newCookies += ps[i] + flag;
            }
        }
        document.cookie = CookiesName + "=" + newCookies + ";path=/";
        return true;
    }

    //QueryString
    function request(paras, url) {
        var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
        var paraObj = {}
        for (i = 0; j = paraString[i]; i++) {
            paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = j.substring(j.indexOf("=") + 1, j.length);
        }
        var returnValue = paraObj[paras.toLowerCase()];
        if (typeof (returnValue) == "undefined") {
            return "";
        } else {
            return returnValue;
        }
    }

    function req(paras) {
        return request(paras, location.href);
    }


    //去除数组重复
    function unique(arr) {
        var result = [], hash = {};
        for (var i = 0, elem; (elem = arr[i]) != null; i++) {
            if (!hash[elem]) {
                result.push(elem);
                hash[elem] = true;
            }
        }
        return result;
    }

    //浏览器判断

    function myBrowser() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1) {
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            return "IE";
        }; //判断是否IE浏览器
    }

    function IsPC() {
        var userAgentInfo = navigator.userAgent;
        var Agents = ["Android", "iPhone",
                    "SymbianOS", "Windows Phone",
                    "iPad", "iPod"];
        var flag = true;
        for (var v = 0; v < Agents.length; v++) {
            if (userAgentInfo.indexOf(Agents[v]) > 0) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
                + " " + date.getHours() + seperator2 + date.getMinutes()
                + seperator2 + date.getSeconds();
        return currentdate;
    }

    function isWeiXin() {
        var ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
            return true;
        } else {
            return false;
        }
    }


    function showMessage(content, title, area) {
        if (!area) {
            area = ['800px', '400px'];
        }
        var w = $(window).width();
        var nw = parseInt(area[0].replace('px', ''));
        if (w < nw) {
            area = [w + 'px', '400px'];
        }

        layer.open({
            type: 1,
            title: title,
            maxmin: true,
            area: area,
            content: "<div style='padding:6px'>" + content + "</div>",
            end: function () {
            }
        });
    }


    var layPageindex;
    function sLayPage(title, url, callback, area) {
        if (!area) {
            area = ['600px', '320px'];
        }
        var w = $(window).width();
        var nw = parseInt(area[0].replace('px', ''));
        if (w < nw) {
            area = [w + 'px', '400px'];
        }
        layPageindex = layer.ready(function () {
            layer.open({
                type: 2,
                title: title,
                maxmin: true,
                area: area,
                content: url,
                end: function () {
                    callback();
                }
            });
        });
    }
    var _gotourl = "";
    function gotourl() {
        if (_gotourl != "") {
            location.href = _gotourl;
        }
    }

    function _tip(title, time, callback) {
        layer.msg(title, {
            time: time,
        }, callback)
    }

    function cLayPage() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }

    function closeLayPage() {
        var index = layer.getFrameIndex(window.name);
        layer.close(index);
    }

    function cAll() {
        layer.closeAll();
    }

    function serr(cb) {
        layer.msg(_expText, { icon: 2 }, cb);
    }

    function stitle(title) {
        $(".top_title").html(title);
    }

    function rfPage() {
        location.href = location.href;
    }

    function statusToCode(status,id,type) {
        var cls = "status";
        var refund = "";
        if (id != "" || !isNaN(id)) {
            refund = "<a style='display: inline-block;width: 50px;' onclick='order_refund(\"" + id + "\")'>申请退单</a>"
        }
        switch (status) {
            case "未开始":
                cls += "";
                break;
            case "进行中":
                cls += " normal";
                break;
            case "已完成":
                cls += " finish";
                refund = "";
                break;
            case "任务异常":
                cls += " fail";
                refund = "";
                break;
            case "订单异常":
                cls += " nomal";
                break;
            default:
                if (type == "ad" && status.indexOf("开播") != -1) {

                } else {
                    refund = "";
                }
                break;

        }
        status = "<div class='" + cls + "'>" + status + "</div>" + refund;
        return status;
    }