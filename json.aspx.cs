using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;
using System.Xml;
using com.cotees;

public partial class myServerFiles : baseClass
{
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        string _action = Request.QueryString["do"] + "";
        Response.Write(main(_action));
        Response.End();
    }

    public string main(string action)
    {
        fuzhu fz = new fuzhu(HttpContext.Current);
        bool checkResult = false;
        List<SqlParameter> pams = fz.collectReqParames(out checkResult);


        string res = "";
        int exres = 0;

        unified unif = new unified();

        //数据库相关参数
        string sql = string.Empty;
        dbClass db = new dbClass();
        SqlParameter[] parames;
        DataTable dt;
        DataSet ds;
        DataRow[] dr;
        string sqlParam = string.Empty;

        // - table参数
        string cond = string.Empty;
        string _key = fz.req("search_text");
        _key = SafeSql(_key);
        List<string> cols;


        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
        Dictionary<string, object> dic = new Dictionary<string, object>();
        Dictionary<string, object> dic2 = new Dictionary<string, object>();
        Dictionary<string, object> temp_dic = new Dictionary<string, object>();


        string token = string.Empty;
        string sp = string.Empty;
        string status = string.Empty;
        string source = string.Empty;
        string temp = string.Empty;
        string temp2 = string.Empty;
        string order_id = string.Empty;

        if (!uConfig._userIsLogin)
        {
            fz.sendResponse("无法完成操作");
        }

        panda pd = new panda();

        bool is_Login = true;

        switch (action)
        {
            case "list":
                switch (fz.req("table"))
                {
                    case "orders":
                        cols = new List<string>();
                        cols.Add("transactionID as taskid");
                        cols.Add("appname as type");
                        cols.Add("source_url as taskurl");
                        cols.Add("url as order_url");
                        cols.Add("plid as param");
                        cols.Add("total_fee");
                        cols.Add("otherinfo");
                        cols.Add("createTime-function formatDate() as time");

                        cols.Add("order_status");
                        cols.Add("replace_people");
                        cols.Add("close_not_refund");
                        cols.Add("switch_name");

                        cols.Add("order_start");
                        cols.Add("order_current");
                        cols.Add("order_count");
                        cols.Add("usernick");
                        cols.Add("allow_refund");
                        cols.Add("refund_order");
                        cols.Add("close_expire_time");
                        cols.Add("state_text");
                        //cols.Add("remark1");


                        temp_dic["db"] = "orders";
                        if (fz.req("record_orders") == "true")
                        {
                            temp_dic["db"] = "record_orders";
                        }



                        if (fz.req("agent") == "1")
                        {
                            cols.Add("market_status");
                            cols.Add("market_money");

                            cond += cond == string.Empty ? string.Empty : " and ";
                            cond += " o.market_id='" + uConfig.p_uid + "'";
                        }
                        else
                        {
                            cond += cond == "" ? "" : " and ";
                            cond += " o.uid='" + uConfig.p_uid + "' ";
                        }

                        if (!string.IsNullOrEmpty(fz.param("itemId")))
                        {
                            cond += cond == string.Empty ? string.Empty : " and ";
                            cond += " ap.appcode='" + fz.param("itemId") + "'";
                        }
                        temp = ",未开始,进行中,已完成,退单中,已退单,订单异常,";
                        if (!string.IsNullOrEmpty(fz.param("orderStatus")) && temp.IndexOf("," + fz.param("orderStatus") + ",") != -1)
                        {
                            cond += cond == string.Empty ? string.Empty : " and ";
                            cond += " o.status='" + fz.param("orderStatus") + "'";
                        }
                        temp = string.Empty;
                        temp2 = _key;
                        if (_key != string.Empty)
                        {
                            _key = douyin_short_urlid(_key);

                            if (IsNumeric(_key) || _key.IndexOf("MS") == 0)
                            {
                                temp = getLiveUserIdNew(_key);
                                if (!string.IsNullOrEmpty(temp))
                                {
                                    temp = SafeSql(temp);
                                    temp = " or o.url='" + temp + "' ";
                                }
                            }

                            cond += cond == string.Empty ? string.Empty : " and ";
                            //cond += "(url like '%" + _key + "%' or note like '%" + _key + "%' " + (IsNumeric(_key) ? " or o.id=" + _key : "") + " )";
                            cond += "(" + (IsNumeric(_key) && _key.Length <= 23 ? " o.id=" + _key + " or o.transactionID='" + _key + "' or o.orderid='" + _key + "' or  " : "") + "  o.source_url='" + temp2 + "'or o.url like '%" + _key + "%'  " + temp + ")";
                        }

                        sqlParam += ",dateadd(minute,isnull(ap.close_limit_times,10),o.createTime) as close_expire_time";
                        sqlParam += ",(case when dateadd(minute,isnull(ap.close_limit_times,10),o.createTime)>getdate() then 1 else 0 end) as allow_refund";

                        sqlParam += ",(case when o.total_fee is not null then o.total_fee else o.cost end) as total_fee";

                        sqlParam += ",(case when renqi_num is not null and o.status<>'已退单' and o.status<>'已完成' then 1 else 0 end) as replace_people";
                        sqlParam += ",(case when renqi_num is not null and (o.status='进行中') then (case when lo.online=1 then '开播' else '停播' end) else o.status end) as order_status";
                        sqlParam += ",(case when (o.status='未开始' or (renqi_num is null and o.status='进行中')) and o.app_id=10001 then 1 else 0 end) as refund_order";
                        sqlParam += ",(case when o.order_type=988 and o.expireTime>GETDATE() then (case when o.status='已完成' then '临停订单' else '启用订单' end) else '' end) as switch_name";


                        sqlParam += ",(case when renqi_num is not null then (case when lo.online=1 then o2.startCount else -1 end) else o.startCount end) as order_start";
                        sqlParam += ",(case when renqi_num is not null then (case when lo.online=1 then o2.nowCount else -1 end) else o.nowCount end) as order_current";
                        //sqlParam += ",(case when renqi_num is not null then (case when o2.num is null then renqi_num else o2.num end) else o.nowCount end) as order_count";
                        sqlParam += ",o.num as order_count";

                        res = jsonClass.queryPager(temp_dic["db"] + " o  with(nolock) left join apps ap on o.code=ap.appcode left join liveOrders lo with(nolock) on lo.orderid=o.id left join orders o2 with(nolock) on lo.sub_orderid=o2.id left join users u on o.uid=u.id", cols, cond, "o.id,o.orderid,o.code,o.url,o.otherinfo,o.source_url,o.num,o.cost,o.startCount,o.nowCount,o.status,o.createTime,o.createIp,o.ofrom,o.state_text,ap.appname,ap.typename,ap.close_not_refund,o.data as plid,lo.renqi_num as renqi_num,DATEDIFF(hour,GETDATE(),dateadd(hour,o.num,o.createTime)) as diejia_num,lo.online as online,o2.startCount as rqStart,o2.num as rqNum,o2.nowCount as rqNow,isnull(o.father_id,0) as father_id,o.expireTime,o.market_status,o.market_money,u.nick as usernick,o.remark1,isnull(o.transactionID,o.id) as transactionID" + sqlParam, "o.id desc", fz.req("index"), fz.req("num"), "{}", "", "user");
                        break;
                    case "flowOrder":
                        cols = new List<string>();
                        cols.Add("id");
                        cols.Add("name as type");
                        cols.Add("note as remark");
                        cols.Add("cost as dianshu");
                        cols.Add("rest as qianbao");
                        cols.Add("createTime-function formatDate() as time");


                        cond += cond == "" ? "" : " and ";
                        cond += " uid='" + uConfig.p_uid + "' ";

                        if (_key != string.Empty)
                        {
                            cond += cond == string.Empty ? string.Empty : " and ";
                            cond += "(name like '%" + _key + "%' or note like '%" + _key + "%' " + (IsNumeric(_key) ? " or id=" + _key : "") + " )";
                        }
                        res = jsonClass.queryPager("records with(nolock)", cols, cond, "*", "id desc", fz.req("index"), fz.req("num"), "{}", "", "user");
                        break;
                    default:
                        res = "{\"code\":-1,\"msg\":\"请求的页面不存在!\"}";
                        break;
                }
                break;
            case "operator":
                fz.check_exist(new string[] { "order_id" });
                switch (fz.req("type"))
                {
                    case "repair_order":
                        //fz.sendResponse("此订单不支持补单");
                        sql = " update o set status='待补单',repairTime=(case when refundTime is null then GETDATE() else repairTime end) output deleted.* from orders o left join apps ap on o.appid=ap.id where o.id=@order_id and o.uid=@userid and ap.repair_order_max>0 and o.status='已完成' and dateadd(day,repair_order_max,createTime)>GETDATE() and (repairTime is null or dateadd(day,repair_order_intelval,repairTime)<GETDATE()) ";


                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count > 0)
                        {
                            temp = dt.Rows[0]["status"] + "";
                            log.WriteLog("退单", "前台|" + uConfig.p_userNick, "id:" + fz.req("order_id") + ",status:" + temp);
                            if (temp == "未开始")
                            {
                                parames = new SqlParameter[]{
                                    new SqlParameter("@orderid",fz.req("order_id"))
                                };
                                sql = " [tuidan] @orderid,'客户' ";
                                db.ExecuteNonQuery(sql, parames);

                                fz.sendResponse("取消成功");
                            }
                            else
                            {
                                fz.sendResponse("已提交");
                            }
                        }
                        else
                        {
                            fz.sendResponse("此订单无法补单");
                            //dic.Add("code", -1);
                            //dic.Add("msg", "订单不支持退款");
                        }
                        res = JsonMapper.ToJson(dic);

                        break;
                    case "cancel_order":
                        //fz.sendResponse("此订单不支持退单");

                        //sql = " update o set status='退单中',refundTime=(case when refundTime is null then GETDATE() else refundTime end) from orders o left join apps ap on o.appid=ap.id output deleted.* where id=@order_id and app_id=10001 and uid=@userid and (status='未开始' or (order_type=0 and code not like '%dc' and (status='进行中' or status='订单异常'))) ";

                        sql = " update o set status='退单中',refundTime=(case when refundTime is null then GETDATE() else refundTime end) output deleted.* from orders o left join apps ap on o.appid=ap.id where o.id=@order_id and o.uid=@userid and ap.refund_order=1 and (o.status='未开始' or o.status='进行中') ";


                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count > 0)
                        {
                            temp = dt.Rows[0]["status"] + "";
                            log.WriteLog("退单", "前台|" + uConfig.p_userNick, "id:" + fz.req("order_id") + ",status:" + temp);
                            if (temp == "未开始")
                            {
                                parames = new SqlParameter[]{
                                    new SqlParameter("@orderid",fz.req("order_id"))
                                };
                                sql = " [tuidan] @orderid,'客户' ";
                                db.ExecuteNonQuery(sql, parames);

                                fz.sendResponse("取消成功");
                            }
                            else
                            {
                                fz.sendResponse("已提交");
                            }
                        }
                        else
                        {
                            fz.sendResponse("此订单不支持退单");
                            //dic.Add("code", -1);
                            //dic.Add("msg", "订单不支持退款");
                        }
                        res = JsonMapper.ToJson(dic);

                        break;
                    case "close_order":
                        sql = " update o set state_text='请求中' output deleted.* from orders o left join apps ap on o.appid=ap.id where o.transactionID=@order_id and isnull(ap.close_not_refund,0)=1 and isnull(o.state_text,'')='' and  dateadd(minute,isnull(ap.close_limit_times,10),o.createTime)>getdate() ";


                        exres = db.ExecuteNonQuery(sql, pams.ToArray());
                        if (exres > 0)
                        {
                            fz.sendResponse("请求成功",1);
                        }
                        else
                        {
                            fz.sendResponse("当前订单无法操作");
                        }

                        break;
                    case "replace_people":
                        sql = " update t set t.actTime=GETDATE() output lv.id from orders t left join liveOrders lv on t.id=lv.orderid where t.id=@order_id and GETDATE()>dateadd(minute,3,isnull(t.actTime,'1990-01-01 01:01:01')) ";
                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count > 0)
                        {
                            temp = "cache_liveid_" + dt.Rows[0]["id"] + "";
                            DateTime expireTime = DateTime.Now.AddMinutes(-999);
                            cae.SetCache(temp, expireTime, expireTime);

                            fz.sendResponse("已提交");
                            //fz.sendResponse("操作成功！" + dt.Rows[0]["id"]);
                        }
                        else
                        {
                            fz.sendResponse("请休息一会后重试");
                        }
                        break;
                    case "switch_order":
                        sql = " update orders set status=(case when status='已完成' then '进行中' else '已完成' end) where id=@order_id and order_type=988 and expireTime>GETDATE() and uid=@userid ";

                        exres = db.ExecuteNonQuery(sql, pams.ToArray());
                        if (exres > 0)
                        {
                            fz.sendResponse("操作成功", 1);
                        }
                        else
                        {
                            fz.sendResponse("订单不存在", 0);
                        }
                        break;
                    default:
                        fz.sendResponse("请求失败");
                        break;
                }
                break;
            case "o_status":
                fz.check_exist(new string[] { "OrderId,订单ID" });
                if (fz.req("StatusType") == "restatus")
                {
                    sql = " update orders set status=(case when status='已完成' then '进行中' else '已完成' end) where id=@OrderId and order_type=988 and expireTime>GETDATE() and token=@token ";
                }

                if (!string.IsNullOrEmpty(sql))
                {
                    exres = db.ExecuteNonQuery(sql, pams.ToArray());
                    if (exres > 0)
                    {
                        fz.sendResponse("状态变更成功", 1);
                    }
                    else
                    {
                        fz.sendResponse("订单错误", 0);
                    }
                }
                else
                {
                    fz.sendResponse("数据有误");
                }
                break;
            case "geteway":
                //@pay_type 支付方式： Orders[未登錄付款] Recharge[用戶充值]

                //check_item_pay

                switch (fz.req("type"))
                {
                    case "item":
                        //商品订单
                        temp = "-1";
                        if (is_Login)
                        {
                            temp = uConfig.p_uid;
                        }
                        pams.Add(new SqlParameter("@fs", "1"));
                        pams.Add(new SqlParameter("@temp_userId", temp));
                        sql = " [createItemOrder] @fs,@userid,@itemId,@paytype,@email,@num ";
                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count > 0)
                        {
                            temp = dt.Rows[0]["errmsg"] + "";
                            try
                            {
                                dic.Add("orderId", dt.Rows[0]["payOrderId"] + "");
                                dic.Add("itemOrderId", dt.Rows[0]["orderId"] + "");
                                dic.Add("total_fee", dt.Rows[0]["total_fee"] + "");
                            }
                            catch (Exception)
                            {

                            }

                            if (fz.req("paytype") == "account" && temp == "success")
                            {
                                pams = new List<SqlParameter>();
                                pams.Add(new SqlParameter("@item_orderId", dt.Rows[0]["item_orderId"] + ""));
                                sql = " [item_deliver] @item_orderId ";
                                dt = db.getDataTable(sql, pams.ToArray());
                                if (dt.Rows.Count > 0)
                                {
                                    temp = dt.Rows[0]["errmsg"] + "";
                                }
                                else
                                {
                                    fz.sendResponseTrans("error");
                                }
                            }

                            fz.sendResponseTrans(temp, (temp == "success" ? 1 : 0), dic);
                        }
                        else
                        {
                            fz.sendResponseTrans("error");
                        }

                        break;
                    case "check_item_pay":
                        sql = " select * from [item_order_list] with(nolock) where orderId=@orderId ";
                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count == 0)
                        {
                            fz.sendResponse("orderError", -1009);
                        }
                        dic = new Dictionary<string, object>();
                        dic.Add("orderId", dt.Rows[0]["orderId"] + "");
                        dic.Add("finish_time", dt.Rows[0]["finish_time"] + "");
                        if (dt.Rows[0]["finish_time"] + "" != "")
                        {
                            fz.sendResponse("success", 1, dic);
                        }
                        else
                        {
                            fz.sendResponse("unpay", -1, dic);
                        }

                        break;
                    default:
                        //用户充值
                        if (fz.req("type") == "check_email")
                        {
                            if (fz.empty("email"))
                            {
                                fz.sendResponseTrans("emailIsEmpty");
                            }
                            sql = " select * from customer_list with(nolock) where email=@email ";
                            dt = db.getDataTable(sql, pams.ToArray());
                            dic.Add("email", fz.req("email"));
                            if (dt.Rows.Count > 0)
                            {
                                dic.Add("username", dt.Rows[0]["name"] + "");
                                temp = "success";
                            }
                            else
                            {
                                temp = "accountIsNotExists";
                            }
                            fz.sendResponseTrans(temp, (dt.Rows.Count > 0 ? 1 : 0), dic);
                        }


                        if (!is_Login && fz.req("pay_type") == "Recharge" && fz.empty("recharge_account"))
                        {
                            fz.sendResponseTrans("nullRechargeAccount");
                        }


                        fz.limit_string("pay_type", "Orders|Recharge", "{key} is Error!");
                        fz.limit_string("payway", "paypal", "{key} is Error!");
                        if (fz.req("pay_type") == "Recharge")
                        {
                            fz.is_Number("total_fee", "money is error");
                        }
                        sql = " [createPayingOrder] @fs,@userid,@pay_type,@payway,@total_fee,@unlogin_orderNo,@recharge_account ";
                        dt = db.getDataTable(sql, pams.ToArray());
                        if (dt.Rows.Count > 0)
                        {
                            temp = dt.Rows[0]["errmsg"] + "";
                            try
                            {
                                dic.Add("orderId", dt.Rows[0]["orderId"] + "");
                                dic.Add("total_fee", dt.Rows[0]["total_fee"] + "");
                            }
                            catch (Exception)
                            {

                            }
                            fz.sendResponseTrans(temp, (temp == "success" ? 1 : 0), dic);
                        }
                        else
                        {
                            fz.sendResponseTrans("error");
                        }
                        break;
                }
                break;
            case "search_item":

                switch (fz.req("typename"))
                {
                    case "邮箱":
                        temp = " email=@keyword ";
                        if ((fz.req("keyword").Split('@')[0].Length < 7 && IsNumeric(fz.req("keyword").Split('@')[0])) || fz.req("keyword").Split('@')[0].Length < 4)
                        {
                            fz.sendResponse("邮箱过于简单不支持查询");
                        }
                        break;
                    case "订单号":
                        temp = " orderId=@keyword ";
                        break;
                    default:
                        fz.sendResponse("数据有误");
                        break;
                }

                sql = " select top 5 *,CONVERT(varchar(100),create_time, 20) as format_time from [item_order_list] with(nolock) where " + temp;
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dic = new Dictionary<string, object>();
                        dic.Add("title", dt.Rows[i]["title"]);
                        dic.Add("orderId", dt.Rows[i]["orderId"]);
                        dic.Add("num", dt.Rows[i]["num"]);
                        dic.Add("state", dt.Rows[i]["state"]);
                        dic.Add("create_time", dt.Rows[i]["format_time"]);

                        list.Add(dic);
                    }
                    dic = new Dictionary<string, object>();
                    dic.Add("list", list);
                    fz.sendResponse("成功", 1, dic);

                }
                else
                {
                    fz.sendResponse("暂无数据");
                }


                break;
            default:
                fz.sendResponse("request error");
                break;
        }


        return res;
    }

    //public string checkPhonePackage(string phone,string itemid){
    //}

    public string random()
    {
        Random ran = new Random();
        string RandKey = string.Empty;
        for (int i = 0; i < 4; i++)
        {
            RandKey += ran.Next(1000, 9999);
        }
        return RandKey;
    }
}