using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using LitJson;
using System.Text.RegularExpressions;
using System.IO;
using System.Text;

public partial class accessFiles : globalClass
{
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.ContentType = "application/json";
        Response.Write(main());
        Response.End();
    }

    public string main()
    {

        //string postContent = string.Empty;
        //using (StreamReader reader = new StreamReader(Request.InputStream, Encoding.UTF8))
        //{
        //    postContent = reader.ReadToEnd();
        //    // 处理 postContent
        //}

        //log.WriteLog("api_NoitfyLogs", getUserIP(HttpContext.Current), Request.QueryString.ToString() + "|" + postContent);


        string _code = "status";
        string _message = "message";

        fuzhu fz = new fuzhu(HttpContext.Current);
        bool checkResult = false;
        List<SqlParameter> pams = fz.collectReqParames(out checkResult);

        string str = string.Empty;
        string temp = string.Empty;
        string temp2 = string.Empty;
        string sqlParam = string.Empty;
        bool boo = false;
        Dictionary<string, object> dic = new Dictionary<string, object>();
        List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();

        string userid = fz.req("apiUid");
        string token = fz.req("apiToken");
        string ItemIds = formatOrderids(fz.req("orderid"), 30);

        dbClass db = new dbClass();
        DataTable dt = new DataTable();
        SqlParameter[] parames = new SqlParameter[] { };
        string sql = string.Empty;
        int res = 0;

        unified unif = new unified();

        string cacheToken = string.Empty;
        cacheToken = (string)cae.GetCache("uidtoken_" + userid);

        if (string.IsNullOrEmpty(cacheToken))
        {
            sql = " select * from users with(nolock) where id=@userid ";
            parames = new SqlParameter[]{
                new SqlParameter("@userid",userid)
            };
            dt = db.getDataTable(sql, parames);
            if (dt.Rows.Count == 0)
            {
                return getResBody(_config("apiLimit"), -1, null, _code, _message);
            }
            cacheToken = dt.Rows[0]["token"] + "";
            cae.SetCache("uidtoken_" + userid, cacheToken, DateTime.Now.AddMinutes(10));

            log.WriteLog("refresh-api-token", "", "userid:" + userid);

        }
        if (cacheToken != token)
        {
            return getResBody(_config("apiLimit"), -1, null, _code, _message);
        }

        switch (fz.req("service"))
        {
            case "order_create":
                log.WriteLog("create-api", "", "userid:" + userid + ",url:" + fz.req("url") + ",typeid:" + fz.req("pid") + ",num:" + fz.req("count"));

                DateTime beforeDT = DateTime.Now;
                temp = "cache_liveid_" + Request.QueryString["id"];

                if (fz.req("pid") == "dy-pldz")
                {
                    log.WriteLog("apidy", "pldz", "url:" + fz.req("url") + ",data:" + fz.req("note"));
                }

                try
                {
                    str = unif.OrderCreate(userid, fz.req("pid"), "", fz.req("url"), fz.req("count"), fz.req("note"), "", fz.req("note"), fz.req("oid"), "API");
                }
                catch (Exception ex)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("errMessage", ex.Message.ToString());
                    str = getResBody("error", -1, dic);
                    log.WriteLog("api_order_error", "api_error", "url:" + fz.req("url") + ",typeid:" + fz.req("pid") + ",num:" + fz.req("count") + ",param:" + fz.req("note") + ",errmsg:" + ex.ToString());
                }


                //log.WriteLog("api_order", "info", appkeyUserName(token) + " > url:" + fz.req("task_id") + ",typeid:" + fz.req("typeid") + ",num:" + fz.req("task_num") + ",param:" + fz.req("task_param") + ",str:" + str);

                if (str.IndexOf("success") != -1)
                {
                    log.WriteLog("api_success_order", "info", appkeyUserName(token) + " > url:" + fz.req("url") + ",typeid:" + fz.req("pid") + ",num:" + fz.req("count") + ",param:" + fz.req("note") + ",str:" + str);
                }
                else
                {
                    log.WriteLog("api_fail_order", "info", appkeyUserName(token) + " > url:" + fz.req("url") + ",typeid:" + fz.req("pid") + ",num:" + fz.req("count") + ",param:" + fz.req("note") + ",str:" + str);
                }
                break;
            case "order_query":
                if (ItemIds == "")
                {
                    return getResBody(_config("apiNoOrderid"), -1, null, _code, _message);
                }
                temp = " and o.uid=@apiUid ";

                sqlParam += ",o.startCount as order_start";
                sqlParam += ",o.nowCount as order_current";
                sqlParam += ",o.num as order_count";
                sqlParam += ",o.status as order_status";

                sql = " select o.*" + sqlParam + " from [orders] o with(nolock) where o.transactionID in(" + ItemIds + ") " + temp;


                dt = db.getDataTable(sql, pams.ToArray());

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dic = new Dictionary<string, object>();
                    dic.Add("id", dt.Rows[i]["transactionID"]);
                    dic.Add("pid", dt.Rows[i]["appid"]);
                    dic.Add("url", dt.Rows[i]["source_url"]);
                    //dic.Add("url", dt.Rows[i]["url"]);
                    dic.Add("num", dt.Rows[i]["order_count"] + "");
                    dic.Add("note", dt.Rows[i]["othernum"] + "");
                    dic.Add("orderid", dt.Rows[i]["orderid"] + "");
                    dic.Add("start", dt.Rows[i]["order_start"] + "");
                    dic.Add("now", dt.Rows[i]["order_current"] + "");
                    dic.Add("status", dt.Rows[i]["order_status"] + "");
                    dic.Add("order_time", Convert.ToDateTime(dt.Rows[i]["createTime"]).ToString("yyyy-MM-dd hh:mm:ss"));
                    list.Add(dic);
                }
                dic = new Dictionary<string, object>();
                dic.Add("order", list);
                str = getResBody("success", 1, dic, _code, _message);
                break;
            case "order_qx":
                temp2 = " order_type=0 ";
                if (fz.req("task_type") == "renqi")
                {
                    temp = ",nowCount=(case when order_type>0 and order_type<100 and createTime>=DATEADD(HOUR,-1,GETDATE()) then startCount else (DATEDIFF(SECOND,createTime,DATEADD(MINUTE,-15,GETDATE()))/3600)+1 end),shangjia_refund=1";
                    temp2 = " order_type>0 and order_type<100 and num<=24 ";//仅支持包天以内订单退单（一小时内全退，超出一小时按进度退）
                }

                temp2 += " and ";

                //sql = " update orders set status='退单中' where id=@id and token=@token and (status='未开始' or status='进行中') ";
                sql = " update orders set status='退单中',refundTime=(case when refundTime is null then GETDATE() else refundTime end)" + temp + " output deleted.* where transactionID=@orderid and app_id=10001 and uid=@apiUid and (status='未开始' or (" + temp2 + " code not like '%dc' and (status='进行中' or status='订单异常'))) ";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    temp = dt.Rows[0]["status"] + "";
                    string orderId = dt.Rows[0]["id"] + "";

                    if (fz.req("rqorder") == "1")
                    {
                        string roomid = dt.Rows[0]["data"] + "";
                        roomid = roomid.Replace("【补单】", "").Replace("【新订单重下】", "");
                        string leave_result = SendRequest(uConfig._static_dyApiurl + "v1/notice/add", "{\"name\":\"leaveRoom\",\"param\":{\"room_id\":\"" + roomid + "\"}}", "application/json");
                        log.WriteLog("api_user_leave_room", fz.req("ItemIds"), "[" + temp + "] " + roomid + " => " + leave_result);
                    }

                    try
                    {
                        dt = selectDateTable(uConfig.users, " token='" + token + "' ", 1);
                        if (dt.Rows.Count > 0)
                        {
                            temp2 = dt.Rows[0]["nick"] + "";
                        }
                        log.WriteLog("退单", "uapi|" + temp2, "id:" + ItemIds + "(" + orderId + "),status:" + temp);
                    }
                    catch (Exception)
                    {

                    }

                    dic.Add("code", 1);
                    if (temp == "未开始")
                    {
                        parames = new SqlParameter[]{
                                new SqlParameter("@orderid",orderId)
                            };
                        sql = " [tuidan] @orderid,'客户_Api' ";
                        db.ExecuteNonQuery(sql, parames);

                    }
                    str = getResBody("success", 1, null, _code, _message);
                }
                else
                {
                    str = getResBody(_config("orderNoDiy"), -1, null, _code, _message);
                }
                break;
            //case "order_sx":
            //    sql = " update t set t.actTime=GETDATE() output lv.id from orders t left join liveOrders lv on t.id=lv.orderid where t.id=@orderid and uid=@apiUid and GETDATE()>dateadd(minute,3,isnull(t.actTime,'1990-01-01 01:01:01')) ";
            //    dt = db.getDataTable(sql, pams.ToArray());
            //    if (dt.Rows.Count > 0)
            //    {
            //        temp = "cache_liveid_" + dt.Rows[0]["id"] + "";
            //        DateTime expireTime = DateTime.Now.AddMinutes(-999);
            //        cae.SetCache(temp, expireTime, expireTime);

            //        str = getResBody("success", 1, null, _code, _message);
            //    }
            //    else
            //    {
            //        str = getResBody(_config("orderNoDiy"), -1, null, _code, _message);
            //    }
            //    break;
            default:
                str = getResBody(_config("apiNoAction") + ":" + getUserIP(HttpContext.Current), -1, null, _code, _message);
                break;
        }

        return str;
    }

    public string appkeyUserName(string app_key)
    {
        DataTable dt = new DataTable();
        string usernick = app_key;
        try
        {
            dt = selectDateTable(uConfig.users, " token='" + app_key + "' ", 1);
            if (dt.Rows.Count > 0)
            {
                usernick = dt.Rows[0]["nick"] + "";
            }
        }
        catch (Exception)
        {
            usernick = "errorUser";
        }
        return usernick;
    }

    public string formatOrderids(string orderids, int maxLen = 10)
    {
        string[] s = orderids.Split(',');
        int check_count = 0;

        orderids = "";
        for (int i = 0; i < s.Length; i++)
        {
            if (IsNumeric(s[i]) && s[i].Length < maxLen)
            {
                if (orderids != "")
                {
                    orderids += ",";
                }
                orderids += s[i];
                check_count += 1;
            }
            if (check_count >= 1000)
            {
                break;
            }
        }
        return orderids;
    }

    //判断是不是数字
    public static bool isInterger(string str)
    {
        if (str == "")
        {
            return false;
        }
        else
        {
            foreach (char c in str)
            {
                if (char.IsNumber(c))
                {
                    continue;
                }
                else
                {
                    return false;
                }
            }
        }
        return true;

    }

    //只允许数字或字母的判断
    public static bool isIntergerOrLetter(string content)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(content);
    }
}