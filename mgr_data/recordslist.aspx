<%@ Page Title="消费记录" Language="C#" MasterPageFile="~/mgr_data/m.master" AutoEventWireup="true" CodeFile="recordslist.aspx.cs" Inherits="u_records_list" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .income {
            color: #53a000!important;
        }

        .outcome {
            color: #f37800!important;
        }

        .layui-inline {
            display:inline-block;
        }

        .layui-input {
            max-width:120px;
        }
    </style>
    <script src="../layer/layer.js"></script>
    <script src="../layui/layui.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">

    <div>
           
            <div class="row">
                
            <asp:Button ID="Button1" ClientIDMode="Static" runat="server" Text="Button" OnClick="Button1_Click" CssClass="none" />
            <input id="sdate" runat="server" class="sdate none" />
            <input id="edate" runat="server" class="edate none" />
            <input id="appid" runat="server" class="appid none" />
            <script>
                function downLoadResult() {
                    var sdate = $("#starttime").val();
                    var edate = $("#endtime").val();
                    if (sdate == "" && edate == "") {
                        _tip("开始和结束时间必须输入一个", 800);
                        return;
                    }
                    $(".sdate").val(sdate);
                    $(".edate").val(edate);
                    //$(".appid").val(opt);
                    $("#Button1").click();

                }
            </script>    

                <div class="col-sm-12">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">开始时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="starttime" placeholder="留空无效">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">结束时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="endtime" placeholder="留空无效">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button type="button" class="layui-btn layui-btn-normal" onclick="downLoadResult()">导出记录</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        <div>
            <div class="input-group" style="padding: 6px;">
                <span class="input-group-btn">
                    <select class="btn btn-sm appNames" style="border: 1px solid #ddd;" onchange="setPageSize(this)">
                        <option value="">全部</option>
                        <option value="充值">充值记录</option>
                        <option value="退单">退款记录</option>
                    </select>
                </span>
                <input id="search-keyword" type="text" placeholder="请输入关键词" class="input-sm form-control" />
                <span class="input-group-btn">
                    <a class="btn btn-sm btn-primary" onclick="searchList()">搜索</a>
                    <a class="btn btn-sm btn-danger" onclick="javascript:location.href=location.href;"><span class="glyphicon glyphicon-refresh"></span></a>
                </span>
            </div>
        </div>


        <table class="ctTable">
            <thead>
                <tr>
                    <th></th>
                    <th>用户</th>
                    <th>类型</th>
                    <th>详情</th>
                    <th>点数</th>
                    <th>剩余点数</th>
                    <th>时间</th>
                </tr>
            </thead>
            <tbody id="list-display">
            </tbody>
        </table>


        <div style="text-align: center;">
            <div style="margin-top: 10px;">
                <div class="dataTables_info" id="editable_info" role="status" aria-live="polite">当前显示 1 到 <span class="page_size">1</span> 条  共 <span class="page_count">0</span> 条记录</div>
            </div>
            <div>
                <div class="dataTables_paginate paging_simple_numbers" id="editable_paginate">
                    <ul class="pagination">
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        var pager_size = 30;
        var pager_index = 0;
        var pager_key = "";

        var opt = "";

        $("#pagesize").val(pager_key);
        function setPageSize(obj) {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            opt = $(obj).val();
            getPager();
        }

        function searchList() {
            pager_index = 0;
            pager_key = $("#search-keyword").val();
            getPager();
        }

        function getPager() {

            $.ajax({
                type: "POST",
                url: "../mgr_data/apidata.aspx?do=page",
                data: { s: pager_size, p: pager_index, page: 'records-list', key: pager_key, opt: opt, start_time: $("#starttime").val(), end_time: $("#endtime").val() },
                datatype: "json",
                success: function (json) {
                    $("#list-display").html("");
                    if (json.code == 1) {
                        var data = json.data.list;
                        for (var i = 0; i < data.length; i++) {
                            var tr = "";
                            tr += "<tr>";
                            tr += ("<td>" + data[i].id + "</td>");
                            tr += ("<td>" + data[i].nick + "</td>");
                            tr += ("<td><span>" + data[i].name + "</span></td>");
                            tr += ("<td>" + data[i].note + "</td>");
                            tr += "<td class='" + (parseFloat(data[i].cost) >= 0 ? "income" : "outcome") + "'>" + (parseFloat(data[i].cost) >= 0 ? "+" : "") + data[i].cost + "</td>";
                            tr += ("<td>" + data[i].rest + "</td>");
                            tr += ("<td>" + data[i].createTime + "</td>");
                            tr += "</tr>";
                            $("#list-display").append(tr)
                        }
                        $(".page_size").html(pager_size);
                        $(".page_count").html(json.data.count);
                        $(".pagination").html(json.data.html);
                    } else {

                    }
                },
                error: function () {
                    serr();
                }
            });
        }
        getPager();

        function _pt(i) {
            pager_index = i;
            getPager();
        }
    </script>

    
     <script>
         layui.use(['form', 'layedit', 'laydate'], function () {
             var form = layui.form
             , layer = layui.layer
             , layedit = layui.layedit
             , laydate = layui.laydate;

             //开始时间
             laydate.render({
                 elem: '#starttime'
             });

             //结束时间
             laydate.render({
                 elem: '#endtime'
             });

         });
            </script>
</asp:Content>

