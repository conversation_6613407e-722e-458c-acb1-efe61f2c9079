
function markname(mark) {
    var name = "";
    var lab = "";
    switch (mark) {
        default:
            lab = "color:#ddd;";
            name = "未知身份";
            break;

    }
    status = "<span style=\"" + lab + "\">" + name + "</span>";
    return status;
}

function qxname(mark) {
    var name = "";
    var lab = "";
    switch (mark) {
        default:
            lab = "color:#ddd;";
            name = "未知身份";
            break;

    }
    status = "<span style=\"" + lab + "\">" + name + "</span>";
    return status;
}
function statusLable(status) {
    var cls = "fzlab";
    switch (status) {
        case "未开始":
            cls += "";
            break;
        case "进行中":
            cls += " normal";
            break;
        case "已完成":
            cls += " success";
            break;
        case "任务异常":
            cls += " error";
            break;
        case "订单异常":
            cls += " error";
            break;
        case "暂停中":
            cls += " error";
            break;
        case "已退单":
            cls += " error";
            break;
        default:
            break;

    }
    status = "<div class='" + cls + "'>" + status + "</div>";
    return status;
}

function actionbtn(status, id, isrefund) {
    var refund = "";
    if (id != "" || !isNaN(id)) {
        refund = "<a onclick='tuidan(" + id + ",this)' style='font-size: 12px;'>退单</a>"
    }
    if (isrefund == 0) {
        refund = "";
    }
    switch (status) {
        case "未开始":
            break;
        case "进行中":
            break;
        case "任务异常":
            break;
        case "订单异常":
            break;
        case "暂停中":
            break;
        default:
            refund = "";
            break;

    }
    status = refund;
    return status;
}

//Common Com Lib | 通用命令库
window.onkeyup = function (ev) {
    var key = ev.keyCode || ev.which;
    if (key == 27) { //按下Escape
        layer.closeAll(); //疯狂模式，关闭所有层
    }
}
function colPage() {
    layer.closeAll()
}

function closeMy() {
    if (req("inner") != "1") {
        window.history.go(-1);
        console.log("closeMy", "history", location.href);
    } else {
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        console.log("closeMy", "iframe", index);
        parent.layer.close(index); //再执行关闭
    }
}

function event_create(arr) {
    var tiptext = "是否确认此次操作？";
    if (arr['event'] == "delete") {
        tiptext = "是否确认删除此条数据？";
    }
    var aname = arr.aname;
    aname = aname ? aname : "base";
    refreshL = arr.rl;
    refreshL = refreshL ? refreshL : false;

    layer.confirm(tiptext, {
        btn: ['确认', '取消']
    }, function () {
        var mark = mask = layer.load(2, { shade: [0.8, '#393D49'] });;
        $.ajax({
            type: "POST",
            url: "../api/" + aname + ".aspx?do=event_create",
            data: arr,
            dataType: "json",
            success: function (data) {
                layer.close(mask);
                if (data.code == 1) {
                    layer.msg(data.msg, { icon: 1, time: 1200 }, refFrame);
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            },
            error: function (data) {
                layer.close(mask);
                console.log("出现错误：", data);
            }
        });
    }, function () {
    });
}
var refreshL = false;
function edtext(object) {;
    return JSON.stringify(object).replace(/\'/g, "&apos;");
}
function edit(arr) {
    var claname = (arr.small ? "lay-small" : "");
    var h = "<form id='editform' class='" + claname + "' ><div class='layui-form' id='edit-content' style=\"padding-top:18px;\"><input class='none' name='id' value='" + arr.id + "'>";
    var title = arr.title;
    var area = arr.area;
    var action = arr.action;
    var aname = arr.aname;
    aname = aname ? aname : "v1/data";
    refreshL = arr.rl;
    refreshL = refreshL ? refreshL : false;

    var height = 113;
    var baseH = arr.small ? 30 : 43;
    arr = arr.data;
    for (var i = 0; i < arr.length; i++) {
        height += baseH;
        if ('textarea' == arr[i].type) {
            var textareaHeight = 80;
            height += textareaHeight + 20 - baseH;
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><textarea name=\"" + arr[i].id + "\" placeholder=\"请输入" + arr[i].name + "\" class=\"layui-input\" style=\"width: 98%;max-width:98%;min-width:98%;height:" + textareaHeight + "px;\">" + arr[i].value + "</textarea></div></div>";
        }else if ('select' == arr[i].type) {
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><select  class=\"layui-input\" style=\"width: 98%;\" name=\"" + arr[i].id + "\">";
            for (var d = 0; d < arr[i].data.length; d++) {
                var checkStr = "";
                if (d == 0 || arr[i].data[d][0] == arr[i].value) {
                    checkStr = "selected";
                }
                h += "<option value=\"" + arr[i].data[d][0] + "\" " + checkStr + ">" + arr[i].data[d][1] + "</option>";
            }
            h += "</select></div></div>";
        } else if ('option' == arr[i].type) {
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\">";
            for (var d = 0; d < arr[i].data.length; d++) {
                var checkStr = "";
                if (d == 0 || arr[i].data[d][0] == arr[i].value) {
                    checkStr = "checked";
                }
                h += "<input type=\"radio\" name=\"" + arr[i].id + "\" value=\"" + arr[i].data[d][0] + "\" title=\"" + arr[i].data[d][1] + "\" " + checkStr + ">";
            }
            h += "</div></div>";
        } else if ('check' == arr[i].type) {
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\">";
            for (var d = 0; d < arr[i].data.length; d++) {
                var checkStr = arr[i].data[d][2] && arr[i].data[d][2]["c"] ? "checked" : "";
                var disabled = arr[i].data[d][2] && arr[i].data[d][2]["d"] ? "disabled" : "";
                h += "<input type=\"checkbox\" name=\"" + arr[i].id + ":[" + arr[i].data[d][0] + "];\" lay-skin=\"primary\" title=\"" + arr[i].data[d][1] + "\" " + checkStr + " " + disabled + ">";
            }
            height += parseInt((arr[i].data.length - 1) / 4) * 28;
            h += "</div></div>";
        } else if ('hidden' == arr[i].type) {
            height -= baseH;
            h += "<input class='none'  name=\"" + arr[i].id + "\" value='" + arr[i].value + "'>";
        } else if ('disabled' == arr[i].type) {
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><input type=\"text\" name=\"" + arr[i].id + "\" placeholder=\"请输入" + arr[i].name + "\" class=\"layui-input\" style=\"width: 98%;background: #f6f6f6;\" value=\"" + arr[i].value + "\" disabled></div></div>";
        } else {
            h += "<div class=\"layui-form-item\"><label class=\"layui-form-label\">" + arr[i].name + "</label><div class=\"layui-input-block\"><input type=\"text\" name=\"" + arr[i].id + "\" placeholder=\"请输入" + arr[i].name + "\" class=\"layui-input\" style=\"width: 98%;\" value=\"" + arr[i].value + "\"></div></div>";
        }
    }
    height += 20;
    if (area) {
        area[1] = height + "px";
    } else {
        area = ['500px', height + 'px'];
    }
    //h += "<div style=\"height:25px;\"></div>";
    
    h += "<div class=\"layui-form-item\"  style=\"bottom: 0px;background: #f9f9f9;border-top: 1px solid #e5e5e5;;width: 100%;left: 0;padding: 10px;text-align: right;margin-bottom: 0;\"><div class=\"layui-input-block\"><button class=\"layui-btn layui-btn-normal\" lay-submit=\"\" type=\"button\" lay-filter=\"edit-content\">确认</button><button class=\"layui-btn layui-btn-default\" type=\"button\" onclick=\"javascript:parent.layer.closeAll();\">取消</button></div></div>";
    h += "</div></form>";

    showMessage(h, title, area);
    initEditForm({ action: action, aname: aname});
}

function initEditForm(data) {
    layui.use(['form', 'layedit'], function () {
        var form = layui.form
        , layer = layui.layer
        , layedit = layui.layedit;
        layui.form.render();
        if (data.aname.indexOf('/')==-1) {
            data.aname = "api/" + data.aname;
        }
        //添加
        form.on('submit(edit-content)', function () {
            var mark = mask = layer.load(2, { shade: [0.8, '#393D49'] });;
            $.ajax({
                type: "POST",
                url: "../" + data.aname + ".aspx?do=edit_create&_app=" + data.action,
                data: $('#editform').serialize(),
                dataType: "json",
                success: function (data) {
                    layer.close(mask);
                    if (data.code == 1) {
                        layer.msg(data.msg, { icon: 1, time: 1200 }, refFrame);
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                },
                error: function (data) {
                    layer.close(mask);
                    console.log("出现错误：", data);
                }
            });

        });
    });

    
}
function refFrame() {
    layer.closeAll();
    if (!refreshL && typeof(getPager) == "function") {
        getPager();
    } else {
        location.href = location.href;
    }
}