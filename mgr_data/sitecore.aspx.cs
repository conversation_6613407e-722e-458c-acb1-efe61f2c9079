using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class admin_sitecore : baseClass
{
    public string myhost = string.Empty;
    public List<listdata> arrlist = new List<listdata>();
    protected void Page_Load(object sender, EventArgs e)
    {
        cae.RemoteCache("stc_");

        myhost = getHost(Request);
        if (myhost.IndexOf("localhost:") != -1)
        {
            myhost = "..";
        }


        listdata data = new listdata();
        addList("平台网站名称", "sitename", true, "input", "", "");
        addList("禁用ID列表", "limit_list", true, "textarea", "", "");
        addList("全站公告", "notify_business", true, "textarea", "kedit", "", "h:800;");
    }


    public void addList(string name, string id, bool nostar = false, string type = "input", string remark = "", string defval = "", string param = "")
    {
        listdata data = new listdata();
        data.name = name; data.id = id; data.type = type; data.nostar = nostar; data.remark = remark; data.defval = defval; data.param = param;
        arrlist.Add(data);
    }



    public class listdata
    {
        public string id
        {
            get;
            set;
        }
        public string name
        {
            get;
            set;
        }
        public string type
        {
            get;
            set;
        }
        public string remark
        {
            get;
            set;
        }
        public bool nostar
        {
            get;
            set;
        }
        public string defval
        {
            get;
            set;
        }
        public string param
        {
            get;
            set;
        }
    }

    public string emptyValue(string text, string deftext)
    {
        if (string.IsNullOrEmpty(text))
        {
            return deftext;
        }
        return text;
    }
}