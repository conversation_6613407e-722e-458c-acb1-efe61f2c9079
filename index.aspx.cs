using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class index : baseClass
{
    public DataTable user_dt = new DataTable();
    public string order_business = string.Empty;
    protected void Page_Load(object sender, EventArgs e)
    {
        fuzhu fz = new fuzhu();
        fz.setResponseLable("msg");
        List<SqlParameter> pams = fz.collectReqParames();
        string result = string.Empty;
        string tmp = string.Empty;
        string tmp2 = string.Empty;
        Dictionary<string, object> dic = new Dictionary<string, object>();

        string sql = string.Empty;
        dbClass db = new dbClass();
        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        DataTable udt = new DataTable();
        DataRow[] dr;

        switch (fz.req("do"))
        {
            case "signout":
                Response.Cookies[uConfig.loginParames_uid].Expires = DateTime.Now.AddDays(-1);
                Response.Cookies[uConfig.loginParames_nick].Expires = DateTime.Now.AddDays(-1);
                Response.Cookies[uConfig.loginParames_pwd].Expires = DateTime.Now.AddDays(-1);
                fz.sendResponse("success", 1);
                break;
            case "orderTask":
                unified unif = new unified();
                if (fz.empty("typeid"))
                {
                    fz.sendResponse("请选择业务！");
                }
                result = unif.OrderCreate(uConfig.p_uid, fz.req("typeid"), string.Empty, fz.req("task_id"), fz.req("task_num"), fz.req("task_param"), "", fz.req("task_param"), "", "前台");
                break;
            case "json_info":
                fz.check_exist(new string[] { "typeid" });
                sql = " [listBusiness] @userid,@typeid ";
                ds = db.getDataSet(sql, pams.ToArray());
                dt = ds.Tables[0];
                tmp = dt.Rows[0]["tp"] + "";
                dt = ds.Tables[1];

                if (dt.Rows.Count > 0)
                {
                    if (dt.Rows[0]["active_id"] + "" != "")
                    {
                        tmp2 = dt.Rows[0]["active_total_fee"] + "";
                    }
                    if (tmp2 == "")
                    {
                        tmp2 = dt.Rows[0]["app_total_fee"] + "";
                    }
                    dic.Add("dianshu", market_price(fz.req("typeid"), uConfig.p_usp, uConfig.p_uid, tmp2));
                    dic.Add("tixin", dt.Rows[0]["notice"] + "");
                }

                dr = uConfig.appList.Select("appcode='" + fz.req("itemid") + "'");
                if (dr.Length > 0)
                {
                    tmp = dr[0]["typename"] + "" + dr[0]["appname"] + "";
                    dic.Add("itemname", tmp);
                    dic.Add("iteminfo", dr[0]["notice"] + "");
                }
                fz.sendResponse("success", 1, dic);
                break;
            default:
                break;
        }
        if (!string.IsNullOrEmpty(result))
        {
            Response.ContentType = "application/json";
            Response.Write(result);
            Response.End();
        }





        if (uConfig.p_utp != "admin")
        {
            tmp = " and istest<>1 ";
        }

        sql = " select ap.id,ap.appParam as param,ap.typename,appcode,appname,type2 from apps ap with(nolock) left join (select * from opens with(nolock) where uid=@userid) op on ap.id=op.appid where ap.status=1 " + tmp + " and (ap.istest<>2 or op.id is not null) order by (case when ap.typename='dy' then '0' else ap.typename end),sort ";
        sql += " select * from [users] with(nolock) where id=@userid ";
        ds = db.getDataSet(sql, pams.ToArray());
        dt = ds.Tables[0];


        user_dt = ds.Tables[1];

        items.DataSource = dt;
        items.DataBind();

        order_business = ToJson(dt);
    }
}