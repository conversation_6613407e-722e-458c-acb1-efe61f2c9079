<%@ Page Title="Api文档" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="apis.aspx.cs" Inherits="wiky_index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script>
        $(function () {
            $('#top_name').html('文档');
        })
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">


    <div class="panel-sub" id="task">
        <div class="ccvoYb" style="margin-bottom: 0px;">
            <div class="document_content" style="padding: 20px;">

                <div class="document_detail" deep="3">
                    <div class="title">注意事项</div>
                    <h4 style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">对接说明</span></h4>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">商户如果没有遵循接口文档要求开发，出现问题商户自行负责。</span></p>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"><span style="color: rgb(255, 0, 0);">平台提供一对一技术支持服务，商户开发过程中请务必联系贵司商务人员，由商务人员提供技术支持协助联调，以保证项目上线后的稳定性。</span></span></p>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="color: rgb(255, 0, 0); font-family: arial, helvetica, sans-serif; font-size: 14px;"></span></p>
                    <p style="text-indent: 0em; white-space: normal; line-height: 22px; background: rgb(255, 255, 255);"><span style="color: rgb(255, 0, 0); font-family: arial, helvetica, sans-serif; font-size: 14px;">请勿频繁调取平台接口，否则将关闭您的对接权限。</span></p>
                    <h4 style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">API对接信息</span></h4>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">请求Url里面的apiUid与apiToken，请勿泄露。</span></p>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">请求url：<a target="_blank" href="<%=getHost(Request) %>/api/user/order/<%=uConfig.gd(udt,"id") %>/<%=uConfig.gd(udt,"token") %>"><%=getHost(Request) %>/api/user/order/<%=uConfig.gd(udt,"id") %>/<%=uConfig.gd(udt,"token") %></a></span></p>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">POST 内容体进行请求</span></p>
                </div>


                <div class="document_detail" deep="3">
                    <div class="title">创建订单API</div>
                    <p>
                        <br data-filtered="filtered">
                    </p>
                    <table width="811">
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["createApi"] %>
                        </tbody>
                    </table>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"></span></p>
                    <p style="white-space: normal;">
                        <br data-filtered="filtered">
                    </p>
                    <h4 style="line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">返回结果</span></h4>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">数据按JSON的格式实时返回</span></p>
                    <table width="811">
                        <colgroup data-filtered="filtered">
                            <col style="width: 109.50pf" width="146" data-filtered="filtered">
                            <col style="width: 99.75pf" width="133" data-filtered="filtered">
                            <col style="width: 51.75pf" width="69" data-filtered="filtered">
                            <col style="width: 104.25pf" width="139" data-filtered="filtered">
                            <col style="width: 243.00pf" width="324" data-filtered="filtered">
                        </colgroup>
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["createApiResult"] %>
                        </tbody>
                    </table>
                    <!-- 正文 -->

                </div>


                <div class="document_detail" deep="3">
                    <div class="title">查询订单API</div>
                    <p>
                        <br data-filtered="filtered">
                    </p>
                    <table width="811">
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["queryApi"] %>
                        </tbody>
                    </table>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"></span></p>
                    <p style="white-space: normal;">
                        <br data-filtered="filtered">
                    </p>
                    <h4 style="line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">返回结果</span></h4>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">数据按JSON的格式实时返回</span></p>
                    <table width="811">
                        <colgroup data-filtered="filtered">
                            <col style="width: 109.50pf" width="146" data-filtered="filtered">
                            <col style="width: 99.75pf" width="133" data-filtered="filtered">
                            <col style="width: 51.75pf" width="69" data-filtered="filtered">
                            <col style="width: 104.25pf" width="139" data-filtered="filtered">
                            <col style="width: 243.00pf" width="324" data-filtered="filtered">
                        </colgroup>
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["queryApiResult"] %>
                        </tbody>
                    </table>
                    <!-- 正文 -->

                </div>


                <div class="document_detail" deep="3">
                    <div class="title">取消订单API</div>
                    <p>
                        <br data-filtered="filtered">
                    </p>
                    <table width="811">
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["cancelApi"] %>
                        </tbody>
                    </table>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"></span></p>
                    <p style="white-space: normal;">
                        <br data-filtered="filtered">
                    </p>
                    <h4 style="line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">返回结果</span></h4>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">数据按JSON的格式实时返回</span></p>
                    <table width="811">
                        <colgroup data-filtered="filtered">
                            <col style="width: 109.50pf" width="146" data-filtered="filtered">
                            <col style="width: 99.75pf" width="133" data-filtered="filtered">
                            <col style="width: 51.75pf" width="69" data-filtered="filtered">
                            <col style="width: 104.25pf" width="139" data-filtered="filtered">
                            <col style="width: 243.00pf" width="324" data-filtered="filtered">
                        </colgroup>
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["cancelApiResult"] %>
                        </tbody>
                    </table>
                    <!-- 正文 -->

                </div>


                <%--<div class="document_detail" deep="3">
                    <div class="title">刷新订单API</div>
                    <p>
                        <br data-filtered="filtered">
                    </p>
                    <table width="811">
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["refreshApi"] %>
                        </tbody>
                    </table>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"></span></p>
                    <p style="white-space: normal;">
                        <br data-filtered="filtered">
                    </p>
                    <h4 style="line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">返回结果</span></h4>
                    <p style="white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">数据按JSON的格式实时返回</span></p>
                    <table width="811">
                        <colgroup data-filtered="filtered">
                            <col style="width: 109.50pf" width="146" data-filtered="filtered">
                            <col style="width: 99.75pf" width="133" data-filtered="filtered">
                            <col style="width: 51.75pf" width="69" data-filtered="filtered">
                            <col style="width: 104.25pf" width="139" data-filtered="filtered">
                            <col style="width: 243.00pf" width="324" data-filtered="filtered">
                        </colgroup>
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">字段名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">变量名</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">必填</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="139">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-right: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="324">说明</td>
                            </tr>
                            <%=apidic["refreshApiResult"] %>
                        </tbody>
                    </table>
                    <!-- 正文 -->

                </div>--%>


                <div class="document_detail" deep="3">
                    <div class="title">PID表</div>
                    <p>
                        <br data-filtered="filtered">
                    </p>
                    <table width="811">
                        <tbody>
                            <tr class="firstRow">
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="146">类型</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="133">业务</td>
                                <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-top: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(244, 245, 249);" width="69">pid</td>
                            </tr>
                            <asp:Repeater ID="pids" runat="server">
                                <ItemTemplate>
                                    <tr>
                                        <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; vertical-align: middle; white-space: normal; border-left: 3px solid rgb(231, 231, 235); border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);" width="146"><%#Eval("typename") %></td>
                                        <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; text-align: left; vertical-align: middle; white-space: normal; border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);" width="133"><%#Eval("appname") %></td>
                                        <td style="color: rgb(34, 34, 34); font-size: 14px; font-weight: 400; font-style: normal; vertical-align: middle; white-space: normal; border-bottom: 3px solid rgb(231, 231, 235); background: rgb(255, 255, 255);" width="69"><%#Eval("id") %></td>
                                    </tr>
                                </ItemTemplate>
                            </asp:Repeater>
                        </tbody>
                    </table>

                    <!-- 正文 -->

                </div>

                <div class="document_detail" deep="3">
                    <div class="title">关于其他</div>
                    <h4 style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">账户点数</span></h4>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">您当前账户点数为<%=uConfig.gd(udt,"score") %>，本次查询为实时查询结果。</span></p>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="color: rgb(255, 0, 0); font-family: arial, helvetica, sans-serif; font-size: 14px;"></span></p>
                    <h4 style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">在线服务</span></h4>
                    
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"><span style="color: rgb(255, 0, 0);">平台提供线上提交服务，请商户尽快使用API进行对接，在线服务仅提供测试查看数据使用。</span></span></p>
                    <p style="text-indent: 0em; white-space: normal; line-height: 2em;"><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">
                        

                        </p>
                </div>

            </div>

            <div style="background: #eee; text-align: center; color: #666; font-size: 13px; padding: 2px 0;">
                <div>
                    如有疑问，请联系客服
                </div>
            </div>
        </div>
    </div>
</asp:Content>

