using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

/// <summary>
/// CacheHelper 的摘要说明
/// </summary>
public class cae
{
    /// <summary> 
    /// 获取当前应用程序指定CacheKey的Cache对象值  
    /// </summary> 
    /// <param name="CacheKey">索引键值</param> 
    /// <returns>返回缓存对象</returns>  
    public static object GetCache(string CacheKey)
    {
        System.Web.Caching.Cache objCache = HttpRuntime.Cache;
        return objCache[CacheKey];
    }
    /// <summary> 
    /// 设置当前应用程序指定CacheKey的Cache对象值 
    /// </summary> 
    /// <param name="CacheKey">索引键值</param> 
    /// <param name="objObject">缓存对象</param> 
    public static void SetCache(string CacheKey, object objObject)
    {
        System.Web.Caching.Cache objCache = HttpRuntime.Cache;
        objCache.Insert(CacheKey, objObject);
    }
    /// <summary> 
    /// 移除当前应用程序指定CacheKey的Cache对象值 
    /// </summary> 
    /// <param name="CacheKey">移除对象</param> 
    public static void RemoteCache(string CacheKey)
    {
        System.Web.Caching.Cache objCache = HttpRuntime.Cache;
        objCache.Remove(CacheKey);
    }


    /// <summary> 
    /// 设置当前应用程序指定CacheKey的Cache对象值 
    /// </summary> 
    /// <param name="CacheKey">索引键值</param> 
    /// <param name="objObject">缓存对象</param> 
    /// <param name="absoluteExpiration">绝对过期时间</param> 
    /// <param name="slidingExpiration">最后一次访问所插入对象时与该对象过期时之间的时间间隔</param>
    public static void SetCache(string CacheKey, object objObject, DateTime
   absoluteExpiration)
    {
        System.Web.Caching.Cache objCache = HttpRuntime.Cache;
        objCache.Insert(CacheKey, objObject, null, absoluteExpiration,
       System.Web.Caching.Cache.NoSlidingExpiration);
    }

    /// <summary> 
    /// 设置当前应用程序指定CacheKey的Cache对象值 
    /// </summary> 
    /// <param name="CacheKey">索引键值</param> 
    /// <param name="objObject">缓存对象</param> 
    /// <param name="absoluteExpiration">绝对过期时间</param> 
    /// <param name="slidingExpiration">最后一次访问所插入对象时与该对象过期时之间的时间间隔</param>
    public static void SetCacheByTime(string CacheKey, object objObject,TimeSpan slidingExpiration)
    {
        System.Web.Caching.Cache objCache = HttpRuntime.Cache;
        objCache.Insert(CacheKey, objObject, null, System.Web.Caching.Cache.NoAbsoluteExpiration,
       slidingExpiration);
    }

}